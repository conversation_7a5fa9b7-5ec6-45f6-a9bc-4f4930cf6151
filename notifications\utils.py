"""
خدمات الإشعارات
"""

def get_dashboard_url(user_type, page):
    """توليد مسار صحيح للوحة التحكم"""
    base_urls = {
        'admin': '/dashboard/admin/',
        'teacher': '/dashboard/teacher/',
        'student': '/dashboard/student/'
    }

    page_mappings = {
        # صفحات المدير
        'users': 'users/',
        'payment-management': 'payment-management/',
        'ratings': 'ratings/',
        'support': 'support/',
        'teacher-payouts': 'teacher-payouts/',

        # صفحات المعلم
        'students': 'students/',
        'earnings': 'earnings/',
        'schedule': 'schedule/',
        'teacher_ratings': 'ratings/',
        'payouts': 'payouts/',

        # صفحات الطالب
        'lessons': 'lessons/',
        'courses': 'courses/',
        'payments': 'payments/',
        'progress': 'progress/',
        'subscriptions': 'subscriptions/',
        'archive': 'archive/',

        # صفحات مشتركة
        'dashboard': '',
    }

    base_url = base_urls.get(user_type, '/dashboard/')
    page_path = page_mappings.get(page, page + '/')

    return base_url + page_path


def create_notification(recipient, notification_type, title, message, sender=None, priority='medium', action_url=None, action_text=None):
    """إنشاء إشعار جديد"""
    from .models import Notification

    notification = Notification.objects.create(
        recipient=recipient,
        sender=sender,
        notification_type=notification_type,
        title=title,
        message=message,
        priority=priority,
        action_url=action_url,
        action_text=action_text
    )

    return notification


class NotificationService:
    """خدمة الإشعارات الشاملة"""

    @staticmethod
    def notify_lesson_created(lesson):
        """إشعار بإنشاء حصة جديدة"""
        # إشعار الطالب
        create_notification(
            recipient=lesson.enrollment.student,
            notification_type='lesson_created',
            title='حصة جديدة مجدولة',
            message=f'تم جدولة حصة جديدة في دورة {lesson.enrollment.course.title} يوم {lesson.scheduled_date.strftime("%Y-%m-%d")} الساعة {lesson.scheduled_time.strftime("%H:%M")}',
            sender=lesson.enrollment.teacher,
            priority='medium',
            action_url=get_dashboard_url('student', 'lessons'),
            action_text='عرض الحصص'
        )

        # إشعار المعلم
        create_notification(
            recipient=lesson.enrollment.teacher,
            notification_type='lesson_created',
            title='حصة جديدة مجدولة',
            message=f'تم جدولة حصة جديدة مع الطالب {lesson.enrollment.student.get_full_name()} يوم {lesson.scheduled_date.strftime("%Y-%m-%d")} الساعة {lesson.scheduled_time.strftime("%H:%M")}',
            priority='medium',
            action_url=get_dashboard_url('teacher', 'schedule'),
            action_text='عرض الجدول'
        )

    @staticmethod
    def notify_payment_status_changed(payment, changed_by):
        """إشعار بتغيير حالة الدفع"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار الطالب
        create_notification(
            recipient=payment.enrollment.student,
            notification_type='payment_status_changed',
            title='تحديث حالة الدفع',
            message=f'تم تحديث حالة دفعتك إلى "{payment.get_status_display()}" للدورة {payment.enrollment.course.title}',
            sender=changed_by,
            priority='high' if payment.status == 'completed' else 'medium',
            action_url=get_dashboard_url('student', 'payments'),
            action_text='عرض المدفوعات'
        )

        # إشعار المعلم
        create_notification(
            recipient=payment.enrollment.teacher,
            notification_type='payment_status_changed',
            title='تحديث حالة دفع طالب',
            message=f'تم تحديث حالة دفع الطالب {payment.enrollment.student.get_full_name()} إلى "{payment.get_status_display()}"',
            sender=changed_by,
            priority='medium',
            action_url=get_dashboard_url('teacher', 'students'),
            action_text='عرض الطلاب'
        )

        # إشعار المدير إذا كانت الحالة فاشلة
        if payment.status == 'failed':
            admins = User.objects.filter(user_type='admin', is_active=True)
            for admin in admins:
                create_notification(
                    recipient=admin,
                    notification_type='admin_payment_pending',
                    title='فشل في دفعة',
                    message=f'فشلت دفعة الطالب {payment.enrollment.student.get_full_name()} بمبلغ {payment.amount} ر.س',
                    priority='high',
                    action_url=get_dashboard_url('admin', 'payment-management'),
                    action_text='إدارة المدفوعات'
                )

    @staticmethod
    def notify_new_enrollment(enrollment):
        """إشعار بتسجيل جديد"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار الطالب
        create_notification(
            recipient=enrollment.student,
            notification_type='new_enrollment',
            title='تسجيل جديد',
            message=f'تم تسجيلك في دورة {enrollment.course.title} مع المعلم {enrollment.teacher.get_full_name()}',
            priority='high',
            action_url=get_dashboard_url('student', 'courses'),
            action_text='عرض دوراتي'
        )

        # إشعار المعلم
        create_notification(
            recipient=enrollment.teacher,
            notification_type='new_student_assigned',
            title='طالب جديد',
            message=f'تم تعيين الطالب {enrollment.student.get_full_name()} لك في دورة {enrollment.course.title}',
            priority='high',
            action_url=get_dashboard_url('teacher', 'students'),
            action_text='عرض الطلاب'
        )

        # إشعار المدير
        admins = User.objects.filter(user_type='admin', is_active=True)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='admin_new_user',
                title='تسجيل جديد',
                message=f'تم تسجيل الطالب {enrollment.student.get_full_name()} في دورة {enrollment.course.title}',
                priority='medium',
                action_url=get_dashboard_url('admin', 'users'),
                action_text='إدارة المستخدمين'
            )

    @staticmethod
    def notify_lesson_rating(rating):
        """إشعار بتقييم حصة"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار المعلم
        create_notification(
            recipient=rating.lesson.enrollment.teacher,
            notification_type='rating_received',
            title='تقييم جديد',
            message=f'تلقيت تقييم {rating.overall_satisfaction}/5 من الطالب {rating.lesson.enrollment.student.get_full_name()}',
            sender=rating.lesson.enrollment.student,
            priority='medium',
            action_url=get_dashboard_url('teacher', 'teacher_ratings'),
            action_text='عرض التقييمات'
        )

        # إشعار المدير إذا كان التقييم منخفض
        if rating.overall_satisfaction <= 2:
            admins = User.objects.filter(user_type='admin', is_active=True)
            for admin in admins:
                create_notification(
                    recipient=admin,
                    notification_type='admin_low_rating',
                    title='تقييم منخفض',
                    message=f'تلقى المعلم {rating.lesson.enrollment.teacher.get_full_name()} تقييم منخفض ({rating.overall_satisfaction}/5)',
                    priority='high',
                    action_url=get_dashboard_url('admin', 'ratings'),
                    action_text='عرض التقييمات'
                )

    @staticmethod
    def notify_support_ticket(ticket):
        """إشعار بتذكرة دعم جديدة"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار جميع المديرين
        admins = User.objects.filter(user_type='admin', is_active=True)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='support_ticket',
                title='تذكرة دعم جديدة',
                message=f'تذكرة دعم جديدة #{ticket.ticket_number} من {ticket.user.get_full_name()}: {ticket.subject}',
                sender=ticket.user,
                priority='high' if ticket.priority in ['high', 'urgent'] else 'medium',
                action_url=get_dashboard_url('admin', 'support'),
                action_text='عرض التذاكر'
            )

    @staticmethod
    def notify_teacher_rates_changed(teacher, changed_by):
        """إشعار بتغيير أسعار المعلم"""
        create_notification(
            recipient=teacher,
            notification_type='teacher_rate_changed',
            title='تحديث أسعارك',
            message='تم تحديث أسعار حصصك ونسب العمولة الخاصة بك',
            sender=changed_by,
            priority='medium',
            action_url=get_dashboard_url('teacher', 'earnings'),
            action_text='عرض الأرباح'
        )

    @staticmethod
    def notify_lesson_reminder(lesson, hours_before=2):
        """تذكير بالحصة"""
        # إشعار الطالب
        create_notification(
            recipient=lesson.enrollment.student,
            notification_type='lesson_reminder',
            title='تذكير بالحصة',
            message=f'لديك حصة في دورة {lesson.enrollment.course.title} خلال {hours_before} ساعة',
            priority='high',
            action_url=get_dashboard_url('student', 'lessons'),
            action_text='الانضمام للحصة'
        )

        # إشعار المعلم
        create_notification(
            recipient=lesson.enrollment.teacher,
            notification_type='lesson_reminder',
            title='تذكير بالحصة',
            message=f'لديك حصة مع الطالب {lesson.enrollment.student.get_full_name()} خلال {hours_before} ساعة',
            priority='high',
            action_url=get_dashboard_url('teacher', 'schedule'),
            action_text='بدء الحصة'
        )

    @staticmethod
    def notify_new_support_ticket(ticket):
        """إشعار بتذكرة دعم جديدة"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # إشعار جميع المديرين
        admins = User.objects.filter(user_type='admin', is_active=True)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='new_support_ticket',
                title='تذكرة دعم جديدة',
                message=f'تذكرة دعم جديدة #{ticket.ticket_number} من {ticket.created_by.get_full_name()}: {ticket.title}',
                sender=ticket.created_by,
                priority='high' if ticket.priority in ['high', 'urgent'] else 'medium',
                action_url=f'/support/tickets/{ticket.id}/',
                action_text='عرض التذكرة'
            )

    @staticmethod
    def notify_support_ticket_updated(ticket):
        """إشعار بتحديث تذكرة دعم"""
        # إشعار منشئ التذكرة إذا لم يكن مدير
        if not ticket.created_by.is_admin():
            create_notification(
                recipient=ticket.created_by,
                notification_type='support_ticket_updated',
                title='تحديث تذكرة الدعم',
                message=f'تم تحديث تذكرة الدعم #{ticket.ticket_number} - الحالة: {ticket.get_status_display()}',
                priority='medium',
                action_url=f'/support/tickets/{ticket.id}/',
                action_text='عرض التذكرة'
            )

    @staticmethod
    def notify_support_response_added(response):
        """إشعار بإضافة رد على تذكرة دعم"""
        ticket = response.ticket

        if response.is_admin_response:
            # رد من المدير - إشعار المستخدم
            create_notification(
                recipient=ticket.created_by,
                notification_type='support_response_added',
                title='رد جديد على تذكرة الدعم',
                message=f'تلقيت رد جديد على تذكرة الدعم #{ticket.ticket_number}',
                sender=response.created_by,
                priority='medium',
                action_url=f'/support/tickets/{ticket.id}/',
                action_text='عرض الرد'
            )
        else:
            # رد من المستخدم - إشعار المديرين
            from django.contrib.auth import get_user_model
            User = get_user_model()

            admins = User.objects.filter(user_type='admin', is_active=True)
            for admin in admins:
                create_notification(
                    recipient=admin,
                    notification_type='support_response_added',
                    title='رد جديد على تذكرة دعم',
                    message=f'رد جديد من {response.created_by.get_full_name()} على التذكرة #{ticket.ticket_number}',
                    sender=response.created_by,
                    priority='medium',
                    action_url=f'/support/tickets/{ticket.id}/',
                    action_text='عرض الرد'
                )

    @staticmethod
    def notify_ticket_status_changed(ticket, old_status):
        """إشعار بتغيير حالة التذكرة"""
        create_notification(
            recipient=ticket.created_by,
            notification_type='ticket_status_changed',
            title='تغيير حالة التذكرة',
            message=f'تم تغيير حالة تذكرة الدعم #{ticket.ticket_number} من {old_status} إلى {ticket.get_status_display()}',
            sender=ticket.assigned_to,
            priority='medium',
            action_url=f'/support/tickets/{ticket.id}/',
            action_text='عرض التذكرة'
        )

    # تم حذف notify_support_message_received - استخدم notify_system_message_received بدلاً منه

    @staticmethod
    def notify_new_message(message, recipient):
        """إشعار برسالة جديدة في المحادثة - إشعار واحد فقط لكل رسالة"""
        from .models import Notification

        # فحص وجود إشعار لنفس الرسالة مسبقاً
        existing_notification = Notification.objects.filter(
            recipient=recipient,
            sender=message.sender,
            notification_type='new_message',
            action_url=f'/messages/conversation/{message.conversation.id}/',
            is_read=False
        ).first()

        # إذا وُجد إشعار غير مقروء لنفس المحادثة، لا نرسل إشعار جديد
        if existing_notification:
            return existing_notification

        # إنشاء إشعار جديد فقط إذا لم يوجد إشعار غير مقروء
        notification = create_notification(
            recipient=recipient,
            notification_type='new_message',
            title=f'رسالة جديدة من {message.sender.get_full_name()}',
            message=message.content[:100] + '...' if len(message.content) > 100 else message.content,
            sender=message.sender,
            priority='medium',
            action_url=f'/messages/conversation/{message.conversation.id}/',
            action_text='عرض المحادثة'
        )

        return notification

    @staticmethod
    def notify_system_message_received(system_message, recipient):
        """إشعار باستلام رسالة نظام جديدة"""
        # تحديد الأولوية بناءً على نوع الرسالة وأولويتها
        priority_map = {
            'urgent': 'urgent',
            'high': 'high',
            'medium': 'medium',
            'low': 'low'
        }

        # تحديد الأولوية بناءً على نوع الرسالة
        if system_message.message_type in ['warning', 'urgent']:
            priority = 'urgent'
        elif system_message.message_type in ['maintenance', 'policy']:
            priority = 'high'
        else:
            priority = priority_map.get(system_message.priority, 'medium')

        # تحديد العنوان بناءً على نوع الرسالة
        title_map = {
            'announcement': 'إعلان جديد من النظام',
            'warning': 'تحذير مهم من النظام',
            'reminder': 'تذكير من النظام',
            'update': 'تحديث النظام',
            'maintenance': 'إشعار صيانة',
            'policy': 'سياسة جديدة',
            'feature': 'ميزة جديدة',
            'urgent': 'رسالة عاجلة من النظام',
        }

        title = title_map.get(system_message.message_type, 'رسالة جديدة من النظام')

        create_notification(
            recipient=recipient,
            notification_type='system_announcement',
            title=title,
            message=f'{system_message.title}',
            sender=system_message.sent_by,
            priority=priority,
            action_url=f'/support/system-messages/{system_message.id}/',
            action_text='عرض الرسالة'
        )

    @staticmethod
    def notify_user_approved(user):
        """إشعار بالموافقة على طلب التسجيل"""
        # إشعار المستخدم بالموافقة
        create_notification(
            recipient=user,
            notification_type='account_approved',
            title='تمت الموافقة على حسابك',
            message='تمت الموافقة على حسابك بنجاح. يمكنك الآن استخدام جميع ميزات النظام.',
            priority='high',
            action_url='/dashboard/',
            action_text='الذهاب للوحة التحكم'
        )

        # إشعار المديرين
        from django.contrib.auth import get_user_model
        User = get_user_model()

        admins = User.objects.filter(user_type='admin', is_active=True).exclude(id=user.verified_by_id if user.verified_by_id else 0)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='admin_user_approved',
                title='تمت الموافقة على مستخدم',
                message=f'تمت الموافقة على حساب {user.get_full_name()} ({user.get_user_type_display()}) بواسطة {user.verified_by.get_full_name() if user.verified_by else "النظام"}',
                sender=user.verified_by,
                priority='medium',
                action_url=get_dashboard_url('admin', 'users'),
                action_text='إدارة المستخدمين'
            )

    @staticmethod
    def notify_user_rejected(user):
        """إشعار برفض طلب التسجيل"""
        # إشعار المستخدم بالرفض
        rejection_reason = f" السبب: {user.rejection_reason}" if user.rejection_reason else ""

        create_notification(
            recipient=user,
            notification_type='account_rejected',
            title='تم رفض طلب التسجيل',
            message=f'نأسف، تم رفض طلب التسجيل الخاص بك.{rejection_reason}',
            priority='high',
            action_url='/dashboard/verification-rejected/',
            action_text='عرض التفاصيل'
        )

        # إشعار المديرين
        from django.contrib.auth import get_user_model
        User = get_user_model()

        admins = User.objects.filter(user_type='admin', is_active=True).exclude(id=user.verified_by_id if user.verified_by_id else 0)
        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='admin_user_rejected',
                title='تم رفض طلب تسجيل',
                message=f'تم رفض طلب تسجيل {user.get_full_name()} ({user.get_user_type_display()}) بواسطة {user.verified_by.get_full_name() if user.verified_by else "النظام"}',
                sender=user.verified_by,
                priority='medium',
                action_url=get_dashboard_url('admin', 'users'),
                action_text='إدارة المستخدمين'
            )


def send_bulk_notifications(recipients, notification_type, title, message, sender=None, priority='medium'):
    """إرسال إشعارات جماعية"""
    from .models import Notification

    notifications = []
    for recipient in recipients:
        notifications.append(
            Notification(
                recipient=recipient,
                sender=sender,
                notification_type=notification_type,
                title=title,
                message=message,
                priority=priority
            )
        )

    Notification.objects.bulk_create(notifications)
    return len(notifications)
