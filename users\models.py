from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """نموذج المستخدم المخصص"""

    USER_TYPES = (
        ('admin', _('مدير')),
        ('teacher', _('معلم')),
        ('student', _('طالب')),
    )

    STUDENT_LEVELS = (
        ('beginner', _('مبتدئ')),
        ('intermediate', _('متوسط')),
        ('advanced', _('متقدم')),
    )

    user_type = models.CharField(
        max_length=10,
        choices=USER_TYPES,
        default='student',
        verbose_name=_('نوع المستخدم')
    )

    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم الهاتف')
    )

    date_of_birth = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الميلاد')
    )

    # خاص بالطلاب
    student_level = models.CharField(
        max_length=15,
        choices=STUDENT_LEVELS,
        blank=True,
        null=True,
        verbose_name=_('مستوى الطالب')
    )

    # خاص بالمعلمين
    hourly_rate_30 = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('أجر الحصة 30 دقيقة')
    )

    hourly_rate_45 = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('أجر الحصة 45 دقيقة')
    )

    hourly_rate_60 = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_('أجر الحصة 60 دقيقة')
    )

    # نسب عمولة المعلم حسب مدة الحصة
    commission_rate_30 = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=70.00,
        verbose_name=_('نسبة العمولة - 30 دقيقة %')
    )

    commission_rate_45 = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=70.00,
        verbose_name=_('نسبة العمولة - 45 دقيقة %')
    )

    commission_rate_60 = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=70.00,
        verbose_name=_('نسبة العمولة - 60 دقيقة %')
    )

    is_active_teacher = models.BooleanField(
        default=True,
        verbose_name=_('معلم نشط')
    )

    # حقول التحقق من التسجيل
    VERIFICATION_STATUS_CHOICES = (
        ('pending', _('في انتظار المراجعة')),
        ('approved', _('تم الموافقة')),
        ('rejected', _('تم الرفض')),
        ('under_review', _('قيد المراجعة')),
    )

    verification_status = models.CharField(
        max_length=15,
        choices=VERIFICATION_STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة التحقق')
    )

    verification_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات التحقق')
    )

    verified_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'admin'},
        related_name='verified_users',
        verbose_name=_('تم التحقق بواسطة')
    )

    verified_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ التحقق')
    )

    rejection_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('سبب الرفض')
    )

    # حقل لتتبع ما إذا كان الحساب نشطًا من قبل
    was_active = models.BooleanField(
        default=False,
        verbose_name=_('كان نشطًا سابقًا')
    )

    # حقول نظام الحظر
    is_banned = models.BooleanField(
        default=False,
        verbose_name=_('محظور')
    )

    ban_type = models.CharField(
        max_length=20,
        choices=(
            ('temporary', _('مؤقت')),
            ('permanent', _('دائم')),
        ),
        blank=True,
        null=True,
        verbose_name=_('نوع الحظر')
    )

    ban_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('سبب الحظر')
    )

    banned_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الحظر')
    )

    banned_until = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ انتهاء الحظر')
    )

    banned_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'admin'},
        related_name='banned_users',
        verbose_name=_('تم الحظر بواسطة')
    )

    # معلومات إضافية
    profile_picture = models.ImageField(
        upload_to='profile_pics/',
        blank=True,
        null=True,
        verbose_name=_('صورة الملف الشخصي')
    )

    bio = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('نبذة شخصية')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')

    def __str__(self):
        return f"{self.get_full_name()} ({self.get_user_type_display()})"

    def get_full_name(self):
        """إرجاع الاسم الكامل"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username

    def is_admin(self):
        """التحقق من كون المستخدم مدير"""
        return self.user_type == 'admin'

    def is_teacher(self):
        """التحقق من كون المستخدم معلم"""
        return self.user_type == 'teacher'

    def is_student(self):
        """التحقق من كون المستخدم طالب"""
        return self.user_type == 'student'

    def get_lesson_rate(self, duration_minutes):
        """الحصول على سعر الحصة حسب المدة"""
        if duration_minutes == 30:
            return self.hourly_rate_30 or 50.00
        elif duration_minutes == 45:
            return self.hourly_rate_45 or 70.00
        elif duration_minutes == 60:
            return self.hourly_rate_60 or 90.00
        else:
            return 70.00  # سعر افتراضي

    def get_commission_rate(self, duration_minutes):
        """الحصول على نسبة العمولة حسب مدة الحصة"""
        if duration_minutes == 30:
            return self.commission_rate_30
        elif duration_minutes == 45:
            return self.commission_rate_45
        elif duration_minutes == 60:
            return self.commission_rate_60
        else:
            return 70.00  # نسبة افتراضية

    def calculate_teacher_earning(self, lesson_price, duration_minutes):
        """حساب ربح المعلم من الحصة"""
        from decimal import Decimal
        commission_rate = self.get_commission_rate(duration_minutes)
        # تحويل commission_rate إلى Decimal لضمان التوافق
        commission_rate = Decimal(str(commission_rate))
        return (Decimal(str(lesson_price)) * commission_rate) / Decimal('100')

    def is_online(self):
        """التحقق من كون المستخدم متصل"""
        try:
            return self.user_status.is_online
        except:
            return False

    def get_last_seen(self):
        """الحصول على آخر ظهور للمستخدم"""
        try:
            return self.user_status.last_seen
        except:
            return None

    def is_verified(self):
        """التحقق من كون المستخدم تم التحقق منه"""
        return self.verification_status == 'approved'

    def is_pending_verification(self):
        """التحقق من كون المستخدم في انتظار التحقق"""
        return self.verification_status == 'pending'

    def is_rejected(self):
        """التحقق من كون المستخدم مرفوض"""
        return self.verification_status == 'rejected'

    def can_access_dashboard(self):
        """التحقق من إمكانية الوصول للوحة التحكم"""
        # المديرين يمكنهم الوصول دائماً
        if self.user_type == 'admin':
            return True

        # التحقق من الحظر أولاً - المستخدمين المحظورين لا يمكنهم الوصول للوحة التحكم
        if self.is_currently_banned():
            return False

        # المستخدمين الآخرين يحتاجون للتحقق والتفعيل
        return self.is_verified() and self.is_active

    def approve_verification(self, admin_user, notes=None):
        """الموافقة على التحقق"""
        from django.utils import timezone

        self.verification_status = 'approved'
        self.verified_by = admin_user
        self.verified_at = timezone.now()
        self.verification_notes = notes or ''
        self.is_active = True  # تفعيل الحساب
        self.was_active = True  # تعيين الحقل الجديد عند تفعيل الحساب
        self.save()

        # إرسال إشعار للمستخدم
        self._send_verification_notification('approved')

        # إرسال رسالة ترحيبية عند الموافقة على الحساب
        from users.views import _send_welcome_message
        _send_welcome_message(self, is_approved=True)

    def reject_verification(self, admin_user, reason, notes=None):
        """رفض التحقق"""
        from django.utils import timezone

        self.verification_status = 'rejected'
        self.verified_by = admin_user
        self.verified_at = timezone.now()
        self.rejection_reason = reason
        self.verification_notes = notes or ''
        self.is_active = False  # إلغاء تفعيل الحساب
        self.save()

        # إرسال إشعار للمستخدم
        self._send_verification_notification('rejected')

    def _send_verification_notification(self, status):
        """إرسال إشعار حالة التحقق"""
        try:
            from notifications.utils import NotificationService
            if status == 'approved':
                NotificationService.notify_user_approved(self)
            elif status == 'rejected':
                NotificationService.notify_user_rejected(self)
        except ImportError:
            pass  # في حالة عدم وجود نظام الإشعارات

    def get_verification_status_display_ar(self):
        """عرض حالة التحقق بالعربية"""
        status_map = {
            'pending': 'في انتظار المراجعة',
            'approved': 'تم الموافقة',
            'rejected': 'تم الرفض',
            'under_review': 'قيد المراجعة',
        }
        return status_map.get(self.verification_status, self.verification_status)

    def is_currently_banned(self):
        """التحقق من كون المستخدم محظور حالياً"""
        if not self.is_banned:
            return False

        # إذا كان الحظر دائم
        if self.ban_type == 'permanent':
            return True

        # إذا كان الحظر مؤقت، تحقق من انتهاء المدة
        if self.ban_type == 'temporary' and self.banned_until:
            from django.utils import timezone
            return timezone.now() < self.banned_until

        return False

    def get_ban_status_display(self):
        """عرض حالة الحظر"""
        if not self.is_banned:
            return "غير محظور"

        if self.ban_type == 'permanent':
            return "محظور نهائياً"

        if self.ban_type == 'temporary' and self.banned_until:
            from django.utils import timezone
            if timezone.now() >= self.banned_until:
                return "انتهى الحظر"
            else:
                return f"محظور حتى {self.banned_until.strftime('%Y-%m-%d %H:%M')}"

        return "محظور"

    def ban_user(self, admin_user, ban_type, reason, banned_until=None):
        """حظر المستخدم"""
        from django.utils import timezone

        # حفظ حالة is_active الحالية في was_active
        if self.is_active and not self.was_active:
            self.was_active = True

        self.is_banned = True
        self.ban_type = ban_type
        self.ban_reason = reason
        self.banned_at = timezone.now()
        self.banned_by = admin_user
        self.banned_until = banned_until if ban_type == 'temporary' else None
        # لا نقوم بتغيير حالة is_active للسماح للمستخدم بالبقاء مسجل الدخول
        # self.is_active = False  # تم تعطيل هذا السطر
        self.save()

    def unban_user(self):
        """إلغاء حظر المستخدم"""
        self.is_banned = False
        self.ban_type = None
        self.ban_reason = None
        self.banned_at = None
        self.banned_until = None
        self.banned_by = None

        # إذا كان المستخدم نشطًا سابقًا، نقوم بتفعيل الحساب تلقائيًا
        if self.was_active:
            self.is_active = True

        self.save()

    def can_register_again(self):
        """التحقق من إمكانية التسجيل مرة أخرى"""
        # إذا كان محظور حالياً، لا يمكن التسجيل
        if self.is_currently_banned():
            return False

        # إذا كان مرفوض، يمكن التسجيل مرة أخرى
        if self.verification_status == 'rejected':
            return True

        return False

    def update_last_seen(self):
        """تحديث آخر ظهور للمستخدم"""
        from django.utils import timezone
        status, created = UserStatus.objects.get_or_create(
            user=self,
            defaults={'last_seen': timezone.now(), 'is_online': True}
        )
        if not created:
            status.last_seen = timezone.now()
            status.is_online = True
            status.save()
        return status


class UserProfile(models.Model):
    """ملف تعريف إضافي للمستخدم"""

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name=_('المستخدم')
    )

    emergency_contact = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('جهة الاتصال في حالات الطوارئ')
    )

    emergency_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('هاتف الطوارئ')
    )

    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('العنوان')
    )

    preferred_language = models.CharField(
        max_length=5,
        choices=[('ar', 'العربية'), ('en', 'English')],
        default='ar',
        verbose_name=_('اللغة المفضلة')
    )

    timezone = models.CharField(
        max_length=50,
        default='Asia/Riyadh',
        verbose_name=_('المنطقة الزمنية')
    )

    class Meta:
        verbose_name = _('ملف تعريف المستخدم')
        verbose_name_plural = _('ملفات تعريف المستخدمين')

    def __str__(self):
        return f"ملف تعريف {self.user.get_full_name()}"


class AcademySettings(models.Model):
    """إعدادات الأكاديمية"""

    academy_name = models.CharField(
        max_length=100,
        default="أكاديمية القرآنية",
        verbose_name=_('اسم الأكاديمية')
    )

    academy_email = models.EmailField(
        max_length=100,
        default="<EMAIL>",
        verbose_name=_('البريد الإلكتروني للأكاديمية')
    )

    academy_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم هاتف الأكاديمية')
    )

    academy_whatsapp = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم واتساب الأكاديمية')
    )

    academy_support_email = models.EmailField(
        max_length=100,
        blank=True,
        null=True,
        default="<EMAIL>",
        verbose_name=_('البريد الإلكتروني للدعم الفني')
    )

    academy_logo = models.ImageField(
        upload_to='academy/',
        blank=True,
        null=True,
        verbose_name=_('شعار الأكاديمية')
    )

    academy_address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('عنوان الأكاديمية')
    )

    academy_description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف الأكاديمية')
    )

    academy_slogan = models.CharField(
        max_length=200,
        default="نظام قرآنيا التعليمي",
        blank=True,
        null=True,
        verbose_name=_('سلوجان الأكاديمية')
    )

    academy_working_hours = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ساعات العمل')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('إعدادات الأكاديمية')
        verbose_name_plural = _('إعدادات الأكاديمية')

    def __str__(self):
        return self.academy_name

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات الأكاديمية، أو إنشاء إعدادات افتراضية إذا لم تكن موجودة"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class UserStatus(models.Model):
    """حالة المستخدم (متصل/غير متصل)"""

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='user_status',
        verbose_name=_('المستخدم')
    )

    is_online = models.BooleanField(
        default=False,
        verbose_name=_('متصل')
    )

    last_seen = models.DateTimeField(
        auto_now=True,
        verbose_name=_('آخر ظهور')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('حالة المستخدم')
        verbose_name_plural = _('حالات المستخدمين')

    def __str__(self):
        status = "متصل" if self.is_online else "غير متصل"
        return f"{self.user.get_full_name()} - {status}"

    def get_status_display(self):
        """عرض حالة المستخدم"""
        if self.is_online:
            return "متصل"
        else:
            from django.utils import timezone
            from datetime import timedelta

            now = timezone.now()
            time_diff = now - self.last_seen

            if time_diff < timedelta(minutes=5):
                return "متصل مؤخراً"
            elif time_diff < timedelta(hours=1):
                minutes = int(time_diff.total_seconds() / 60)
                return f"آخر ظهور منذ {minutes} دقيقة"
            elif time_diff < timedelta(days=1):
                hours = int(time_diff.total_seconds() / 3600)
                return f"آخر ظهور منذ {hours} ساعة"
            else:
                days = time_diff.days
                return f"آخر ظهور منذ {days} يوم"

    def get_status_color(self):
        """لون حالة المستخدم"""
        if self.is_online:
            return "green"
        else:
            from django.utils import timezone
            from datetime import timedelta

            now = timezone.now()
            time_diff = now - self.last_seen

            if time_diff < timedelta(minutes=5):
                return "yellow"
            else:
                return "gray"
