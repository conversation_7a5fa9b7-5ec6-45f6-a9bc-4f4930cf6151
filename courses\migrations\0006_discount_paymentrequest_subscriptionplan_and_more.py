# Generated by Django 4.2.7 on 2025-05-23 11:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0005_teacherearning_lesson_duration_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Discount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود الخصم')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العرض')),
                ('discount_type', models.CharField(choices=[('percentage', 'نسبة مئوية'), ('fixed', 'مبلغ ثابت')], max_length=20, verbose_name='نوع الخصم')),
                ('value', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='قيمة الخصم')),
                ('min_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأدنى للمبلغ')),
                ('max_uses', models.PositiveIntegerField(blank=True, null=True, verbose_name='الحد الأقصى للاستخدام')),
                ('used_count', models.PositiveIntegerField(default=0, verbose_name='عدد مرات الاستخدام')),
                ('valid_from', models.DateTimeField(verbose_name='صالح من')),
                ('valid_until', models.DateTimeField(verbose_name='صالح حتى')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(limit_choices_to={'user_type': 'admin'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'خصم',
                'verbose_name_plural': 'الخصومات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ (دولار)')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم')),
                ('final_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ النهائي')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('paid', 'تم الدفع'), ('confirmed', 'مؤكد'), ('rejected', 'مرفوض'), ('expired', 'منتهي الصلاحية')], default='pending', max_length=20, verbose_name='الحالة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('due_date', models.DateTimeField(verbose_name='تاريخ الاستحقاق')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(limit_choices_to={'user_type': 'admin'}, on_delete=django.db.models.deletion.CASCADE, related_name='created_payment_requests', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('discount', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='courses.discount', verbose_name='الخصم المطبق')),
                ('student', models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='payment_requests', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
            ],
            options={
                'verbose_name': 'طلب دفع',
                'verbose_name_plural': 'طلبات الدفع',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الخطة')),
                ('duration_type', models.CharField(choices=[('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('yearly', 'سنوي')], max_length=20, verbose_name='نوع المدة')),
                ('lessons_count', models.PositiveIntegerField(verbose_name='عدد الحصص')),
                ('lesson_duration', models.IntegerField(choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')], default=45, verbose_name='مدة الحصة (دقيقة)')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر (دولار)')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'خطة اشتراك',
                'verbose_name_plural': 'خطط الاشتراك',
                'ordering': ['duration_type', 'lessons_count'],
            },
        ),
        migrations.CreateModel(
            name='StudentSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('expired', 'منتهي'), ('suspended', 'معلق'), ('cancelled', 'ملغي')], default='active', max_length=20, verbose_name='الحالة')),
                ('lessons_used', models.PositiveIntegerField(default=0, verbose_name='الحصص المستخدمة')),
                ('lessons_remaining', models.PositiveIntegerField(verbose_name='الحصص المتبقية')),
                ('auto_renewal', models.BooleanField(default=False, verbose_name='التجديد التلقائي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('payment_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='subscription', to='courses.paymentrequest', verbose_name='طلب الدفع')),
                ('student', models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
                ('subscription_plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='courses.subscriptionplan', verbose_name='خطة الاشتراك')),
            ],
            options={
                'verbose_name': 'اشتراك طالب',
                'verbose_name_plural': 'اشتراكات الطلاب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='paymentrequest',
            name='subscription_plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='courses.subscriptionplan', verbose_name='خطة الاشتراك'),
        ),
        migrations.CreateModel(
            name='PaymentProof',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('proof_image', models.ImageField(upload_to='payment_proofs/%Y/%m/', verbose_name='صورة إثبات الدفع')),
                ('transaction_id', models.CharField(blank=True, max_length=100, verbose_name='رقم المعاملة')),
                ('payment_method', models.CharField(max_length=50, verbose_name='طريقة الدفع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات الطالب')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('payment_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='proof', to='courses.paymentrequest', verbose_name='طلب الدفع')),
            ],
            options={
                'verbose_name': 'إثبات دفع',
                'verbose_name_plural': 'إثباتات الدفع',
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
