# Generated by Django 4.2.7 on 2025-05-23 17:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_userstatus'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='rejection_reason',
            field=models.TextField(blank=True, null=True, verbose_name='سبب الرفض'),
        ),
        migrations.AddField(
            model_name='user',
            name='verification_notes',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات التحقق'),
        ),
        migrations.AddField(
            model_name='user',
            name='verification_status',
            field=models.CharField(choices=[('pending', 'في انتظار المراجعة'), ('approved', 'تم الموافقة'), ('rejected', 'تم الرفض'), ('under_review', 'قيد المراجعة')], default='pending', max_length=15, verbose_name='حالة التحقق'),
        ),
        migrations.AddField(
            model_name='user',
            name='verified_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق'),
        ),
        migrations.AddField(
            model_name='user',
            name='verified_by',
            field=models.ForeignKey(blank=True, limit_choices_to={'user_type': 'admin'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_users', to=settings.AUTH_USER_MODEL, verbose_name='تم التحقق بواسطة'),
        ),
    ]
