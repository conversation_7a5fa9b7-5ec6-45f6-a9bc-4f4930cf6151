# Generated by Django 4.2.7 on 2025-05-23 00:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('notifications', '0004_remove_ticketreply_ticket_remove_ticketreply_user_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='message',
            name='message_type',
            field=models.CharField(choices=[('private', 'رسالة خاصة'), ('support', 'رسالة دعم'), ('announcement', 'إعلان عام'), ('update', 'تحديث النظام'), ('maintenance', 'إشعار صيانة'), ('policy', 'تحديث السياسات'), ('training', 'دورة تدريبية'), ('event', 'فعالية أو مناسبة'), ('urgent', 'إشعار عاجل'), ('welcome', 'رسالة ترحيب'), ('reminder', 'تذكير'), ('congratulations', 'تهنئة')], default='private', max_length=15, verbose_name='نوع الرسالة'),
        ),
        migrations.CreateModel(
            name='BulkMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('announcement', 'إعلان عام'), ('update', 'تحديث النظام'), ('maintenance', 'إشعار صيانة'), ('policy', 'تحديث السياسات'), ('training', 'دورة تدريبية'), ('event', 'فعالية أو مناسبة'), ('urgent', 'إشعار عاجل'), ('welcome', 'رسالة ترحيب'), ('reminder', 'تذكير'), ('congratulations', 'تهنئة'), ('newsletter', 'نشرة إخبارية'), ('survey', 'استطلاع رأي')], default='announcement', max_length=20, verbose_name='نوع الرسالة')),
                ('recipient_type', models.CharField(choices=[('all', 'جميع المستخدمين'), ('teachers', 'المعلمين فقط'), ('students', 'الطلاب فقط'), ('active_users', 'المستخدمين النشطين'), ('new_users', 'المستخدمين الجدد'), ('custom', 'مستخدمين محددين')], default='all', max_length=20, verbose_name='نوع المستقبلين')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('urgent', 'عاجل')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('send_email', models.BooleanField(default=False, verbose_name='إرسال إيميل أيضاً')),
                ('schedule_send', models.DateTimeField(blank=True, null=True, verbose_name='جدولة الإرسال')),
                ('is_sent', models.BooleanField(default=False, verbose_name='تم الإرسال')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الإرسال')),
                ('recipients_count', models.PositiveIntegerField(default=0, verbose_name='عدد المستقبلين')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('custom_recipients', models.ManyToManyField(blank=True, related_name='received_bulk_messages', to=settings.AUTH_USER_MODEL, verbose_name='مستقبلين محددين')),
                ('sender', models.ForeignKey(limit_choices_to={'user_type': 'admin'}, on_delete=django.db.models.deletion.CASCADE, related_name='sent_bulk_messages', to=settings.AUTH_USER_MODEL, verbose_name='المرسل')),
            ],
            options={
                'verbose_name': 'رسالة جماعية',
                'verbose_name_plural': 'الرسائل الجماعية',
                'ordering': ['-created_at'],
            },
        ),
    ]
