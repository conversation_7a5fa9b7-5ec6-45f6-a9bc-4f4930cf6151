# Generated by Django 4.2.7 on 2025-05-22 12:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Lesson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الحصة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الحصة')),
                ('scheduled_date', models.DateTimeField(verbose_name='موعد الحصة')),
                ('duration_minutes', models.PositiveIntegerField(choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')], default=45, verbose_name='مدة الحصة (بالدقائق)')),
                ('status', models.CharField(choices=[('scheduled', 'مجدولة'), ('in_progress', 'جارية'), ('completed', 'مكتملة'), ('cancelled', 'ملغية'), ('missed', 'فائتة')], default='scheduled', max_length=15, verbose_name='حالة الحصة')),
                ('jitsi_room_id', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='معرف غرفة Jitsi')),
                ('jitsi_password', models.CharField(blank=True, max_length=50, null=True, verbose_name='كلمة مرور Jitsi')),
                ('actual_start_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت البداية الفعلي')),
                ('actual_end_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت النهاية الفعلي')),
                ('teacher_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات المعلم')),
                ('admin_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات الإدارة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'حصة',
                'verbose_name_plural': 'الحصص',
                'ordering': ['scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='LessonAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('joined_at', models.DateTimeField(auto_now_add=True, verbose_name='وقت الدخول')),
                ('left_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الخروج')),
                ('duration_minutes', models.PositiveIntegerField(default=0, verbose_name='مدة الحضور (بالدقائق)')),
            ],
            options={
                'verbose_name': 'حضور الحصة',
                'verbose_name_plural': 'حضور الحصص',
            },
        ),
        migrations.CreateModel(
            name='LessonContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_type', models.CharField(choices=[('memorization', 'حفظ'), ('review', 'مراجعة'), ('recitation', 'تلاوة'), ('correction', 'تصحيح')], max_length=15, verbose_name='نوع المحتوى')),
                ('surah_name', models.CharField(max_length=100, verbose_name='اسم السورة')),
                ('from_verse', models.PositiveIntegerField(verbose_name='من الآية')),
                ('to_verse', models.PositiveIntegerField(verbose_name='إلى الآية')),
                ('quality_score', models.PositiveIntegerField(blank=True, choices=[(1, '1/10'), (2, '2/10'), (3, '3/10'), (4, '4/10'), (5, '5/10'), (6, '6/10'), (7, '7/10'), (8, '8/10'), (9, '9/10'), (10, '10/10')], null=True, verbose_name='تقييم الجودة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('recorded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التسجيل')),
            ],
            options={
                'verbose_name': 'محتوى الحصة',
                'verbose_name_plural': 'محتوى الحصص',
                'ordering': ['-recorded_at'],
            },
        ),
        migrations.CreateModel(
            name='LessonRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('teacher_rating', models.PositiveIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='تقييم المعلم')),
                ('lesson_quality', models.PositiveIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='جودة الحصة')),
                ('technical_quality', models.PositiveIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='الجودة التقنية')),
                ('overall_satisfaction', models.PositiveIntegerField(choices=[(1, '1 نجوم'), (2, '2 نجوم'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='الرضا العام')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='تعليق')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقييم')),
                ('lesson', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='rating', to='lessons.lesson', verbose_name='الحصة')),
            ],
            options={
                'verbose_name': 'تقييم الحصة',
                'verbose_name_plural': 'تقييمات الحصص',
            },
        ),
    ]
