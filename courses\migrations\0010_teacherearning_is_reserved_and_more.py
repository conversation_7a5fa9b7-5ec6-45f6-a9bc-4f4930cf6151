# Generated by Django 4.2.7 on 2025-05-23 13:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0007_teacherpayoutrequest_teacherpayout_adminpayoutproof'),
    ]

    operations = [
        migrations.AddField(
            model_name='teacherearning',
            name='is_reserved',
            field=models.BooleanField(default=False, verbose_name='محجوز لطلب سحب'),
        ),
        migrations.AddField(
            model_name='teacherearning',
            name='is_withdrawn',
            field=models.BooleanField(default=False, verbose_name='تم سحبه'),
        ),
        migrations.AddField(
            model_name='teacherearning',
            name='reserved_for_request',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reserved_earnings', to='courses.teacherpayoutrequest', verbose_name='محجوز لطلب'),
        ),
        migrations.AddField(
            model_name='teacherearning',
            name='withdrawn_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ السحب'),
        ),
    ]
