/* Enhanced Islamic Theme Colors & Variables */
:root {
    --islamic-primary: #2D5016;
    --islamic-light: #4A7C59;
    --islamic-dark: #1A3009;
    --islamic-gold: #D4AF37;
    --islamic-light-gold: #F4E4BC;
    --islamic-light-blue: #E8F4FD;
    --islamic-cream: #FDF6E3;
    --islamic-emerald: #50C878;
    --islamic-sage: #9CAF88;
    --islamic-mint: #F0F8F0;
    --shadow-light: rgba(45, 80, 22, 0.1);
    --shadow-medium: rgba(45, 80, 22, 0.15);
    --shadow-strong: rgba(45, 80, 22, 0.25);
    --gradient-primary: linear-gradient(135deg, var(--islamic-primary) 0%, var(--islamic-light) 100%);
    --gradient-gold: linear-gradient(135deg, var(--islamic-gold) 0%, var(--islamic-light-gold) 100%);
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --transition-fast: all 0.2s ease;
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Base Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8fffe 0%, var(--islamic-mint) 100%);
    min-height: 100vh;
}

/* Enhanced Card Styles */
.enhanced-card {
    background: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 4px 20px var(--shadow-light);
    border: 1px solid rgba(45, 80, 22, 0.08);
    transition: var(--transition-smooth);
    overflow: hidden;
}

.enhanced-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px var(--shadow-medium);
}

.enhanced-card-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem;
    border-bottom: none;
}

.enhanced-card-body {
    padding: 1.5rem;
}

/* Enhanced Buttons */
.btn-islamic-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    transition: var(--transition-smooth);
    box-shadow: 0 2px 10px var(--shadow-light);
}

.btn-islamic-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 20px var(--shadow-medium);
    color: white;
}

.btn-islamic-gold {
    background: var(--gradient-gold);
    border: none;
    color: var(--islamic-dark);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    transition: var(--transition-smooth);
}

.btn-islamic-gold:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

/* Enhanced Stats Cards */
.stats-card {
    background: white;
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    box-shadow: 0 2px 15px var(--shadow-light);
    border-left: 4px solid var(--islamic-primary);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: var(--islamic-mint);
    border-radius: 50%;
    transform: translate(30px, -30px);
    opacity: 0.1;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px var(--shadow-medium);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
}

/* Enhanced Navigation */
.enhanced-nav {
    background: white;
    box-shadow: 0 2px 20px var(--shadow-light);
    border-bottom: 1px solid rgba(45, 80, 22, 0.1);
}

.nav-link-enhanced {
    color: var(--islamic-dark);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
    margin: 0 0.25rem;
}

.nav-link-enhanced:hover {
    background: var(--islamic-mint);
    color: var(--islamic-primary);
    transform: translateY(-1px);
}

.nav-link-enhanced.active {
    background: var(--gradient-primary);
    color: white;
}

/* Enhanced Forms */
.form-control-enhanced {
    border: 2px solid rgba(45, 80, 22, 0.1);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
    background: white;
}

.form-control-enhanced:focus {
    border-color: var(--islamic-primary);
    box-shadow: 0 0 0 3px var(--shadow-light);
    outline: none;
}

/* Enhanced Tables */
.table-enhanced {
    background: white;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 15px var(--shadow-light);
}

.table-enhanced thead {
    background: var(--gradient-primary);
    color: white;
}

.table-enhanced tbody tr {
    transition: var(--transition-fast);
}

.table-enhanced tbody tr:hover {
    background: var(--islamic-mint);
    transform: scale(1.01);
}

/* Enhanced Progress Bars */
.progress-enhanced {
    height: 8px;
    border-radius: 10px;
    background: rgba(45, 80, 22, 0.1);
    overflow: hidden;
}

.progress-bar-enhanced {
    background: var(--gradient-primary);
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Enhanced Badges */
.badge-enhanced {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success-enhanced {
    background: linear-gradient(135deg, var(--islamic-emerald) 0%, var(--islamic-sage) 100%);
    color: white;
}

.badge-warning-enhanced {
    background: var(--gradient-gold);
    color: var(--islamic-dark);
}

.badge-info-enhanced {
    background: linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%);
    color: white;
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* Enhanced Responsive Sidebar */
.sidebar-container {
    position: relative;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-sidebar {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background: var(--islamic-primary);
    z-index: 50;
    transition: var(--transition-smooth);
    box-shadow: -4px 0 20px var(--shadow-medium);
    overflow-y: auto;
    display: none;
}

.mobile-sidebar.active {
    right: 0;
    display: block;
}

@media (max-width: 1024px) {
    .mobile-sidebar {
        display: block;
    }
}

.sidebar-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 60;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    box-shadow: 0 4px 15px var(--shadow-medium);
    transition: var(--transition-smooth);
    font-size: 1.25rem;
    cursor: pointer;
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px var(--shadow-strong);
}

.sidebar-toggle:active {
    transform: scale(0.95);
}

.sidebar-close {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    color: white;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .sidebar-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .desktop-sidebar {
        display: none !important;
    }

    .sidebar-enhanced {
        display: none !important;
    }

    .main-content {
        margin-right: 0 !important;
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .enhanced-card {
        margin: 0.5rem;
        border-radius: var(--border-radius-sm);
    }

    .stats-card {
        padding: 1rem;
    }

    .enhanced-card-header {
        padding: 1rem;
    }

    .enhanced-card-body {
        padding: 1rem;
    }

    .grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .text-3xl {
        font-size: 1.875rem !important;
    }

    .mobile-sidebar {
        width: 280px;
    }

    .sidebar-toggle {
        top: 0.75rem;
        right: 0.75rem;
        width: 48px;
        height: 48px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .enhanced-card-header h1 {
        font-size: 1.5rem !important;
    }

    .stats-card {
        padding: 0.75rem;
    }

    .stats-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .text-3xl {
        font-size: 1.5rem !important;
    }
}

/* Enhanced Sidebar */
.sidebar-enhanced {
    background: var(--gradient-primary);
    box-shadow: 2px 0 20px var(--shadow-light);
    border-left: 1px solid var(--islamic-light);
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

.sidebar-link-enhanced {
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    margin: 0.25rem 0.5rem;
    transition: var(--transition-smooth);
    position: relative;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.sidebar-link-enhanced:hover {
    background: var(--islamic-light);
    color: white;
    transform: translateX(2px);
    text-decoration: none;
}

.sidebar-link-enhanced.active {
    background: var(--islamic-light);
    color: white;
    border-right: 4px solid var(--islamic-gold);
}

.sidebar-link-enhanced.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--islamic-gold);
}

/* Sidebar Navigation Scrolling */
.sidebar-enhanced nav {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: var(--islamic-gold) transparent;
}

.sidebar-enhanced nav::-webkit-scrollbar {
    width: 4px;
}

.sidebar-enhanced nav::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-enhanced nav::-webkit-scrollbar-thumb {
    background: var(--islamic-gold);
    border-radius: 2px;
}

.sidebar-enhanced nav::-webkit-scrollbar-thumb:hover {
    background: var(--islamic-light-gold);
}

/* Enhanced Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Enhanced Tooltips */
.tooltip-enhanced {
    position: relative;
}

.tooltip-enhanced::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--islamic-dark);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
    z-index: 1000;
}

.tooltip-enhanced:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}
