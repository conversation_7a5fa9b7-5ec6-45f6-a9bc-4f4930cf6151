from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.contrib.auth import get_user_model
from notifications.models import Notification
import json

User = get_user_model()


@login_required
@require_http_methods(["GET"])
def notification_count(request):
    """إرجاع عدد الإشعارات غير المقروءة"""
    count = Notification.objects.filter(
        recipient=request.user,
        is_read=False
    ).count()
    
    return JsonResponse({'count': count})


@login_required
@require_http_methods(["POST"])
def mark_notification_read(request, notification_id):
    """تحديد إشعار كمقروء"""
    try:
        notification = Notification.objects.get(
            id=notification_id,
            recipient=request.user
        )
        notification.mark_as_read()
        return JsonResponse({'success': True})
    except Notification.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Notification not found'})


@login_required
@require_http_methods(["GET"])
def user_search(request):
    """البحث عن المستخدمين"""
    query = request.GET.get('q', '')
    user_type = request.GET.get('type', '')
    
    if len(query) < 2:
        return JsonResponse({'users': []})
    
    users = User.objects.filter(
        first_name__icontains=query
    ) | User.objects.filter(
        last_name__icontains=query
    ) | User.objects.filter(
        username__icontains=query
    )
    
    if user_type:
        users = users.filter(user_type=user_type)
    
    users = users[:10]  # حد أقصى 10 نتائج
    
    users_data = []
    for user in users:
        users_data.append({
            'id': user.id,
            'name': user.get_full_name(),
            'username': user.username,
            'user_type': user.get_user_type_display(),
            'email': user.email,
        })
    
    return JsonResponse({'users': users_data})


@login_required
@require_http_methods(["GET"])
def user_profile(request):
    """إرجاع بيانات الملف الشخصي للمستخدم"""
    user = request.user
    
    profile_data = {
        'id': user.id,
        'username': user.username,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'email': user.email,
        'phone': user.phone,
        'user_type': user.user_type,
        'user_type_display': user.get_user_type_display(),
        'date_joined': user.date_joined.isoformat(),
    }
    
    if hasattr(user, 'profile'):
        profile_data.update({
            'preferred_language': user.profile.preferred_language,
            'timezone': user.profile.timezone,
            'address': user.profile.address,
        })
    
    return JsonResponse({'profile': profile_data})
