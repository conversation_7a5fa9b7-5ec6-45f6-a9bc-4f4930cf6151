#!/usr/bin/env python
"""
Script لاختبار تدفق تسجيل الدخول والتحقق
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania_lms.settings')
django.setup()

from users.models import User

def test_login_flow():
    """اختبار تدفق تسجيل الدخول"""
    
    print("🔐 اختبار تدفق تسجيل الدخول والتحقق")
    print("=" * 50)
    
    # جلب المستخدمين التجريبيين
    test_users = [
        ('test_teacher', 'معلم في انتظار المراجعة'),
        ('test_student', 'طالب في انتظار المراجعة'),
        ('test_teacher2', 'معلم مرفوض'),
        ('test_student2', 'طالب مرفوض'),
    ]
    
    for username, description in test_users:
        user = User.objects.filter(username=username).first()
        if user:
            print(f"\n👤 {description}:")
            print(f"   اسم المستخدم: {username}")
            print(f"   كلمة المرور: test123")
            print(f"   الحالة: {user.verification_status}")
            print(f"   نشط: {user.is_active}")
            print(f"   يمكن الوصول للوحة التحكم: {user.can_access_dashboard()}")
            
            # تحديد الصفحة المتوقعة بعد تسجيل الدخول
            if user.is_admin():
                expected_page = "/dashboard/admin/"
            elif user.verification_status == 'pending':
                expected_page = "/dashboard/verification-pending/"
            elif user.verification_status == 'rejected':
                expected_page = "/dashboard/verification-rejected/"
            elif user.verification_status == 'approved':
                if user.user_type == 'teacher':
                    expected_page = "/dashboard/teacher/"
                elif user.user_type == 'student':
                    expected_page = "/dashboard/student/"
                else:
                    expected_page = "/dashboard/"
            else:
                expected_page = "/login/"
            
            print(f"   الصفحة المتوقعة بعد تسجيل الدخول: {expected_page}")
        else:
            print(f"\n❌ {description}: المستخدم غير موجود")
    
    print("\n" + "=" * 50)
    print("🔗 روابط الاختبار المباشرة:")
    print("   تسجيل الدخول: http://localhost:8080/login/")
    print("   انتظار المراجعة: http://localhost:8080/dashboard/verification-pending/")
    print("   رفض الطلب: http://localhost:8080/dashboard/verification-rejected/")
    print("   إدارة التحقق: http://localhost:8080/dashboard/admin/user-verifications/")
    
    print("\n🧪 خطوات الاختبار:")
    print("1. اذهب لصفحة تسجيل الدخول")
    print("2. جرب تسجيل الدخول بالمستخدمين التجريبيين")
    print("3. تحقق من التوجيه للصفحة الصحيحة")
    print("4. اختبر الروابط المباشرة للصفحات")

if __name__ == '__main__':
    test_login_flow()
