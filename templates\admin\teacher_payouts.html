{% extends 'base.html' %}
{% load static %}

{% block title %}مدفوعات المعلمين - قرآنيا{% endblock %}

{% block extra_css %}
<style>
    .tab-button {
        @apply px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 transition-colors duration-200;
    }

    .tab-button.active {
        @apply text-islamic-primary border-islamic-primary;
    }

    .tab-content {
        @apply transition-all duration-300;
    }

    .tab-content.hidden {
        @apply opacity-0 pointer-events-none;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">مدفوعات المعلمين</h1>
                    <p class="mt-2 text-gray-600">إدارة طلبات السحب ومدفوعات المعلمين</p>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="{% url 'admin_dashboard' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-file-invoice-dollar text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">إجمالي الطلبات</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_requests }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">في الانتظار</p>
                        <p class="text-2xl font-bold text-gray-900">{{ pending_requests }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">مدفوعات مكتملة</p>
                        <p class="text-2xl font-bold text-gray-900">{{ completed_requests }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">إجمالي المدفوع</p>
                        <p class="text-2xl font-bold text-gray-900">${{ total_paid }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8 space-x-reverse">
                <button onclick="showTab('payout-requests')" id="tab-payout-requests" class="tab-button active">
                    <i class="fas fa-hand-holding-usd ml-2"></i>
                    طلبات السحب
                </button>
                <button onclick="showTab('approved-requests')" id="tab-approved-requests" class="tab-button">
                    <i class="fas fa-check ml-2"></i>
                    الطلبات المعتمدة
                </button>
                <button onclick="showTab('completed-payouts')" id="tab-completed-payouts" class="tab-button">
                    <i class="fas fa-history ml-2"></i>
                    المدفوعات المكتملة
                </button>
            </nav>
        </div>

        <!-- Payout Requests Tab -->
        <div id="payout-requests-tab" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">طلبات السحب</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعلم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأرباح المتاحة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الشهر/السنة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الطلب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for request in payout_requests %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-user text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ request.teacher.get_full_name }}</div>
                                            <div class="text-sm text-gray-500">{{ request.teacher.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">${{ request.amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">${{ request.available_earnings }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ request.month }}/{{ request.year }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if request.status == 'paid' %}bg-green-100 text-green-800
                                        {% elif request.status == 'approved' %}bg-blue-100 text-blue-800
                                        {% elif request.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif request.status == 'rejected' %}bg-red-100 text-red-800
                                        {% elif request.status == 'cancelled' %}bg-gray-100 text-gray-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ request.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ request.created_at|date:"Y-m-d H:i" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2 space-x-reverse">
                                        {% if request.status == 'pending' %}
                                            <button onclick="approveRequest({{ request.id }})" class="text-green-600 hover:text-green-900">
                                                <i class="fas fa-check ml-1"></i>
                                                موافقة
                                            </button>
                                            <button onclick="rejectRequest({{ request.id }})" class="text-red-600 hover:text-red-900">
                                                <i class="fas fa-times ml-1"></i>
                                                رفض
                                            </button>
                                        {% elif request.status == 'approved' %}
                                            <button onclick="openUploadProofModal({{ request.id }}, '{{ request.teacher.get_full_name }}', '{{ request.amount }}')" class="text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-upload ml-1"></i>
                                                رفع إثبات الدفع
                                            </button>
                                        {% endif %}
                                        <button onclick="viewPayoutDetails({{ request.id }})" class="text-purple-600 hover:text-purple-900">
                                            <i class="fas fa-eye ml-1"></i>
                                            عرض
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center">
                                    <i class="fas fa-hand-holding-usd text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات سحب</h3>
                                    <p class="text-gray-500">لم يتم إنشاء أي طلبات سحب بعد</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Approved Requests Tab -->
        <div id="approved-requests-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">الطلبات المعتمدة - في انتظار رفع إثبات الدفع</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعلم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة الدفع المفضلة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الموافقة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for request in payout_requests %}
                            {% if request.status == 'approved' %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-user text-green-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ request.teacher.get_full_name }}</div>
                                            <div class="text-sm text-gray-500">{{ request.teacher.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-bold text-green-600">${{ request.amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ request.payment_method|default:"غير محدد" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ request.processed_at|date:"Y-m-d H:i" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="openUploadProofModal({{ request.id }}, '{{ request.teacher.get_full_name }}', '{{ request.amount }}')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-upload ml-1"></i>
                                        رفع إثبات الدفع
                                    </button>
                                </td>
                            </tr>
                            {% endif %}
                            {% empty %}
                            <tr>
                                <td colspan="5" class="px-6 py-12 text-center">
                                    <i class="fas fa-check text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات معتمدة</h3>
                                    <p class="text-gray-500">جميع الطلبات المعتمدة تم دفعها</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Completed Payouts Tab -->
        <div id="completed-payouts-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">المدفوعات المكتملة</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعلم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الشهر/السنة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة الدفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإكمال</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for payout in completed_payouts %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-user text-green-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ payout.teacher.get_full_name }}</div>
                                            <div class="text-sm text-gray-500">{{ payout.teacher.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-bold text-green-600">${{ payout.amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ payout.month }}/{{ payout.year }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ payout.payment_method }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ payout.completed_at|date:"Y-m-d H:i" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="viewPayoutDetails({{ payout.payout_request.id }})" class="text-blue-600 hover:text-blue-900">
                                        <i class="fas fa-eye ml-1"></i>
                                        عرض التفاصيل
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-history text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مدفوعات مكتملة</h3>
                                    <p class="text-gray-500">ستظهر المدفوعات المكتملة هنا</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Proof Modal -->
<div id="uploadProofModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">رفع إثبات الدفع</h3>
                <button onclick="closeUploadProofModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4 p-4 bg-green-50 rounded-lg">
                <h4 class="font-medium text-green-900 mb-2">تفاصيل الدفعة</h4>
                <p class="text-sm text-green-800">المعلم: <span id="modalTeacherName" class="font-medium"></span></p>
                <p class="text-sm text-green-800">المبلغ: <span id="modalPayoutAmount" class="font-medium"></span></p>
            </div>

            <form method="post" enctype="multipart/form-data" id="uploadProofForm">
                {% csrf_token %}
                <input type="hidden" name="action" value="upload_proof">
                <input type="hidden" name="request_id" id="modalRequestId">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Payment Method -->
                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع</label>
                        <select name="payment_method" id="payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">شيك</option>
                            <option value="cash">نقدي</option>
                            <option value="paypal">PayPal</option>
                            <option value="western_union">ويسترن يونيون</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>

                    <!-- Transaction ID -->
                    <div>
                        <label for="transaction_id" class="block text-sm font-medium text-gray-700 mb-2">رقم المعاملة</label>
                        <input type="text" name="transaction_id" id="transaction_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" placeholder="رقم المعاملة أو المرجع">
                    </div>

                    <!-- Bank Name -->
                    <div>
                        <label for="bank_name" class="block text-sm font-medium text-gray-700 mb-2">اسم البنك</label>
                        <input type="text" name="bank_name" id="bank_name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" placeholder="اسم البنك المرسل">
                    </div>
                </div>

                <!-- Proof Image -->
                <div class="mt-4">
                    <label for="proof_image" class="block text-sm font-medium text-gray-700 mb-2">صورة إثبات الدفع</label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="proof_image" class="relative cursor-pointer bg-white rounded-md font-medium text-islamic-primary hover:text-islamic-secondary focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-islamic-primary">
                                    <span>ارفع صورة</span>
                                    <input id="proof_image" name="proof_image" type="file" class="sr-only" accept="image/*" required>
                                </label>
                                <p class="pr-1">أو اسحب وأفلت</p>
                            </div>
                            <p class="text-xs text-gray-500">PNG, JPG, GIF حتى 10MB</p>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mt-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" placeholder="أي ملاحظات أو تفاصيل إضافية..."></textarea>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse mt-6">
                    <button type="button" onclick="closeUploadProofModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-islamic-primary text-white rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-upload ml-1"></i>
                        رفع إثبات الدفع
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Payout Details Modal -->
<div id="payoutDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">تفاصيل طلب السحب</h3>
            <button onclick="closePayoutDetailsModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div id="payoutDetailsContent">
            <!-- سيتم ملء المحتوى بواسطة JavaScript -->
        </div>
    </div>
</div>

<script>
// Tab Management
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all buttons
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName + '-tab').classList.remove('hidden');

    // Add active class to selected button
    document.getElementById('tab-' + tabName).classList.add('active');
}

// Upload Proof Modal
function openUploadProofModal(requestId, teacherName, amount) {
    document.getElementById('modalRequestId').value = requestId;
    document.getElementById('modalTeacherName').textContent = teacherName;
    document.getElementById('modalPayoutAmount').textContent = '$' + amount;
    document.getElementById('uploadProofModal').classList.remove('hidden');
}

function closeUploadProofModal() {
    document.getElementById('uploadProofModal').classList.add('hidden');
    document.getElementById('uploadProofForm').reset();
}

// Payout Actions
function approveRequest(requestId) {
    const notes = prompt('ملاحظات الموافقة (اختياري):');
    if (notes !== null) {
        // Send AJAX request to approve
        fetch('{% url "admin_teacher_payouts" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `action=approve_payout&request_id=${requestId}&admin_notes=${encodeURIComponent(notes)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function rejectRequest(requestId) {
    const reason = prompt('سبب الرفض:');
    if (reason !== null && reason.trim() !== '') {
        // Send AJAX request to reject
        fetch('{% url "admin_teacher_payouts" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `action=reject_payout&request_id=${requestId}&reason=${encodeURIComponent(reason)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function viewPayoutDetails(requestId) {
    // جلب تفاصيل الطلب من الخادم
    fetch(`{% url "admin_teacher_payouts" %}?action=get_payout_details&request_id=${requestId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPayoutDetails(data.request);
        } else {
            alert('حدث خطأ في جلب التفاصيل: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال بالخادم');
    });
}

function displayPayoutDetails(request) {
    const content = `
        <div class="space-y-6">
            <!-- معلومات الطلب الأساسية -->
            <div class="bg-blue-50 rounded-lg p-4">
                <h4 class="font-bold text-blue-900 mb-3">معلومات الطلب</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">رقم الطلب:</span>
                        <span class="text-gray-900">#${request.id}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">الحالة:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(request.status)}">
                            ${getStatusLabel(request.status)}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">تاريخ الإنشاء:</span>
                        <span class="text-gray-900">${formatDate(request.created_at)}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">الشهر/السنة:</span>
                        <span class="text-gray-900">${request.month}/${request.year}</span>
                    </div>
                </div>
            </div>

            <!-- معلومات المعلم -->
            <div class="bg-green-50 rounded-lg p-4">
                <h4 class="font-bold text-green-900 mb-3">معلومات المعلم</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">الاسم:</span>
                        <span class="text-gray-900">${request.teacher_name}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">البريد الإلكتروني:</span>
                        <span class="text-gray-900">${request.teacher_email}</span>
                    </div>
                </div>
            </div>

            <!-- تفاصيل المبلغ -->
            <div class="bg-purple-50 rounded-lg p-4">
                <h4 class="font-bold text-purple-900 mb-3">تفاصيل المبلغ</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">المبلغ المطلوب:</span>
                        <span class="text-xl font-bold text-purple-600">$${request.amount}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">الأرباح المتاحة:</span>
                        <span class="text-gray-900">$${request.available_earnings}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">طريقة الدفع المفضلة:</span>
                        <span class="text-gray-900">${request.payment_method || 'غير محدد'}</span>
                    </div>
                </div>
            </div>

            <!-- تفاصيل البنك -->
            ${request.bank_details ? `
            <div class="bg-yellow-50 rounded-lg p-4">
                <h4 class="font-bold text-yellow-900 mb-3">تفاصيل البنك</h4>
                <p class="text-sm text-gray-700">${request.bank_details}</p>
            </div>
            ` : ''}

            <!-- إثبات الدفع (إذا كان موجوداً) -->
            ${request.proof ? `
            <div class="bg-green-50 rounded-lg p-4">
                <h4 class="font-bold text-green-900 mb-3">إثبات الدفع</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">طريقة الدفع:</span>
                        <span class="text-gray-900">${request.proof.payment_method}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">رقم المعاملة:</span>
                        <span class="text-gray-900">${request.proof.transaction_id || 'غير محدد'}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">اسم البنك:</span>
                        <span class="text-gray-900">${request.proof.bank_name || 'غير محدد'}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">تاريخ الرفع:</span>
                        <span class="text-gray-900">${formatDate(request.proof.uploaded_at)}</span>
                    </div>
                </div>

                <!-- صورة إثبات الدفع -->
                <div class="mt-4">
                    <span class="font-medium text-gray-700 block mb-2">صورة إثبات الدفع:</span>
                    <div class="border rounded-lg p-2 bg-white">
                        <img src="${request.proof.image_url}" alt="إثبات الدفع"
                             class="max-w-full h-auto max-h-64 mx-auto rounded cursor-pointer"
                             onclick="window.open('${request.proof.image_url}', '_blank')">
                    </div>
                    <p class="text-xs text-gray-500 mt-1 text-center">اضغط على الصورة لعرضها بحجم أكبر</p>
                </div>
            </div>
            ` : '<div class="bg-gray-50 rounded-lg p-4 text-center text-gray-500">لم يتم رفع إثبات دفع بعد</div>'}

            <!-- ملاحظات -->
            ${request.notes ? `
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-bold text-gray-900 mb-3">ملاحظات المعلم</h4>
                <p class="text-gray-700 text-sm">${request.notes}</p>
            </div>
            ` : ''}

            ${request.admin_notes ? `
            <div class="bg-blue-50 rounded-lg p-4">
                <h4 class="font-bold text-blue-900 mb-3">ملاحظات المدير</h4>
                <p class="text-gray-700 text-sm">${request.admin_notes}</p>
                <p class="text-xs text-gray-500 mt-2">بواسطة: ${request.processed_by || 'غير محدد'}</p>
            </div>
            ` : ''}

            <!-- أزرار الإجراءات -->
            ${request.status === 'pending' ? `
            <div class="flex justify-center space-x-4 space-x-reverse pt-4 border-t">
                <button onclick="approveRequest(${request.id}); closePayoutDetailsModal();"
                        class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-check ml-2"></i>
                    موافقة
                </button>
                <button onclick="rejectRequest(${request.id}); closePayoutDetailsModal();"
                        class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-times ml-2"></i>
                    رفض
                </button>
            </div>
            ` : request.status === 'approved' ? `
            <div class="flex justify-center pt-4 border-t">
                <button onclick="openUploadProofModal(${request.id}, '${request.teacher_name}', '${request.amount}'); closePayoutDetailsModal();"
                        class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-upload ml-2"></i>
                    رفع إثبات الدفع
                </button>
            </div>
            ` : ''}
        </div>
    `;

    document.getElementById('payoutDetailsContent').innerHTML = content;
    document.getElementById('payoutDetailsModal').classList.remove('hidden');
}

function closePayoutDetailsModal() {
    document.getElementById('payoutDetailsModal').classList.add('hidden');
}

function getStatusBadgeClass(status) {
    const classes = {
        'paid': 'bg-green-100 text-green-800',
        'approved': 'bg-blue-100 text-blue-800',
        'pending': 'bg-yellow-100 text-yellow-800',
        'rejected': 'bg-red-100 text-red-800',
        'cancelled': 'bg-gray-100 text-gray-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
}

function getStatusLabel(status) {
    const labels = {
        'paid': 'تم الدفع',
        'approved': 'موافق عليه',
        'pending': 'في الانتظار',
        'rejected': 'مرفوض',
        'cancelled': 'ملغي'
    };
    return labels[status] || status;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Modal close listeners
    document.getElementById('uploadProofModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeUploadProofModal();
        }
    });

    document.getElementById('payoutDetailsModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closePayoutDetailsModal();
        }
    });
});
</script>
{% endblock %}
