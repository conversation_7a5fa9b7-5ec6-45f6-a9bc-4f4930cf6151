#!/usr/bin/env python
"""
Script لاختبار نظام التحقق من التسجيلات الجديدة
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania_lms.settings')
django.setup()

from users.models import User
from django.utils import timezone

def create_test_users():
    """إنشاء مستخدمين تجريبيين لاختبار النظام"""
    
    print("🧪 إنشاء مستخدمين تجريبيين لاختبار نظام التحقق...")
    
    # حذف المستخدمين التجريبيين الموجودين
    test_users = ['test_teacher', 'test_student', 'test_teacher2', 'test_student2']
    for username in test_users:
        if User.objects.filter(username=username).exists():
            User.objects.filter(username=username).delete()
            print(f"  🗑️ تم حذف المستخدم التجريبي: {username}")
    
    # إنشاء معلم تجريبي في انتظار المراجعة
    teacher = User.objects.create_user(
        username='test_teacher',
        email='<EMAIL>',
        password='test123',
        first_name='أحمد',
        last_name='المعلم',
        user_type='teacher',
        phone='0501234567',
        is_active=False,
        verification_status='pending'
    )
    print(f"  ✅ تم إنشاء معلم تجريبي: {teacher.get_full_name()} (pending)")
    
    # إنشاء طالب تجريبي في انتظار المراجعة
    student = User.objects.create_user(
        username='test_student',
        email='<EMAIL>',
        password='test123',
        first_name='فاطمة',
        last_name='الطالبة',
        user_type='student',
        phone='0507654321',
        is_active=False,
        verification_status='pending'
    )
    print(f"  ✅ تم إنشاء طالب تجريبي: {student.get_full_name()} (pending)")
    
    # إنشاء معلم تجريبي مرفوض
    teacher_rejected = User.objects.create_user(
        username='test_teacher2',
        email='<EMAIL>',
        password='test123',
        first_name='محمد',
        last_name='المرفوض',
        user_type='teacher',
        phone='0509876543',
        is_active=False,
        verification_status='rejected',
        rejection_reason='لا تتوفر المؤهلات المطلوبة لتدريس القرآن الكريم'
    )
    print(f"  ❌ تم إنشاء معلم مرفوض: {teacher_rejected.get_full_name()} (rejected)")
    
    # إنشاء طالب تجريبي مرفوض
    student_rejected = User.objects.create_user(
        username='test_student2',
        email='<EMAIL>',
        password='test123',
        first_name='علي',
        last_name='المرفوض',
        user_type='student',
        phone='0502468135',
        is_active=False,
        verification_status='rejected',
        rejection_reason='بيانات غير صحيحة أو غير مكتملة'
    )
    print(f"  ❌ تم إنشاء طالب مرفوض: {student_rejected.get_full_name()} (rejected)")
    
    return [teacher, student, teacher_rejected, student_rejected]

def test_verification_system():
    """اختبار نظام التحقق"""
    
    print("\n🔍 اختبار نظام التحقق...")
    
    # إحصائيات التحقق
    pending_count = User.objects.filter(verification_status='pending').exclude(user_type='admin').count()
    approved_count = User.objects.filter(verification_status='approved').exclude(user_type='admin').count()
    rejected_count = User.objects.filter(verification_status='rejected').exclude(user_type='admin').count()
    
    print(f"\n📊 إحصائيات التحقق:")
    print(f"  ⏳ في الانتظار: {pending_count}")
    print(f"  ✅ تم القبول: {approved_count}")
    print(f"  ❌ تم الرفض: {rejected_count}")
    
    # اختبار دوال النموذج
    test_user = User.objects.filter(username='test_teacher').first()
    if test_user:
        print(f"\n🧪 اختبار دوال المستخدم التجريبي: {test_user.username}")
        print(f"  is_verified(): {test_user.is_verified()}")
        print(f"  is_pending_verification(): {test_user.is_pending_verification()}")
        print(f"  is_rejected(): {test_user.is_rejected()}")
        print(f"  can_access_dashboard(): {test_user.can_access_dashboard()}")
        print(f"  get_verification_status_display_ar(): {test_user.get_verification_status_display_ar()}")

def display_test_credentials():
    """عرض بيانات تسجيل الدخول للاختبار"""
    
    print("\n🔑 بيانات تسجيل الدخول للاختبار:")
    print("=" * 50)
    
    print("\n👨‍🏫 معلم في انتظار المراجعة:")
    print("  اسم المستخدم: test_teacher")
    print("  كلمة المرور: test123")
    print("  الحالة: pending")
    
    print("\n👨‍🎓 طالب في انتظار المراجعة:")
    print("  اسم المستخدم: test_student")
    print("  كلمة المرور: test123")
    print("  الحالة: pending")
    
    print("\n👨‍🏫 معلم مرفوض:")
    print("  اسم المستخدم: test_teacher2")
    print("  كلمة المرور: test123")
    print("  الحالة: rejected")
    
    print("\n👨‍🎓 طالب مرفوض:")
    print("  اسم المستخدم: test_student2")
    print("  كلمة المرور: test123")
    print("  الحالة: rejected")

def display_test_urls():
    """عرض الروابط للاختبار"""
    
    print("\n🔗 روابط الاختبار:")
    print("=" * 50)
    
    print("\n📝 صفحات التسجيل والدخول:")
    print("  التسجيل: http://localhost:8080/register/")
    print("  تسجيل الدخول: http://localhost:8080/login/")
    
    print("\n👨‍💼 لوحة تحكم المدير:")
    print("  طلبات التحقق: http://localhost:8080/dashboard/admin/user-verifications/")
    print("  إدارة المستخدمين: http://localhost:8080/dashboard/admin/users/")
    
    print("\n📄 صفحات التحقق:")
    print("  انتظار المراجعة: http://localhost:8080/verification-pending/")
    print("  رفض الطلب: http://localhost:8080/verification-rejected/")

def main():
    """الدالة الرئيسية"""
    
    print("🎯 اختبار نظام التحقق من التسجيلات الجديدة")
    print("=" * 60)
    
    try:
        # إنشاء مستخدمين تجريبيين
        test_users = create_test_users()
        
        # اختبار النظام
        test_verification_system()
        
        # عرض بيانات الاختبار
        display_test_credentials()
        display_test_urls()
        
        print("\n" + "=" * 60)
        print("✅ تم إعداد بيانات الاختبار بنجاح!")
        
        print("\n🧪 خطوات الاختبار المقترحة:")
        print("1. جرب تسجيل مستخدم جديد من صفحة التسجيل")
        print("2. جرب تسجيل الدخول بالمستخدمين التجريبيين")
        print("3. تحقق من صفحات انتظار المراجعة والرفض")
        print("4. اختبر لوحة إدارة طلبات التحقق (كمدير)")
        print("5. جرب قبول/رفض الطلبات من لوحة المدير")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
