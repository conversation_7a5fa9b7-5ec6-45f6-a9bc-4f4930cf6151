from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

User = get_user_model()


class Course(models.Model):
    """نموذج الدورة التعليمية"""

    COURSE_TYPES = (
        ('quran_memorization', _('حفظ القرآن')),
        ('quran_recitation', _('تلاوة القرآن')),
        ('quran_review', _('مراجعة القرآن')),
        ('islamic_studies', _('دراسات إسلامية')),
    )

    DIFFICULTY_LEVELS = (
        ('beginner', _('مبتدئ')),
        ('intermediate', _('متوسط')),
        ('advanced', _('متقدم')),
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الدورة')
    )

    description = models.TextField(
        verbose_name=_('وصف الدورة')
    )

    course_type = models.CharField(
        max_length=20,
        choices=COURSE_TYPES,
        verbose_name=_('نوع الدورة')
    )

    difficulty_level = models.CharField(
        max_length=15,
        choices=DIFFICULTY_LEVELS,
        verbose_name=_('مستوى الصعوبة')
    )

    duration_weeks = models.PositiveIntegerField(
        verbose_name=_('مدة الدورة (بالأسابيع)')
    )

    lessons_per_week = models.PositiveIntegerField(
        default=2,
        verbose_name=_('عدد الحصص في الأسبوع')
    )

    lesson_duration = models.PositiveIntegerField(
        choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')],
        default=45,
        verbose_name=_('مدة الحصة (بالدقائق)')
    )

    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('سعر الدورة')
    )

    max_students = models.PositiveIntegerField(
        default=1,
        verbose_name=_('الحد الأقصى للطلاب')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_courses',
        verbose_name=_('منشئ الدورة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('دورة')
        verbose_name_plural = _('الدورات')
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def get_total_lessons(self):
        """حساب إجمالي عدد الحصص في الدورة"""
        return self.duration_weeks * self.lessons_per_week


class Enrollment(models.Model):
    """نموذج تسجيل الطالب في الدورة"""

    STATUS_CHOICES = (
        ('pending', _('في الانتظار')),
        ('active', _('نشط')),
        ('completed', _('مكتمل')),
        ('cancelled', _('ملغي')),
        ('suspended', _('معلق')),
    )

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='enrollments',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    course = models.ForeignKey(
        Course,
        on_delete=models.CASCADE,
        related_name='enrollments',
        verbose_name=_('الدورة')
    )

    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='teaching_enrollments',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    start_date = models.DateField(
        verbose_name=_('تاريخ البداية')
    )

    end_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ النهاية')
    )

    amount_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ المدفوع')
    )

    remaining_lessons = models.PositiveIntegerField(
        verbose_name=_('الحصص المتبقية')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التسجيل')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('تسجيل')
        verbose_name_plural = _('التسجيلات')
        unique_together = ['student', 'course']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.course.title}"

    def is_active(self):
        """التحقق من كون التسجيل نشط"""
        return self.status == 'active'

    def save(self, *args, **kwargs):
        """حفظ التسجيل مع إرسال إشعارات"""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        # إرسال إشعار عند إنشاء تسجيل جديد
        if is_new:
            from notifications.utils import NotificationService
            NotificationService.notify_new_enrollment(self)


class StudentProgress(models.Model):
    """نموذج تقدم الطالب"""

    PROGRESS_TYPES = (
        ('memorization', _('حفظ')),
        ('review', _('مراجعة')),
        ('recitation', _('تلاوة')),
    )

    enrollment = models.ForeignKey(
        Enrollment,
        on_delete=models.CASCADE,
        related_name='progress_records',
        verbose_name=_('التسجيل')
    )

    progress_type = models.CharField(
        max_length=15,
        choices=PROGRESS_TYPES,
        verbose_name=_('نوع التقدم')
    )

    surah_name = models.CharField(
        max_length=100,
        verbose_name=_('اسم السورة')
    )

    from_verse = models.PositiveIntegerField(
        verbose_name=_('من الآية')
    )

    to_verse = models.PositiveIntegerField(
        verbose_name=_('إلى الآية')
    )

    completion_percentage = models.PositiveIntegerField(
        default=0,
        verbose_name=_('نسبة الإنجاز')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    recorded_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='recorded_progress',
        verbose_name=_('مسجل بواسطة')
    )

    recorded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التسجيل')
    )

    class Meta:
        verbose_name = _('تقدم الطالب')
        verbose_name_plural = _('تقدم الطلاب')
        ordering = ['-recorded_at']

    def __str__(self):
        return f"{self.enrollment.student.get_full_name()} - {self.surah_name}"


class Payment(models.Model):
    """نموذج المدفوعات"""

    PAYMENT_METHODS = (
        ('credit_card', _('بطاقة ائتمان')),
        ('bank_transfer', _('تحويل بنكي')),
        ('cash', _('نقداً')),
        ('wallet', _('محفظة إلكترونية')),
    )

    STATUS_CHOICES = (
        ('pending', _('في الانتظار')),
        ('processing', _('قيد المعالجة')),
        ('completed', _('مكتمل')),
        ('failed', _('فاشل')),
        ('refunded', _('مسترد')),
        ('cancelled', _('ملغي')),
    )

    enrollment = models.ForeignKey(
        Enrollment,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('التسجيل')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ')
    )

    payment_method = models.CharField(
        max_length=15,
        choices=PAYMENT_METHODS,
        verbose_name=_('طريقة الدفع')
    )

    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة الدفع')
    )

    transaction_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('معرف المعاملة')
    )

    payment_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الدفع')
    )

    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='processed_payments',
        verbose_name=_('معالج بواسطة')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    # حقول إضافية للتحكم اليدوي
    manual_status_change = models.BooleanField(
        default=False,
        verbose_name=_('تم تغيير الحالة يدوياً')
    )

    status_changed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='payment_status_changes',
        verbose_name=_('تم تغيير الحالة بواسطة')
    )

    status_changed_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ تغيير الحالة')
    )

    class Meta:
        verbose_name = _('دفعة')
        verbose_name_plural = _('المدفوعات')
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.enrollment.student.get_full_name()} - {self.amount} ريال"

    def is_successful(self):
        """التحقق من نجاح الدفع"""
        return self.status == 'completed'

    def change_status(self, new_status, changed_by=None):
        """تغيير حالة الدفع مع تسجيل التغيير"""
        from django.utils import timezone as tz

        old_status = self.status
        self.status = new_status
        self.manual_status_change = True
        self.status_changed_by = changed_by
        self.status_changed_at = tz.now()
        self.save()

        # تحديث حالة التسجيل إذا تم تأكيد الدفع
        if new_status == 'completed' and self.enrollment.status == 'pending':
            self.enrollment.status = 'active'
            self.enrollment.save()

            # إنشاء أرباح المعلم
            self.create_teacher_earning()

            # إرسال إشعارات
            from notifications.utils import NotificationService
            NotificationService.notify_payment_status_changed(self, changed_by)

        return f"تم تغيير حالة الدفع من {old_status} إلى {new_status}"

    def create_teacher_earning(self):
        """إنشاء ربح المعلم من هذه الدفعة"""
        from django.utils import timezone as tz

        # حساب ربح المعلم حسب مدة الحصة
        lesson_duration = self.enrollment.course.lesson_duration
        teacher_earning_amount = self.enrollment.teacher.calculate_teacher_earning(
            self.amount, lesson_duration
        )

        # إنشاء سجل ربح المعلم
        TeacherEarning.objects.create(
            teacher=self.enrollment.teacher,
            enrollment=self.enrollment,
            payment=self,
            amount=teacher_earning_amount,
            commission_rate=self.enrollment.teacher.get_commission_rate(lesson_duration),
            month=tz.now().month,
            year=tz.now().year
        )


class TeacherEarning(models.Model):
    """نموذج أرباح المعلم"""

    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='earnings',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    enrollment = models.ForeignKey(
        Enrollment,
        on_delete=models.CASCADE,
        related_name='teacher_earnings',
        verbose_name=_('التسجيل')
    )

    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='teacher_earnings',
        verbose_name=_('الدفعة')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('مبلغ الربح')
    )

    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=70.00,
        verbose_name=_('نسبة العمولة %')
    )

    lesson_duration = models.PositiveIntegerField(
        verbose_name=_('مدة الحصة (دقيقة)')
    )

    lesson_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('سعر الحصة')
    )

    lessons_completed = models.PositiveIntegerField(
        default=0,
        verbose_name=_('عدد الحصص المكتملة')
    )

    is_paid = models.BooleanField(
        default=False,
        verbose_name=_('مدفوع')
    )

    paid_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الدفع')
    )

    month = models.PositiveIntegerField(
        verbose_name=_('الشهر')
    )

    year = models.PositiveIntegerField(
        verbose_name=_('السنة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    # حقول تتبع الأرباح
    is_reserved = models.BooleanField(
        default=False,
        verbose_name=_('محجوز لطلب سحب')
    )

    reserved_for_request = models.ForeignKey(
        'TeacherPayoutRequest',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reserved_earnings',
        verbose_name=_('محجوز لطلب')
    )

    is_withdrawn = models.BooleanField(
        default=False,
        verbose_name=_('تم سحبه')
    )

    withdrawn_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ السحب')
    )

    class Meta:
        verbose_name = _('ربح المعلم')
        verbose_name_plural = _('أرباح المعلمين')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.teacher.get_full_name()} - {self.amount} ريال ({self.month}/{self.year})"

    def update_lessons_completed(self):
        """تحديث عدد الحصص المكتملة"""
        from lessons.models import Lesson

        completed_lessons = Lesson.objects.filter(
            enrollment__teacher=self.teacher,
            status='completed',
            enrollment__course__lesson_duration=self.lesson_duration,
            actual_end_time__month=self.month,
            actual_end_time__year=self.year
        ).count()

        self.lessons_completed = completed_lessons

        # إعادة حساب المبلغ بناءً على الحصص المكتملة
        from decimal import Decimal
        lesson_price = Decimal(str(self.lesson_price))
        commission_rate = Decimal(str(self.commission_rate))
        lessons_completed = Decimal(str(self.lessons_completed))
        self.amount = (lesson_price * commission_rate / Decimal('100')) * lessons_completed
        self.save()

    @classmethod
    def recalculate_teacher_earnings(cls, teacher, month, year):
        """إعادة حساب أرباح المعلم لشهر معين"""
        from lessons.models import Lesson

        # حذف الأرباح القديمة لهذا الشهر
        cls.objects.filter(teacher=teacher, month=month, year=year).delete()

        # حساب الأرباح الجديدة لكل مدة حصة
        for duration in [30, 45, 60]:
            # عدد الحصص المكتملة لهذه المدة
            completed_lessons = Lesson.objects.filter(
                enrollment__teacher=teacher,
                status='completed',
                enrollment__course__lesson_duration=duration,
                actual_end_time__month=month,
                actual_end_time__year=year
            ).count()

            if completed_lessons > 0:
                # الحصول على السعر والنسبة الحالية
                lesson_price = teacher.get_lesson_rate(duration)
                commission_rate = teacher.get_commission_rate(duration)

                # تحويل القيم إلى Decimal لضمان الدقة وتجنب أخطاء العمليات الحسابية
                from decimal import Decimal
                lesson_price = Decimal(str(lesson_price))
                commission_rate = Decimal(str(commission_rate))
                completed_lessons = Decimal(str(completed_lessons))

                # حساب الربح الإجمالي
                total_earning = (lesson_price * commission_rate / Decimal('100')) * completed_lessons

                # إنشاء سجل الربح
                cls.objects.create(
                    teacher=teacher,
                    enrollment=None,  # سيتم ربطه لاحقاً
                    payment=None,     # سيتم ربطه لاحقاً
                    amount=total_earning,
                    commission_rate=commission_rate,
                    lesson_duration=duration,
                    lesson_price=lesson_price,
                    lessons_completed=completed_lessons,
                    month=month,
                    year=year
                )

    @classmethod
    def get_available_earnings(cls, teacher, month=None, year=None):
        """حساب الأرباح المتاحة للسحب للمعلم"""
        from django.utils import timezone
        from decimal import Decimal

        if month is None:
            month = timezone.now().month
        if year is None:
            year = timezone.now().year

        # جلب جميع الأرباح للشهر المحدد
        earnings = cls.objects.filter(
            teacher=teacher,
            month=month,
            year=year
        )

        # حساب إجمالي الأرباح
        total_earnings = earnings.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')

        # حساب الأرباح المحجوزة (للطلبات المعلقة أو المعتمدة)
        reserved_earnings = earnings.filter(
            is_reserved=True,
            reserved_for_request__status__in=['pending', 'approved']
        ).aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')

        # حساب الأرباح المسحوبة
        withdrawn_earnings = earnings.filter(
            is_withdrawn=True
        ).aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')

        # الأرباح المتاحة = الإجمالي - المحجوز - المسحوب
        available_earnings = total_earnings - reserved_earnings - withdrawn_earnings

        return {
            'total': total_earnings,
            'reserved': reserved_earnings,
            'withdrawn': withdrawn_earnings,
            'available': available_earnings
        }

    @classmethod
    def reserve_earnings_for_request(cls, teacher, amount, payout_request):
        """حجز أرباح للمعلم لطلب سحب معين"""
        from decimal import Decimal

        # جلب الأرباح المتاحة مرتبة حسب التاريخ (الأقدم أولاً)
        available_earnings = cls.objects.filter(
            teacher=teacher,
            month=payout_request.month,
            year=payout_request.year,
            is_reserved=False,
            is_withdrawn=False
        ).order_by('created_at')

        remaining_amount = Decimal(str(amount))
        reserved_earnings = []

        for earning in available_earnings:
            if remaining_amount <= 0:
                break

            if earning.amount <= remaining_amount:
                # حجز الربح بالكامل
                earning.is_reserved = True
                earning.reserved_for_request = payout_request
                earning.save()
                reserved_earnings.append(earning)
                remaining_amount -= earning.amount
            else:
                # تقسيم الربح - إنشاء ربح جديد للجزء المحجوز
                reserved_part = cls.objects.create(
                    teacher=earning.teacher,
                    enrollment=earning.enrollment,
                    payment=earning.payment,
                    amount=remaining_amount,
                    commission_rate=earning.commission_rate,
                    lesson_duration=earning.lesson_duration,
                    lesson_price=earning.lesson_price,
                    lessons_completed=0,  # جزء من الحصص الأصلية
                    month=earning.month,
                    year=earning.year,
                    is_reserved=True,
                    reserved_for_request=payout_request
                )

                # تقليل المبلغ من الربح الأصلي
                earning.amount -= remaining_amount
                earning.save()

                reserved_earnings.append(reserved_part)
                remaining_amount = Decimal('0')

        return reserved_earnings

    @classmethod
    def release_reserved_earnings(cls, payout_request):
        """إلغاء حجز الأرباح عند رفض الطلب"""
        reserved_earnings = cls.objects.filter(
            reserved_for_request=payout_request,
            is_reserved=True
        )

        for earning in reserved_earnings:
            earning.is_reserved = False
            earning.reserved_for_request = None
            earning.save()

    @classmethod
    def mark_earnings_as_withdrawn(cls, payout_request):
        """تحديد الأرباح كمسحوبة عند اكتمال الدفع"""
        from django.utils import timezone

        reserved_earnings = cls.objects.filter(
            reserved_for_request=payout_request,
            is_reserved=True
        )

        for earning in reserved_earnings:
            earning.is_withdrawn = True
            earning.withdrawn_at = timezone.now()
            earning.save()


class TeacherRating(models.Model):
    """نموذج تقييم المعلم الإجمالي"""

    teacher = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='overall_rating',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    total_ratings = models.PositiveIntegerField(
        default=0,
        verbose_name=_('إجمالي التقييمات')
    )

    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0.00,
        verbose_name=_('متوسط التقييم')
    )

    teaching_quality_avg = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0.00,
        verbose_name=_('متوسط جودة التدريس')
    )

    interaction_avg = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0.00,
        verbose_name=_('متوسط التفاعل')
    )

    punctuality_avg = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0.00,
        verbose_name=_('متوسط الالتزام بالوقت')
    )

    last_updated = models.DateTimeField(
        auto_now=True,
        verbose_name=_('آخر تحديث')
    )

    class Meta:
        verbose_name = _('تقييم المعلم الإجمالي')
        verbose_name_plural = _('تقييمات المعلمين الإجمالية')

    def __str__(self):
        return f"{self.teacher.get_full_name()} - {self.average_rating}/5"

    def update_rating(self):
        """تحديث التقييم الإجمالي"""
        from lessons.models import LessonRating
        from django.db.models import Avg, Count

        ratings = LessonRating.objects.filter(
            lesson__enrollment__teacher=self.teacher
        ).aggregate(
            count=Count('id'),
            avg_teacher=Avg('teacher_rating'),
            avg_quality=Avg('lesson_quality'),
            avg_technical=Avg('technical_quality'),
            avg_satisfaction=Avg('overall_satisfaction')
        )

        self.total_ratings = ratings['count'] or 0
        if self.total_ratings > 0:
            self.teaching_quality_avg = ratings['avg_quality'] or 0
            self.interaction_avg = ratings['avg_teacher'] or 0
            self.punctuality_avg = ratings['avg_technical'] or 0

            # حساب المتوسط العام
            total = (
                (ratings['avg_teacher'] or 0) +
                (ratings['avg_quality'] or 0) +
                (ratings['avg_technical'] or 0) +
                (ratings['avg_satisfaction'] or 0)
            )
            self.average_rating = round(total / 4, 2)

        self.save()


class SubscriptionPlan(models.Model):
    """نموذج خطط الاشتراك"""
    DURATION_CHOICES = [
        ('monthly', _('شهري')),
        ('quarterly', _('ربع سنوي')),
        ('yearly', _('سنوي')),
    ]

    LESSON_DURATION_CHOICES = [
        (30, '30 دقيقة'),
        (45, '45 دقيقة'),
        (60, '60 دقيقة'),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم الخطة')
    )

    duration_type = models.CharField(
        max_length=20,
        choices=DURATION_CHOICES,
        verbose_name=_('نوع المدة')
    )

    lessons_count = models.PositiveIntegerField(
        verbose_name=_('عدد الحصص')
    )

    lesson_duration = models.IntegerField(
        choices=LESSON_DURATION_CHOICES,
        default=45,
        verbose_name=_('مدة الحصة (دقيقة)')
    )

    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('السعر (دولار)')
    )

    description = models.TextField(
        blank=True,
        verbose_name=_('الوصف')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('خطة اشتراك')
        verbose_name_plural = _('خطط الاشتراك')
        ordering = ['duration_type', 'lessons_count']

    def __str__(self):
        return f"{self.name} - {self.get_duration_type_display()} - ${self.price}"

    def get_duration_days(self):
        """الحصول على عدد الأيام للخطة"""
        duration_map = {
            'monthly': 30,
            'quarterly': 90,
            'yearly': 365,
        }
        return duration_map.get(self.duration_type, 30)


class Discount(models.Model):
    """نموذج الخصومات والعروض"""
    DISCOUNT_TYPES = [
        ('percentage', _('نسبة مئوية')),
        ('fixed', _('مبلغ ثابت')),
    ]

    code = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('كود الخصم')
    )

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم العرض')
    )

    discount_type = models.CharField(
        max_length=20,
        choices=DISCOUNT_TYPES,
        verbose_name=_('نوع الخصم')
    )

    value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('قيمة الخصم')
    )

    min_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('الحد الأدنى للمبلغ')
    )

    max_uses = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_('الحد الأقصى للاستخدام')
    )

    used_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('عدد مرات الاستخدام')
    )

    valid_from = models.DateTimeField(
        verbose_name=_('صالح من')
    )

    valid_until = models.DateTimeField(
        verbose_name=_('صالح حتى')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'admin'},
        verbose_name=_('أنشئ بواسطة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('خصم')
        verbose_name_plural = _('الخصومات')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.code})"

    def is_valid(self):
        """فحص صحة الخصم"""
        from django.utils import timezone
        now = timezone.now()

        if not self.is_active:
            return False

        if now < self.valid_from or now > self.valid_until:
            return False

        if self.max_uses and self.used_count >= self.max_uses:
            return False

        return True

    def calculate_discount(self, amount):
        """حساب قيمة الخصم"""
        if not self.is_valid() or amount < self.min_amount:
            return 0

        if self.discount_type == 'percentage':
            return min(amount * (self.value / 100), amount)
        else:  # fixed
            return min(self.value, amount)


class PaymentRequest(models.Model):
    """نموذج طلبات الدفع"""
    STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('paid', _('تم الدفع')),
        ('confirmed', _('مؤكد')),
        ('rejected', _('مرفوض')),
        ('expired', _('منتهي الصلاحية')),
    ]

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'student'},
        related_name='payment_requests',
        verbose_name=_('الطالب')
    )

    subscription_plan = models.ForeignKey(
        SubscriptionPlan,
        on_delete=models.CASCADE,
        verbose_name=_('خطة الاشتراك')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ (دولار)')
    )

    discount = models.ForeignKey(
        Discount,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name=_('الخصم المطبق')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_('مبلغ الخصم')
    )

    final_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ النهائي')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    start_date = models.DateField(
        verbose_name=_('تاريخ البداية')
    )

    end_date = models.DateField(
        verbose_name=_('تاريخ النهاية')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'admin'},
        related_name='created_payment_requests',
        verbose_name=_('أنشئ بواسطة')
    )

    due_date = models.DateTimeField(
        verbose_name=_('تاريخ الاستحقاق')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('طلب دفع')
        verbose_name_plural = _('طلبات الدفع')
        ordering = ['-created_at']

    def __str__(self):
        return f"طلب دفع #{self.id} - {self.student.get_full_name()} - ${self.final_amount}"

    def is_expired(self):
        """فحص انتهاء صلاحية الطلب"""
        from django.utils import timezone
        return timezone.now() > self.due_date

    def calculate_final_amount(self):
        """حساب المبلغ النهائي بعد الخصم"""
        self.amount = self.subscription_plan.price

        if self.discount:
            self.discount_amount = self.discount.calculate_discount(self.amount)
        else:
            self.discount_amount = 0

        self.final_amount = self.amount - self.discount_amount
        return self.final_amount

    def send_notification(self):
        """إرسال إشعار للطالب"""
        from notifications.utils import create_notification

        create_notification(
            recipient=self.student,
            notification_type='payment_request',
            title='طلب دفع جديد',
            message=f'لديك طلب دفع جديد بقيمة ${self.final_amount} لخطة {self.subscription_plan.name}',
            sender=self.created_by,
            priority='high',
            action_url='/dashboard/student/subscriptions/',
            action_text='عرض الطلب'
        )


class PaymentProof(models.Model):
    """نموذج إثباتات الدفع"""
    payment_request = models.OneToOneField(
        PaymentRequest,
        on_delete=models.CASCADE,
        related_name='proof',
        verbose_name=_('طلب الدفع')
    )

    proof_image = models.ImageField(
        upload_to='payment_proofs/%Y/%m/',
        verbose_name=_('صورة إثبات الدفع')
    )

    transaction_id = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('رقم المعاملة')
    )

    payment_method = models.CharField(
        max_length=50,
        verbose_name=_('طريقة الدفع')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات الطالب')
    )

    uploaded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الرفع')
    )

    class Meta:
        verbose_name = _('إثبات دفع')
        verbose_name_plural = _('إثباتات الدفع')
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"إثبات دفع - {self.payment_request.student.get_full_name()}"

    def send_admin_notification(self):
        """إرسال إشعار للمدير"""
        from notifications.utils import create_notification
        from django.contrib.auth import get_user_model

        User = get_user_model()
        admins = User.objects.filter(user_type='admin', is_active=True)

        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='payment_proof_uploaded',
                title='إثبات دفع جديد',
                message=f'رفع الطالب {self.payment_request.student.get_full_name()} إثبات دفع بقيمة ${self.payment_request.final_amount}',
                sender=self.payment_request.student,
                priority='medium',
                action_url='/dashboard/admin/payment-management/',
                action_text='مراجعة الدفعة'
            )


class StudentSubscription(models.Model):
    """نموذج اشتراكات الطلاب"""
    STATUS_CHOICES = [
        ('active', _('نشط')),
        ('expired', _('منتهي')),
        ('suspended', _('معلق')),
        ('cancelled', _('ملغي')),
    ]

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'student'},
        related_name='subscriptions',
        verbose_name=_('الطالب')
    )

    subscription_plan = models.ForeignKey(
        SubscriptionPlan,
        on_delete=models.CASCADE,
        verbose_name=_('خطة الاشتراك')
    )

    payment_request = models.OneToOneField(
        PaymentRequest,
        on_delete=models.CASCADE,
        related_name='subscription',
        verbose_name=_('طلب الدفع')
    )

    start_date = models.DateField(
        verbose_name=_('تاريخ البداية')
    )

    end_date = models.DateField(
        verbose_name=_('تاريخ النهاية')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('الحالة')
    )

    lessons_used = models.PositiveIntegerField(
        default=0,
        verbose_name=_('الحصص المستخدمة')
    )

    lessons_remaining = models.PositiveIntegerField(
        verbose_name=_('الحصص المتبقية')
    )

    auto_renewal = models.BooleanField(
        default=False,
        verbose_name=_('التجديد التلقائي')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('اشتراك طالب')
        verbose_name_plural = _('اشتراكات الطلاب')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.subscription_plan.name} ({self.status})"

    def is_active(self):
        """فحص نشاط الاشتراك"""
        from django.utils import timezone
        today = timezone.now().date()
        return (
            self.status == 'active' and
            self.start_date <= today <= self.end_date and
            self.lessons_remaining > 0
        )

    def is_expiring_soon(self, days=7):
        """فحص انتهاء الاشتراك قريباً"""
        from django.utils import timezone
        from datetime import timedelta

        expiry_threshold = timezone.now().date() + timedelta(days=days)
        return self.end_date <= expiry_threshold

    def use_lesson(self):
        """استخدام حصة من الاشتراك"""
        if self.lessons_remaining > 0:
            self.lessons_used += 1
            self.lessons_remaining -= 1
            self.save()

            # فحص انتهاء الحصص
            if self.lessons_remaining == 0:
                self.status = 'expired'
                self.save()
                self.send_expiry_notification()

            return True
        return False

    def send_expiry_notification(self):
        """إرسال إشعار انتهاء الاشتراك"""
        from notifications.utils import create_notification

        create_notification(
            recipient=self.student,
            notification_type='subscription_expired',
            title='انتهى اشتراكك',
            message=f'انتهى اشتراكك في خطة {self.subscription_plan.name}. يرجى التجديد للمتابعة.',
            priority='high',
            action_url='/dashboard/student/subscriptions/',
            action_text='تجديد الاشتراك'
        )

    def send_expiry_reminder(self):
        """إرسال تذكير انتهاء الاشتراك"""
        from notifications.utils import create_notification
        from django.utils import timezone

        days_remaining = (self.end_date - timezone.now().date()).days

        create_notification(
            recipient=self.student,
            notification_type='subscription_expiry_reminder',
            title='تذكير: اشتراكك ينتهي قريباً',
            message=f'اشتراكك في خطة {self.subscription_plan.name} سينتهي خلال {days_remaining} أيام.',
            priority='medium',
            action_url='/dashboard/student/subscriptions/',
            action_text='تجديد الاشتراك'
        )

    @classmethod
    def check_expiring_subscriptions(cls):
        """فحص الاشتراكات المنتهية قريباً وإرسال تذكيرات"""
        from django.utils import timezone
        from datetime import timedelta

        # الاشتراكات التي تنتهي خلال 7 أيام
        expiry_threshold = timezone.now().date() + timedelta(days=7)
        expiring_subscriptions = cls.objects.filter(
            status='active',
            end_date__lte=expiry_threshold,
            end_date__gte=timezone.now().date()
        )

        for subscription in expiring_subscriptions:
            subscription.send_expiry_reminder()

    def save(self, *args, **kwargs):
        """حفظ الاشتراك مع حساب الحصص المتبقية"""
        if not self.lessons_remaining:
            self.lessons_remaining = self.subscription_plan.lessons_count - self.lessons_used

        super().save(*args, **kwargs)


class TeacherPayoutRequest(models.Model):
    """نموذج طلبات سحب المعلمين"""
    STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('approved', _('موافق عليه')),
        ('paid', _('تم الدفع')),
        ('rejected', _('مرفوض')),
        ('cancelled', _('ملغي')),
    ]

    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'teacher'},
        related_name='payout_requests',
        verbose_name=_('المعلم')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ المطلوب')
    )

    available_earnings = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('الأرباح المتاحة')
    )

    month = models.IntegerField(
        verbose_name=_('الشهر')
    )

    year = models.IntegerField(
        verbose_name=_('السنة')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    payment_method = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('طريقة الدفع المفضلة')
    )

    bank_details = models.TextField(
        blank=True,
        verbose_name=_('تفاصيل البنك')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات المعلم')
    )

    admin_notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات المدير')
    )

    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'admin'},
        related_name='processed_payout_requests',
        verbose_name=_('تمت المعالجة بواسطة')
    )

    processed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ المعالجة')
    )

    # حقل لتتبع مشاهدة المعلم لآخر تحديث
    teacher_viewed_update = models.BooleanField(
        default=True,  # True عند الإنشاء لأن المعلم أنشأ الطلب
        verbose_name=_('شاهد المعلم آخر تحديث')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('طلب سحب معلم')
        verbose_name_plural = _('طلبات سحب المعلمين')
        ordering = ['-created_at']

    def __str__(self):
        return f"طلب سحب {self.teacher.get_full_name()} - {self.month}/{self.year} - ${self.amount}"

    def can_request_payout(self):
        """فحص إمكانية طلب السحب"""
        # فحص الأرباح المتاحة فقط
        current_earnings_data = TeacherEarning.get_available_earnings(
            teacher=self.teacher,
            month=self.month,
            year=self.year
        )
        return self.amount <= current_earnings_data['available']

    def send_notification(self):
        """إرسال إشعار للمدير"""
        from notifications.utils import create_notification
        from django.contrib.auth import get_user_model

        User = get_user_model()
        admins = User.objects.filter(user_type='admin', is_active=True)

        for admin in admins:
            create_notification(
                recipient=admin,
                notification_type='teacher_payout_request',
                title='طلب سحب جديد من معلم',
                message=f'طلب المعلم {self.teacher.get_full_name()} سحب مبلغ ${self.amount} من أرباح {self.month}/{self.year}',
                sender=self.teacher,
                priority='medium',
                action_url='/dashboard/admin/teacher-payouts/',
                action_text='مراجعة الطلب'
            )

    def approve_request(self, admin_user, admin_notes=''):
        """الموافقة على الطلب"""
        from django.utils import timezone

        self.status = 'approved'
        self.processed_by = admin_user
        self.processed_at = timezone.now()
        self.admin_notes = admin_notes
        self.teacher_viewed_update = False  # المعلم لم يشاهد التحديث بعد
        self.save()

        # إرسال إشعار للمعلم
        from notifications.utils import create_notification
        create_notification(
            recipient=self.teacher,
            notification_type='payout_approved',
            title='تمت الموافقة على طلب السحب',
            message=f'تمت الموافقة على طلب سحب ${self.amount}. سيتم التحويل قريباً.',
            sender=admin_user,
            priority='high',
            action_url='/dashboard/teacher/payouts/',
            action_text='عرض التفاصيل'
        )

    def reject_request(self, admin_user, reason=''):
        """رفض الطلب"""
        from django.utils import timezone

        self.status = 'rejected'
        self.processed_by = admin_user
        self.processed_at = timezone.now()
        self.admin_notes = reason
        self.teacher_viewed_update = False  # المعلم لم يشاهد التحديث بعد
        self.save()

        # إرسال إشعار للمعلم
        from notifications.utils import create_notification
        create_notification(
            recipient=self.teacher,
            notification_type='payout_rejected',
            title='تم رفض طلب السحب',
            message=f'تم رفض طلب سحب ${self.amount}. السبب: {reason}',
            sender=admin_user,
            priority='high',
            action_url='/dashboard/teacher/payouts/',
            action_text='عرض التفاصيل'
        )

    def save(self, *args, **kwargs):
        """حفظ طلب السحب مع إدارة حجز الأرباح"""
        is_new = self.pk is None
        old_status = None

        if not is_new:
            # جلب الحالة القديمة
            old_instance = TeacherPayoutRequest.objects.get(pk=self.pk)
            old_status = old_instance.status

        super().save(*args, **kwargs)

        if is_new:
            # حجز الأرباح للطلب الجديد
            self.reserve_earnings()
            self.send_notification()
        elif old_status != self.status:
            # تغيرت الحالة
            self.handle_status_change(old_status)

    def reserve_earnings(self):
        """حجز الأرباح لهذا الطلب"""
        TeacherEarning.reserve_earnings_for_request(
            teacher=self.teacher,
            amount=self.amount,
            payout_request=self
        )

    def release_earnings(self):
        """إلغاء حجز الأرباح"""
        TeacherEarning.release_reserved_earnings(self)

    def mark_earnings_withdrawn(self):
        """تحديد الأرباح كمسحوبة"""
        TeacherEarning.mark_earnings_as_withdrawn(self)

    def handle_status_change(self, old_status):
        """التعامل مع تغيير حالة الطلب"""
        if self.status == 'rejected' and old_status in ['pending', 'approved']:
            # إلغاء حجز الأرباح عند الرفض
            self.release_earnings()
        elif self.status == 'paid' and old_status == 'approved':
            # تحديد الأرباح كمسحوبة عند الدفع
            self.mark_earnings_withdrawn()


class AdminPayoutProof(models.Model):
    """نموذج إثباتات الدفع من المدير للمعلمين"""
    payout_request = models.OneToOneField(
        TeacherPayoutRequest,
        on_delete=models.CASCADE,
        related_name='payout_proof',
        verbose_name=_('طلب السحب')
    )

    proof_image = models.ImageField(
        upload_to='admin_payout_proofs/%Y/%m/',
        verbose_name=_('صورة إثبات الدفع')
    )

    payment_method = models.CharField(
        max_length=50,
        verbose_name=_('طريقة الدفع')
    )

    transaction_id = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('رقم المعاملة')
    )

    bank_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('اسم البنك')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات المدير')
    )

    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'admin'},
        verbose_name=_('رفع بواسطة')
    )

    uploaded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الرفع')
    )

    class Meta:
        verbose_name = _('إثبات دفع للمعلم')
        verbose_name_plural = _('إثباتات دفع المعلمين')
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"إثبات دفع - {self.payout_request.teacher.get_full_name()} - ${self.payout_request.amount}"

    def send_teacher_notification(self):
        """إرسال إشعار للمعلم"""
        from notifications.utils import create_notification

        create_notification(
            recipient=self.payout_request.teacher,
            notification_type='payout_completed',
            title='تم إرسال دفعتك',
            message=f'تم إرسال دفعة ${self.payout_request.amount} عبر {self.payment_method}. تحقق من إثبات الدفع.',
            sender=self.uploaded_by,
            priority='high',
            action_url='/dashboard/teacher/payouts/',
            action_text='عرض إثبات الدفع'
        )

    def save(self, *args, **kwargs):
        """حفظ إثبات الدفع وتحديث حالة الطلب"""
        super().save(*args, **kwargs)

        # تحديث حالة طلب السحب إلى "تم الدفع"
        self.payout_request.status = 'paid'
        self.payout_request.teacher_viewed_update = False  # المعلم لم يشاهد التحديث بعد
        self.payout_request.save()

        # إرسال إشعار للمعلم
        self.send_teacher_notification()


class TeacherPayout(models.Model):
    """نموذج سجل مدفوعات المعلمين المكتملة"""
    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'teacher'},
        related_name='completed_payouts',
        verbose_name=_('المعلم')
    )

    payout_request = models.OneToOneField(
        TeacherPayoutRequest,
        on_delete=models.CASCADE,
        related_name='completed_payout',
        verbose_name=_('طلب السحب')
    )

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('المبلغ المدفوع')
    )

    month = models.IntegerField(
        verbose_name=_('الشهر')
    )

    year = models.IntegerField(
        verbose_name=_('السنة')
    )

    payment_method = models.CharField(
        max_length=50,
        verbose_name=_('طريقة الدفع')
    )

    processed_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'admin'},
        verbose_name=_('تمت المعالجة بواسطة')
    )

    completed_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإكمال')
    )

    class Meta:
        verbose_name = _('دفعة معلم مكتملة')
        verbose_name_plural = _('دفعات المعلمين المكتملة')
        ordering = ['-completed_at']

    def __str__(self):
        return f"دفعة {self.teacher.get_full_name()} - {self.month}/{self.year} - ${self.amount}"

    @classmethod
    def create_from_request(cls, payout_request, admin_user):
        """إنشاء سجل دفعة مكتملة من طلب السحب"""
        return cls.objects.create(
            teacher=payout_request.teacher,
            payout_request=payout_request,
            amount=payout_request.amount,
            month=payout_request.month,
            year=payout_request.year,
            payment_method=payout_request.payment_method,
            processed_by=admin_user
        )