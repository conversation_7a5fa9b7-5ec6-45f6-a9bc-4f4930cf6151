from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.views import LoginView
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model, login as auth_login
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.db.models import Count, Q, Avg, Sum
from django.utils import timezone
from datetime import timedelta
from django.urls import reverse

from .models import AcademySettings
from .forms import AcademySettingsForm

User = get_user_model()


class CustomLoginView(LoginView):
    """صفحة تسجيل الدخول المخصصة"""
    template_name = 'auth/login.html'
    redirect_authenticated_user = True

    def form_invalid(self, form):
        """عرض رسالة خطأ عند فشل تسجيل الدخول"""
        # التحقق من وجود المستخدم حتى لو كان غير نشط
        username = form.data.get('username')
        password = form.data.get('password')

        if username and password:
            # التحقق من وجود الحساب في سجلات الحذف
            from logs.models import SystemLog
            deleted_user_log = SystemLog.objects.filter(
                action='delete_user',
                _data__icontains=f'"username": "{username}"'
            ).first()

            if deleted_user_log:
                # استخراج معلومات الحذف من السجل
                deleted_data = deleted_user_log.data

                # حفظ معلومات الحذف في الجلسة
                self.request.session['deleted_username'] = deleted_data.get('username', '')
                self.request.session['deleted_user_type'] = deleted_data.get('user_type', '')
                self.request.session['delete_reason'] = deleted_data.get('delete_reason', '')
                self.request.session['deleted_at'] = deleted_data.get('deleted_at', '')
                self.request.session['deleted_by'] = deleted_data.get('deleted_by', '')

                messages.warning(
                    self.request,
                    "هذا الحساب تم حذفه من قبل الإدارة."
                )

                # توجيه للصفحة العامة للحسابات المحذوفة
                return redirect('public_deleted_page')

            try:
                # البحث عن المستخدم بغض النظر عن حالة is_active
                # محاولة البحث باسم المستخدم أو البريد الإلكتروني
                user = User.objects.get(Q(username=username) | Q(email=username))

                # التحقق من كلمة المرور
                from django.contrib.auth.hashers import check_password
                if check_password(password, user.password):
                    # كلمة المرور صحيحة، التحقق من حالة الحساب

                    # حفظ معلومات المستخدم في الجلسة
                    self.request.session['username'] = user.username
                    self.request.session['user_type'] = user.user_type

                    # التحقق من الحظر أولاً
                    if user.is_currently_banned():
                        self.request.session['banned_username'] = user.username
                        self.request.session['ban_reason'] = user.ban_reason
                        self.request.session['ban_type'] = user.ban_type
                        if user.banned_until:
                            self.request.session['banned_until'] = user.banned_until.strftime('%Y-%m-%d %H:%M')

                        messages.warning(
                            self.request,
                            f"حسابك محظور حالياً. {user.ban_reason or ''}"
                        )

                        # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                        if user.was_active:
                            # مستخدم كان نشطًا ثم تم حظره - توجيه للصفحة الداخلية
                            return redirect('user_banned')
                        else:
                            # مستخدم جديد تم حظره قبل التفعيل - توجيه للصفحة العامة
                            return redirect('public_banned_page')

                    # التحقق من حالة التحقق
                    if user.verification_status == 'pending':
                        self.request.session['pending_username'] = user.username
                        self.request.session['pending_user_type'] = user.user_type

                        messages.info(
                            self.request,
                            "حسابك قيد المراجعة. يرجى الانتظار حتى تتم الموافقة عليه."
                        )

                        # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                        if user.was_active:
                            # مستخدم كان نشطًا ثم تم تعليقه للمراجعة - توجيه للصفحة الداخلية
                            return redirect('verification_pending')
                        else:
                            # مستخدم جديد قيد المراجعة - توجيه للصفحة العامة
                            return redirect('public_pending_page')

                    elif user.verification_status == 'rejected':
                        self.request.session['rejected_username'] = user.username
                        self.request.session['rejected_user_type'] = user.user_type
                        self.request.session['rejection_reason'] = user.rejection_reason

                        messages.warning(
                            self.request,
                            f"تم رفض حسابك. {user.rejection_reason or ''}"
                        )

                        # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                        if user.was_active:
                            # مستخدم كان نشطًا ثم تم رفضه - توجيه للصفحة الداخلية
                            return redirect('verification_rejected')
                        else:
                            # مستخدم جديد تم رفضه - توجيه للصفحة العامة
                            return redirect('public_rejected_page')

                    # إذا وصلنا إلى هنا، فهناك مشكلة أخرى في الحساب
                    messages.error(
                        self.request,
                        "لا يمكن تسجيل الدخول. يرجى التواصل مع الإدارة."
                    )
                    return super().form_invalid(form)

            except User.DoesNotExist:
                # المستخدم غير موجود، استمر في السلوك الافتراضي
                pass

        # إضافة رسالة خطأ واضحة
        messages.error(
            self.request,
            "اسم المستخدم أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى."
        )
        return super().form_invalid(form)

    def form_valid(self, form):
        """التحقق من حالة المستخدم قبل تسجيل الدخول"""
        # الحصول على المستخدم
        username = form.cleaned_data.get('username')
        try:
            # محاولة البحث باسم المستخدم أو البريد الإلكتروني
            user = User.objects.get(Q(username=username) | Q(email=username))

            # التحقق من الحظر أولاً
            if not user.is_admin() and user.is_currently_banned():
                # حفظ معلومات المستخدم في الجلسة
                self.request.session['banned_username'] = user.username
                self.request.session['ban_reason'] = user.ban_reason
                self.request.session['ban_type'] = user.ban_type
                if user.banned_until:
                    self.request.session['banned_until'] = user.banned_until.strftime('%Y-%m-%d %H:%M')

                # تسجيل الدخول للمستخدم مع تحديد backend
                from django.contrib.auth import get_user_model
                from users.backends import EmailOrUsernameModelBackend
                auth_login(self.request, user, backend='users.backends.EmailOrUsernameModelBackend')
                # إضافة رسالة توضيحية
                messages.warning(
                    self.request,
                    f"حسابك محظور حالياً. {user.ban_reason or ''}"
                )

                # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                if user.was_active:
                    # مستخدم كان نشطًا ثم تم حظره - توجيه للصفحة الداخلية
                    return redirect('user_banned')
                else:
                    # مستخدم جديد تم حظره قبل التفعيل - توجيه للصفحة العامة
                    return redirect('public_banned_page')

            # التحقق من حالة التحقق
            if not user.is_admin() and user.verification_status == 'pending':
                # حفظ معلومات المستخدم في الجلسة
                self.request.session['pending_username'] = user.username
                self.request.session['pending_user_type'] = user.user_type

                # تسجيل الدخول للمستخدم مع تحديد backend
                auth_login(self.request, user, backend='users.backends.EmailOrUsernameModelBackend')
                # إضافة رسالة توضيحية
                messages.info(
                    self.request,
                    "حسابك قيد المراجعة. يرجى الانتظار حتى تتم الموافقة عليه."
                )

                # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                if user.was_active:
                    # مستخدم كان نشطًا ثم تم تعليقه للمراجعة - توجيه للصفحة الداخلية
                    return redirect('verification_pending')
                else:
                    # مستخدم جديد قيد المراجعة - توجيه للصفحة العامة
                    return redirect('public_pending_page')

            elif not user.is_admin() and user.verification_status == 'rejected':
                # حفظ معلومات المستخدم في الجلسة
                self.request.session['rejected_username'] = user.username
                self.request.session['rejected_user_type'] = user.user_type
                self.request.session['rejection_reason'] = user.rejection_reason

                # تسجيل الدخول للمستخدم مع تحديد backend
                auth_login(self.request, user, backend='users.backends.EmailOrUsernameModelBackend')
                # إضافة رسالة توضيحية
                messages.warning(
                    self.request,
                    f"تم رفض حسابك. {user.rejection_reason or ''}"
                )

                # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                if user.was_active:
                    # مستخدم كان نشطًا ثم تم رفضه - توجيه للصفحة الداخلية
                    return redirect('verification_rejected')
                else:
                    # مستخدم جديد تم رفضه - توجيه للصفحة العامة
                    return redirect('public_rejected_page')

            # تسجيل الدخول الناجح مع تحديد backend
            # استخدام form.get_user() بدلاً من super().form_valid(form)
            auth_login(self.request, user, backend='users.backends.EmailOrUsernameModelBackend')
            # إضافة رسالة ترحيب
            messages.success(
                self.request,
                f"مرحباً بك {user.get_full_name() or user.username}! تم تسجيل الدخول بنجاح."
            )
            # الانتقال إلى صفحة النجاح
            return redirect(self.get_success_url())

        except User.DoesNotExist:
            # استمرار في السلوك الافتراضي (سيتم التعامل معه في form_invalid)
            pass

        return super().form_valid(form)

    def get_success_url(self):
        """توجيه المستخدم حسب نوعه بعد تسجيل الدخول"""
        user = self.request.user

        # التحقق من الحظر أولاً
        if user.is_currently_banned():
            return reverse('user_banned')

        # التحقق من حالة التحقق
        if not user.can_access_dashboard():
            if user.is_pending_verification():
                return reverse('verification_pending')
            elif user.is_rejected():
                return reverse('verification_rejected')

        # توجيه حسب نوع المستخدم
        if user.is_admin():
            return '/dashboard/admin/'
        elif user.is_teacher():
            return '/dashboard/teacher/'
        elif user.is_student():
            return '/dashboard/student/'
        return '/dashboard/'


def dashboard_redirect(request):
    """توجيه المستخدم إلى لوحة التحكم المناسبة"""
    if request.user.is_authenticated:
        # التحقق من الحظر أولاً
        if request.user.is_currently_banned():
            return redirect('user_banned')

        # التحقق من حالة التحقق
        if not request.user.is_admin():
            if request.user.verification_status == 'pending':
                return redirect('verification_pending')
            elif request.user.verification_status == 'rejected':
                return redirect('verification_rejected')

        # التوجيه حسب نوع المستخدم
        if request.user.is_admin():
            return redirect('/dashboard/admin/')
        elif request.user.is_teacher():
            return redirect('/dashboard/teacher/')
        elif request.user.is_student():
            return redirect('/dashboard/student/')
        return redirect('/dashboard/')
    return redirect('login')


@login_required
def dashboard(request):
    """لوحة التحكم العامة - توجيه المستخدم حسب نوعه"""
    # التحقق من الحظر أولاً
    if request.user.is_currently_banned():
        return redirect('user_banned')

    # التحقق من حالة التحقق
    if not request.user.is_admin():
        if request.user.verification_status == 'pending':
            return redirect('verification_pending')
        elif request.user.verification_status == 'rejected':
            return redirect('verification_rejected')

    # التوجيه حسب نوع المستخدم
    if request.user.is_admin():
        return redirect('/dashboard/admin/')
    elif request.user.is_teacher():
        return redirect('/dashboard/teacher/')
    elif request.user.is_student():
        return redirect('/dashboard/student/')
    else:
        messages.error(request, "نوع المستخدم غير محدد.")
        return redirect('login')


@login_required
def admin_dashboard(request):
    """لوحة تحكم المدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    from datetime import timedelta
    from courses.models import Course, Enrollment, Payment
    from lessons.models import Lesson
    from support.models import SupportTicket

    # إحصائيات المستخدمين
    total_users = User.objects.count()
    total_students = User.objects.filter(user_type='student').count()
    total_teachers = User.objects.filter(user_type='teacher').count()

    # إحصائيات الدورات والتسجيلات
    total_courses = Course.objects.filter(is_active=True).count()
    active_enrollments = Enrollment.objects.filter(status='active').count()

    # إحصائيات الحصص
    today = timezone.now().date()
    today_lessons = Lesson.objects.filter(
        scheduled_date__date=today,
        status__in=['scheduled', 'in_progress']
    ).count()

    # إحصائيات الدعم الفني
    open_tickets = SupportTicket.objects.filter(
        status__in=['open', 'in_progress', 'waiting_response']
    ).count()

    # المستخدمون الجدد هذا الأسبوع
    week_ago = timezone.now() - timedelta(days=7)
    new_users_this_week = User.objects.filter(
        created_at__gte=week_ago
    ).count()

    # إحصائيات مالية
    from django.db.models import Sum
    total_revenue = Payment.objects.filter(
        status='completed'
    ).aggregate(
        total=Sum('amount')
    )['total'] or 0

    # الحصص المكتملة هذا الشهر
    month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    completed_lessons_this_month = Lesson.objects.filter(
        status='completed',
        actual_end_time__gte=month_start
    ).count()

    context = {
        'total_users': total_users,
        'total_students': total_students,
        'total_teachers': total_teachers,
        'total_courses': total_courses,
        'active_enrollments': active_enrollments,
        'today_lessons': today_lessons,
        'open_tickets': open_tickets,
        'new_users_this_week': new_users_this_week,
        'total_revenue': total_revenue,
        'completed_lessons_this_month': completed_lessons_this_month,
    }

    return render(request, 'dashboard/admin.html', context)


@login_required
def admin_courses(request):
    """صفحة إدارة الدورات مع البيانات الحقيقية والعلاقات المترابطة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.db.models import Count, Avg, Q
    from courses.models import Course, Enrollment
    from lessons.models import Lesson

    # جلب جميع الدورات مع إحصائياتهم
    courses = Course.objects.all().order_by('-created_at')

    # فلترة حسب النوع إذا تم تحديده
    course_type_filter = request.GET.get('course_type')
    if course_type_filter:
        courses = courses.filter(course_type=course_type_filter)

    # فلترة حسب المستوى
    difficulty_filter = request.GET.get('difficulty')
    if difficulty_filter:
        courses = courses.filter(difficulty_level=difficulty_filter)

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        courses = courses.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # إضافة إحصائيات لكل دورة
    courses_with_stats = []
    for course in courses:
        # إحصائيات التسجيلات
        enrollments = Enrollment.objects.filter(course=course)
        active_enrollments = enrollments.filter(status='active')
        completed_enrollments = enrollments.filter(status='completed')

        # إحصائيات الحصص
        total_lessons = Lesson.objects.filter(enrollment__course=course)
        completed_lessons = total_lessons.filter(status='completed')

        # معدل الإكمال
        completion_rate = 0
        if enrollments.count() > 0:
            completion_rate = round((completed_enrollments.count() / enrollments.count()) * 100, 1)

        # متوسط التقييم
        avg_rating = 0
        if completed_lessons.exists():
            from lessons.models import LessonRating
            ratings = LessonRating.objects.filter(
                lesson__enrollment__course=course
            ).aggregate(avg=Avg('teacher_rating'))
            avg_rating = round(ratings['avg'] or 0, 1)

        course_stats = {
            'course': course,
            'total_enrollments': enrollments.count(),
            'active_enrollments': active_enrollments.count(),
            'completed_enrollments': completed_enrollments.count(),
            'total_lessons': total_lessons.count(),
            'completed_lessons': completed_lessons.count(),
            'completion_rate': completion_rate,
            'avg_rating': avg_rating,
            'status': 'نشط' if course.is_active else 'غير نشط'
        }

        courses_with_stats.append(course_stats)

    # إحصائيات عامة
    total_courses = Course.objects.count()
    active_courses = Course.objects.filter(is_active=True).count()
    total_enrollments = Enrollment.objects.count()
    active_enrollments = Enrollment.objects.filter(status='active').count()

    # الدورات الأكثر شعبية
    popular_courses = Course.objects.annotate(
        enrollment_count=Count('enrollments')
    ).order_by('-enrollment_count')[:5]

    # المعلمين المتاحين
    available_teachers = User.objects.filter(
        user_type='teacher',
        is_active=True,
        is_active_teacher=True
    )

    context = {
        'courses_with_stats': courses_with_stats,
        'total_courses': total_courses,
        'active_courses': active_courses,
        'total_enrollments': total_enrollments,
        'active_enrollments': active_enrollments,
        'popular_courses': popular_courses,
        'available_teachers': available_teachers,
        'course_type_filter': course_type_filter,
        'difficulty_filter': difficulty_filter,
        'search_query': search_query,
        'course_types': Course.COURSE_TYPES,
        'difficulty_levels': Course.DIFFICULTY_LEVELS,
    }

    return render(request, 'admin/courses.html', context)


@login_required
def admin_reports(request):
    """صفحة التقارير والإحصائيات"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    from datetime import timedelta
    from courses.models import Course, Enrollment, Payment, TeacherRating
    from lessons.models import Lesson, LessonRating
    from django.db.models import Sum, Avg, Count

    # الفترة الزمنية للتقرير
    today = timezone.now().date()
    month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    last_month_start = (month_start - timedelta(days=1)).replace(day=1)

    # الإحصائيات الرئيسية
    total_users = User.objects.count()
    completed_lessons = Lesson.objects.filter(status='completed').count()
    total_revenue = Payment.objects.filter(status='completed').aggregate(
        total=Sum('amount')
    )['total'] or 0

    # متوسط التقييم العام
    average_rating = LessonRating.objects.aggregate(
        avg=Avg('overall_satisfaction')
    )['avg'] or 0

    # نمو المستخدمين
    last_month_users = User.objects.filter(
        created_at__gte=last_month_start,
        created_at__lt=month_start
    ).count()
    this_month_users = User.objects.filter(
        created_at__gte=month_start
    ).count()

    user_growth = 0
    if last_month_users > 0:
        user_growth = round(((this_month_users - last_month_users) / last_month_users) * 100, 1)

    # إحصائيات المستخدمين
    students_count = User.objects.filter(user_type='student').count()
    teachers_count = User.objects.filter(user_type='teacher').count()
    admins_count = User.objects.filter(user_type='admin').count()
    active_users = User.objects.filter(is_active=True).count()

    # إحصائيات الدورات
    total_courses = Course.objects.count()
    active_courses = Course.objects.filter(is_active=True).count()
    active_enrollments = Enrollment.objects.filter(status='active').count()

    # معدل الإكمال
    total_enrollments = Enrollment.objects.count()
    completed_enrollments = Enrollment.objects.filter(status='completed').count()
    completion_rate = 0
    if total_enrollments > 0:
        completion_rate = round((completed_enrollments / total_enrollments) * 100, 1)

    # أفضل المعلمين
    top_teachers = TeacherRating.objects.filter(
        total_ratings__gt=0
    ).order_by('-average_rating')[:3]

    # أشهر الدورات
    popular_courses = Course.objects.annotate(
        enrollment_count=Count('enrollments')
    ).filter(enrollment_count__gt=0).order_by('-enrollment_count')[:3]

    context = {
        'total_users': total_users,
        'completed_lessons': completed_lessons,
        'total_revenue': total_revenue,
        'avg_rating': round(average_rating, 1),
        'user_growth': user_growth,
        'lesson_growth': 8,  # يمكن حسابه لاحقاً
        'revenue_growth': 15,  # يمكن حسابه لاحقاً
        'rating_improvement': 0.2,  # يمكن حسابه لاحقاً
        'students_count': students_count,
        'teachers_count': teachers_count,
        'admins_count': admins_count,
        'active_users': active_users,
        'this_month_users': this_month_users,
        'total_courses': total_courses,
        'active_courses': active_courses,
        'active_enrollments': active_enrollments,
        'completion_rate': completion_rate,
        'top_teachers': top_teachers,
        'popular_courses': popular_courses,
    }

    return render(request, 'admin/reports.html', context)


@login_required
def teacher_dashboard(request):
    """لوحة تحكم المعلم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    from datetime import timedelta
    from courses.models import Enrollment, TeacherEarning, TeacherRating
    from lessons.models import Lesson
    from django.db.models import Sum, Count, Avg

    # إحصائيات الحصص
    today = timezone.now().date()
    today_lessons_count = Lesson.objects.filter(
        enrollment__teacher=request.user,
        scheduled_date__date=today,
        status__in=['scheduled', 'in_progress']
    ).count()

    upcoming_lessons_count = Lesson.objects.filter(
        enrollment__teacher=request.user,
        scheduled_date__gt=timezone.now(),
        status='scheduled'
    ).count()

    # إحصائيات الطلاب
    total_students = Enrollment.objects.filter(
        teacher=request.user,
        status='active'
    ).values('student').distinct().count()

    # إحصائيات التقييم
    try:
        teacher_rating = TeacherRating.objects.get(teacher=request.user)
        avg_rating = float(teacher_rating.average_rating)
        total_ratings = teacher_rating.total_ratings
    except TeacherRating.DoesNotExist:
        avg_rating = 0.0
        total_ratings = 0

    # إحصائيات الأرباح الشهرية
    current_month = timezone.now().month
    current_year = timezone.now().year
    monthly_earnings = TeacherEarning.objects.filter(
        teacher=request.user,
        month=current_month,
        year=current_year
    ).aggregate(
        total=Sum('amount')
    )['total'] or 0

    # الحصص المكتملة
    completed_lessons = Lesson.objects.filter(
        enrollment__teacher=request.user,
        status='completed'
    ).count()

    context = {
        'today_lessons_count': today_lessons_count,
        'upcoming_lessons_count': upcoming_lessons_count,
        'total_students': total_students,
        'avg_rating': avg_rating,
        'total_ratings': total_ratings,
        'monthly_earnings': monthly_earnings,
        'completed_lessons': completed_lessons,
    }

    return render(request, 'dashboard/teacher.html', context)


@login_required
def teacher_students(request):
    """صفحة إدارة طلاب المعلم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from courses.models import Enrollment
    from lessons.models import Lesson
    from django.db.models import Count, Q
    from django.utils import timezone

    # جلب تسجيلات المعلم
    enrollments = Enrollment.objects.filter(
        teacher=request.user,
        status='active'
    ).select_related('student', 'course').prefetch_related('lessons')

    # إحصائيات الطلاب
    total_students = enrollments.count()

    # الطلاب النشطون (لديهم حصص قادمة)
    active_students = enrollments.filter(
        lessons__scheduled_date__gt=timezone.now(),
        lessons__status='scheduled'
    ).distinct().count()

    # الطلاب الجدد (مسجلين خلال آخر 30 يوم)
    thirty_days_ago = timezone.now() - timezone.timedelta(days=30)
    new_students = enrollments.filter(
        created_at__gte=thirty_days_ago
    ).count()

    # الطلاب المكملون (أكملوا جميع حصصهم)
    completed_students = Enrollment.objects.filter(
        teacher=request.user,
        status='completed'
    ).count()

    # إضافة معلومات إضافية لكل تسجيل
    for enrollment in enrollments:
        # حساب الحصص المتبقية
        enrollment.remaining_lessons_count = enrollment.lessons.filter(
            status='scheduled'
        ).count()

        # حساب التقدم
        total_lessons = enrollment.lessons.count()
        completed_lessons = enrollment.lessons.filter(status='completed').count()

        if total_lessons > 0:
            enrollment.progress_percentage = round((completed_lessons / total_lessons) * 100)
        else:
            enrollment.progress_percentage = 0

        # آخر حصة
        last_lesson = enrollment.lessons.filter(
            status='completed'
        ).order_by('-actual_end_time').first()

        enrollment.last_lesson_date = last_lesson.actual_end_time.date() if last_lesson else None

    context = {
        'enrollments': enrollments,
        'total_students': total_students,
        'active_students': active_students,
        'new_students': new_students,
        'completed_students': completed_students,
    }

    return render(request, 'teacher/students.html', context)


@login_required
def teacher_earnings(request):
    """صفحة أرباح المعلم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    from courses.models import TeacherEarning, TeacherPayoutRequest
    from lessons.models import Lesson
    from django.db.models import Sum, Count, Avg

    # الشهر الحالي
    current_month = timezone.now().month
    current_year = timezone.now().year

    # جلب الأرباح الفعلية من قاعدة البيانات
    monthly_earnings_qs = TeacherEarning.objects.filter(
        teacher=request.user,
        month=current_month,
        year=current_year
    )

    # حساب إجمالي الأرباح الشهرية
    monthly_earnings = monthly_earnings_qs.aggregate(total=Sum('amount'))['total'] or 0

    # تفصيل الأرباح حسب مدة الحصة من البيانات الفعلية
    earnings_breakdown = {}
    for duration in [30, 45, 60]:
        duration_earnings = monthly_earnings_qs.filter(lesson_duration=duration)
        total_amount = duration_earnings.aggregate(total=Sum('amount'))['total'] or 0
        total_lessons = duration_earnings.aggregate(total=Sum('lessons_completed'))['total'] or 0

        # جلب آخر سعر وعمولة مسجلة
        latest_earning = duration_earnings.first()
        if latest_earning:
            lesson_price = latest_earning.lesson_price
            commission_rate = latest_earning.commission_rate
        else:
            lesson_price = request.user.get_lesson_rate(duration)
            commission_rate = request.user.get_commission_rate(duration)

        earnings_breakdown[duration] = {
            'amount': total_amount,
            'lessons_completed': total_lessons,
            'commission_rate': commission_rate,
            'lesson_price': lesson_price
        }

    # الحصص المكتملة هذا الشهر (من البيانات الفعلية)
    completed_lessons = monthly_earnings_qs.aggregate(total=Sum('lessons_completed'))['total'] or 0

    # ساعات التدريس هذا الشهر (حساب تقريبي من الأرباح)
    total_minutes = 0
    for duration in [30, 45, 60]:
        lessons_count = earnings_breakdown[duration]['lessons_completed']
        total_minutes += lessons_count * duration

    teaching_hours = round(total_minutes / 60, 1) if total_minutes > 0 else 0

    # متوسط الأجر بالساعة
    from decimal import Decimal
    hourly_average = round(float(monthly_earnings) / teaching_hours, 2) if teaching_hours > 0 else 0

    # تفصيل الأرباح حسب مدة الحصة
    earnings_30min = {
        'total': earnings_breakdown[30]['amount'],
        'count': earnings_breakdown[30]['lessons_completed']
    }

    earnings_45min = {
        'total': earnings_breakdown[45]['amount'],
        'count': earnings_breakdown[45]['lessons_completed']
    }

    earnings_60min = {
        'total': earnings_breakdown[60]['amount'],
        'count': earnings_breakdown[60]['lessons_completed']
    }

    # سجل المدفوعات (آخر 6 أشهر) من البيانات الفعلية
    payment_history = []
    for i in range(6):
        month = current_month - i
        year = current_year
        if month <= 0:
            month += 12
            year -= 1

        # الأرباح الفعلية للشهر
        month_earnings = TeacherEarning.objects.filter(
            teacher=request.user,
            month=month,
            year=year
        ).aggregate(total=Sum('amount'))['total'] or 0

        # فحص حالة الدفع من طلبات السحب
        payout_request = TeacherPayoutRequest.objects.filter(
            teacher=request.user,
            month=month,
            year=year
        ).first()

        is_paid = False
        status_text = "في الانتظار"

        if payout_request:
            if payout_request.status == 'paid':
                is_paid = True
                status_text = "تم الدفع"
            elif payout_request.status == 'approved':
                status_text = "معتمد - في انتظار الدفع"
            elif payout_request.status == 'rejected':
                status_text = "مرفوض"
            elif payout_request.status == 'pending':
                status_text = "قيد المراجعة"

        # أسماء الأشهر بالعربية
        month_names = {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }

        payment_history.append({
            'month_name': month_names[month],
            'year': year,
            'amount': month_earnings,
            'is_paid': is_paid,
            'status_text': status_text,
            'is_current': month == current_month and year == current_year
        })

    context = {
        'monthly_earnings': monthly_earnings,
        'completed_lessons': completed_lessons,
        'teaching_hours': teaching_hours,
        'hourly_average': hourly_average,
        'earnings_30min': earnings_30min,
        'earnings_45min': earnings_45min,
        'earnings_60min': earnings_60min,
        'earnings_breakdown': earnings_breakdown,
        'payment_history': payment_history,
        'current_month': current_month,
        'current_year': current_year,
    }

    return render(request, 'teacher/earnings.html', context)


@login_required
def admin_teacher_rates(request):
    """صفحة إدارة أسعار المعلمين"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # جلب جميع المعلمين مع بيانات الأرباح
    from django.utils import timezone
    from courses.models import TeacherEarning
    from lessons.models import Lesson
    from django.db.models import Sum

    teachers = User.objects.filter(user_type='teacher', is_active=True)
    current_month = timezone.now().month
    current_year = timezone.now().year

    # إضافة بيانات الأرباح لكل معلم
    for teacher in teachers:
        teacher.earnings_data = {}
        teacher.total_monthly_earnings = 0

        for duration in [30, 45, 60]:
            # عدد الحصص المكتملة
            completed_lessons = Lesson.objects.filter(
                enrollment__teacher=teacher,
                status='completed',
                enrollment__course__lesson_duration=duration,
                actual_end_time__month=current_month,
                actual_end_time__year=current_year
            ).count()

            # حساب الأرباح المحققة
            lesson_price = teacher.get_lesson_rate(duration)
            commission_rate = teacher.get_commission_rate(duration)

            # تحويل القيم إلى Decimal لضمان الدقة وتجنب أخطاء العمليات الحسابية
            from decimal import Decimal
            lesson_price = Decimal(str(lesson_price))
            commission_rate = Decimal(str(commission_rate))
            completed_lessons = Decimal(str(completed_lessons))

            earnings = (lesson_price * commission_rate / Decimal('100')) * completed_lessons

            teacher.earnings_data[duration] = {
                'completed_lessons': completed_lessons,
                'earnings': earnings,
                'rate': teacher.get_lesson_rate(duration),
                'commission': teacher.get_commission_rate(duration)
            }

            teacher.total_monthly_earnings += earnings

    if request.method == 'POST':
        teacher_id = request.POST.get('teacher_id')
        try:
            teacher = User.objects.get(id=teacher_id, user_type='teacher')

            # تحديث أسعار الحصص
            teacher.hourly_rate_30 = request.POST.get('rate_30', 50.00)
            teacher.hourly_rate_45 = request.POST.get('rate_45', 70.00)
            teacher.hourly_rate_60 = request.POST.get('rate_60', 90.00)

            # تحديث نسب العمولة
            teacher.commission_rate_30 = request.POST.get('commission_30', 70.00)
            teacher.commission_rate_45 = request.POST.get('commission_45', 70.00)
            teacher.commission_rate_60 = request.POST.get('commission_60', 70.00)

            teacher.save()

            # إعادة حساب أرباح المعلم للشهر الحالي
            from django.utils import timezone
            from courses.models import TeacherEarning
            current_month = timezone.now().month
            current_year = timezone.now().year

            TeacherEarning.recalculate_teacher_earnings(teacher, current_month, current_year)

            # إرسال إشعار للمعلم
            from notifications.utils import NotificationService
            NotificationService.notify_teacher_rates_changed(teacher, request.user)

            messages.success(request, f"تم تحديث أسعار المعلم {teacher.get_full_name()} وإعادة حساب أرباحه بنجاح.")

        except User.DoesNotExist:
            messages.error(request, "المعلم غير موجود.")
        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

    context = {
        'teachers': teachers,
    }

    return render(request, 'admin/teacher_rates.html', context)


@login_required
def admin_payment_management(request):
    """صفحة إدارة المدفوعات"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from courses.models import (
        Payment, PaymentRequest, PaymentProof, StudentSubscription,
        SubscriptionPlan, Discount
    )
    from django.http import JsonResponse
    from django.utils import timezone
    from datetime import timedelta

    # معالجة طلبات GET للتفاصيل
    if request.method == 'GET' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        action = request.GET.get('action')
        if action == 'get_request_details':
            try:
                request_id = request.GET.get('request_id')
                payment_request = PaymentRequest.objects.select_related(
                    'student', 'subscription_plan', 'discount', 'created_by'
                ).get(id=request_id)

                # جلب إثبات الدفع إذا كان موجوداً
                proof_data = None
                try:
                    proof = PaymentProof.objects.get(payment_request=payment_request)
                    proof_data = {
                        'payment_method': proof.payment_method,
                        'transaction_id': proof.transaction_id,
                        'notes': proof.notes,
                        'uploaded_at': proof.uploaded_at.isoformat(),
                        'image_url': proof.proof_image.url if proof.proof_image else None
                    }
                except PaymentProof.DoesNotExist:
                    pass

                request_data = {
                    'id': payment_request.id,
                    'status': payment_request.status,
                    'student_name': payment_request.student.get_full_name(),
                    'student_email': payment_request.student.email,
                    'subscription_plan': payment_request.subscription_plan.name,
                    'lessons_count': payment_request.subscription_plan.lessons_count,
                    'lesson_duration': payment_request.subscription_plan.lesson_duration,
                    'amount': str(payment_request.amount),
                    'discount_code': payment_request.discount.code if payment_request.discount else None,
                    'discount_amount': str(payment_request.discount_amount),
                    'final_amount': str(payment_request.final_amount),
                    'start_date': payment_request.start_date.isoformat(),
                    'end_date': payment_request.end_date.isoformat(),
                    'due_date': payment_request.due_date.isoformat(),
                    'created_at': payment_request.created_at.isoformat(),
                    'notes': payment_request.notes,
                    'proof': proof_data
                }

                return JsonResponse({'success': True, 'request': request_data})

            except PaymentRequest.DoesNotExist:
                return JsonResponse({'success': False, 'message': 'طلب الدفع غير موجود'})
            except Exception as e:
                return JsonResponse({'success': False, 'message': str(e)})

    # معالجة الطلبات POST
    if request.method == 'POST':
        action = request.POST.get('action')

        # إنشاء طلب دفع جديد
        if action == 'create_request':
            try:
                students_ids = request.POST.getlist('students')
                subscription_plan_id = request.POST.get('subscription_plan')
                discount_id = request.POST.get('discount') or None
                due_date = request.POST.get('due_date')
                start_date = request.POST.get('start_date')
                end_date = request.POST.get('end_date')
                notes = request.POST.get('notes', '')

                subscription_plan = SubscriptionPlan.objects.get(id=subscription_plan_id)
                discount = Discount.objects.get(id=discount_id) if discount_id else None

                created_count = 0
                for student_id in students_ids:
                    student = User.objects.get(id=student_id, user_type='student')

                    # حساب المبلغ النهائي
                    amount = subscription_plan.price
                    discount_amount = 0
                    if discount:
                        discount_amount = discount.calculate_discount(amount)
                    final_amount = amount - discount_amount

                    # إنشاء طلب الدفع
                    payment_request = PaymentRequest.objects.create(
                        student=student,
                        subscription_plan=subscription_plan,
                        amount=amount,
                        discount=discount,
                        discount_amount=discount_amount,
                        final_amount=final_amount,
                        start_date=start_date,
                        end_date=end_date,
                        due_date=due_date,
                        notes=notes,
                        created_by=request.user
                    )

                    # إرسال إشعار للطالب
                    payment_request.send_notification()
                    created_count += 1

                messages.success(request, f'تم إنشاء {created_count} طلب دفع بنجاح!')
                return redirect('admin_payment_management')

            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء إنشاء طلبات الدفع: {str(e)}')

        # تأكيد الدفع
        elif action == 'confirm_payment':
            try:
                request_id = request.POST.get('request_id')
                payment_request = PaymentRequest.objects.get(id=request_id)

                # تحديث حالة الطلب
                payment_request.status = 'confirmed'
                payment_request.save()

                # إنشاء اشتراك للطالب
                subscription = StudentSubscription.objects.create(
                    student=payment_request.student,
                    subscription_plan=payment_request.subscription_plan,
                    payment_request=payment_request,
                    start_date=payment_request.start_date,
                    end_date=payment_request.end_date,
                    lessons_remaining=payment_request.subscription_plan.lessons_count
                )

                # إرسال إشعار للطالب
                from notifications.utils import create_notification
                create_notification(
                    recipient=payment_request.student,
                    notification_type='payment_confirmed',
                    title='تم تأكيد دفعتك',
                    message=f'تم تأكيد دفعتك لخطة {payment_request.subscription_plan.name} وتم تفعيل اشتراكك.',
                    sender=request.user,
                    priority='high',
                    action_url='/dashboard/student/subscriptions/',
                    action_text='عرض الاشتراك'
                )

                return JsonResponse({'success': True})

            except Exception as e:
                return JsonResponse({'success': False, 'message': str(e)})

        # رفض الدفع
        elif action == 'reject_payment':
            try:
                request_id = request.POST.get('request_id')
                reason = request.POST.get('reason', '')
                payment_request = PaymentRequest.objects.get(id=request_id)

                # تحديث حالة الطلب
                payment_request.status = 'rejected'
                if reason:
                    payment_request.notes = f"{payment_request.notes}\n\nسبب الرفض: {reason}"
                payment_request.save()

                # إرسال إشعار للطالب
                from notifications.utils import create_notification
                create_notification(
                    recipient=payment_request.student,
                    notification_type='payment_rejected',
                    title='تم رفض دفعتك',
                    message=f'تم رفض دفعتك لخطة {payment_request.subscription_plan.name}. {reason}',
                    sender=request.user,
                    priority='high',
                    action_url='/dashboard/student/subscriptions/',
                    action_text='عرض التفاصيل'
                )

                return JsonResponse({'success': True})

            except Exception as e:
                return JsonResponse({'success': False, 'message': str(e)})

        # تغيير حالة الدفع القديم
        else:
            payment_id = request.POST.get('payment_id')
            new_status = request.POST.get('new_status')

            try:
                payment = Payment.objects.get(id=payment_id)
                result = payment.change_status(new_status, request.user)

                # إرسال إشعارات
                from notifications.utils import NotificationService
                NotificationService.notify_payment_status_changed(payment, request.user)

                messages.success(request, result)

            except Payment.DoesNotExist:
                messages.error(request, "الدفعة غير موجودة.")
            except Exception as e:
                messages.error(request, f"حدث خطأ: {str(e)}")

            return redirect('admin_payment_management')

    # جلب البيانات للعرض
    payment_requests = PaymentRequest.objects.all().select_related(
        'student', 'subscription_plan', 'discount', 'created_by'
    ).order_by('-created_at')

    payment_proofs = PaymentProof.objects.all().select_related(
        'payment_request__student', 'payment_request__subscription_plan'
    ).order_by('-uploaded_at')

    # المدفوعات القديمة
    payments = Payment.objects.all().order_by('-payment_date')

    # البيانات للنماذج
    students = User.objects.filter(user_type='student', is_active=True)
    subscription_plans = SubscriptionPlan.objects.filter(is_active=True)
    discounts = Discount.objects.filter(is_active=True)

    # الإحصائيات
    payment_requests_count = payment_requests.count()
    confirmed_payments = payment_requests.filter(status='confirmed').count()
    pending_payments = payment_requests.filter(status='pending').count()
    total_revenue = sum([req.final_amount for req in payment_requests.filter(status='confirmed')])

    context = {
        'payment_requests': payment_requests,
        'payment_proofs': payment_proofs,
        'payments': payments,
        'students': students,
        'subscription_plans': subscription_plans,
        'discounts': discounts,
        'payment_requests_count': payment_requests_count,
        'confirmed_payments': confirmed_payments,
        'pending_payments': pending_payments,
        'total_revenue': total_revenue,
        'status_choices': Payment.STATUS_CHOICES,
    }

    return render(request, 'admin/payment_management.html', context)

@login_required
def admin_teacher_payouts(request):
    """صفحة إدارة مدفوعات المعلمين"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from courses.models import (
        TeacherPayoutRequest, AdminPayoutProof, TeacherPayout
    )
    from django.http import JsonResponse
    from django.utils import timezone
    from datetime import timedelta

    # معالجة طلبات GET للتفاصيل
    if request.method == 'GET' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        action = request.GET.get('action')
        if action == 'get_payout_details':
            try:
                request_id = request.GET.get('request_id')
                payout_request = TeacherPayoutRequest.objects.select_related(
                    'teacher', 'processed_by'
                ).get(id=request_id)

                # جلب إثبات الدفع إذا كان موجوداً
                proof_data = None
                try:
                    proof = AdminPayoutProof.objects.get(payout_request=payout_request)
                    proof_data = {
                        'payment_method': proof.payment_method,
                        'transaction_id': proof.transaction_id,
                        'bank_name': proof.bank_name,
                        'notes': proof.notes,
                        'uploaded_at': proof.uploaded_at.isoformat(),
                        'image_url': proof.proof_image.url if proof.proof_image else None
                    }
                except AdminPayoutProof.DoesNotExist:
                    pass

                request_data = {
                    'id': payout_request.id,
                    'status': payout_request.status,
                    'teacher_name': payout_request.teacher.get_full_name(),
                    'teacher_email': payout_request.teacher.email,
                    'amount': str(payout_request.amount),
                    'available_earnings': str(payout_request.available_earnings),
                    'month': payout_request.month,
                    'year': payout_request.year,
                    'payment_method': payout_request.payment_method,
                    'bank_details': payout_request.bank_details,
                    'notes': payout_request.notes,
                    'admin_notes': payout_request.admin_notes,
                    'created_at': payout_request.created_at.isoformat(),
                    'processed_at': payout_request.processed_at.isoformat() if payout_request.processed_at else None,
                    'processed_by': payout_request.processed_by.get_full_name() if payout_request.processed_by else None,
                    'proof': proof_data
                }

                return JsonResponse({'success': True, 'request': request_data})

            except TeacherPayoutRequest.DoesNotExist:
                return JsonResponse({'success': False, 'message': 'طلب السحب غير موجود'})
            except Exception as e:
                return JsonResponse({'success': False, 'message': str(e)})

    # معالجة الطلبات POST
    if request.method == 'POST':
        action = request.POST.get('action')

        # الموافقة على طلب السحب
        if action == 'approve_payout':
            try:
                request_id = request.POST.get('request_id')
                admin_notes = request.POST.get('admin_notes', '')
                payout_request = TeacherPayoutRequest.objects.get(id=request_id)

                payout_request.approve_request(request.user, admin_notes)

                return JsonResponse({'success': True})

            except Exception as e:
                return JsonResponse({'success': False, 'message': str(e)})

        # رفض طلب السحب
        elif action == 'reject_payout':
            try:
                request_id = request.POST.get('request_id')
                reason = request.POST.get('reason', '')
                payout_request = TeacherPayoutRequest.objects.get(id=request_id)

                payout_request.reject_request(request.user, reason)

                return JsonResponse({'success': True})

            except Exception as e:
                return JsonResponse({'success': False, 'message': str(e)})

        # رفع إثبات الدفع
        elif action == 'upload_proof':
            try:
                request_id = request.POST.get('request_id')
                payment_method = request.POST.get('payment_method')
                transaction_id = request.POST.get('transaction_id', '')
                bank_name = request.POST.get('bank_name', '')
                notes = request.POST.get('notes', '')
                proof_image = request.FILES.get('proof_image')

                payout_request = TeacherPayoutRequest.objects.get(
                    id=request_id,
                    status='approved'
                )

                # إنشاء إثبات الدفع
                proof = AdminPayoutProof.objects.create(
                    payout_request=payout_request,
                    proof_image=proof_image,
                    payment_method=payment_method,
                    transaction_id=transaction_id,
                    bank_name=bank_name,
                    notes=notes,
                    uploaded_by=request.user
                )

                # إنشاء سجل الدفعة المكتملة
                TeacherPayout.create_from_request(payout_request, request.user)

                messages.success(request, 'تم رفع إثبات الدفع بنجاح!')
                return redirect('admin_teacher_payouts')

            except TeacherPayoutRequest.DoesNotExist:
                messages.error(request, 'طلب السحب غير موجود أو غير صالح.')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء رفع إثبات الدفع: {str(e)}')

    # جلب البيانات للعرض
    payout_requests = TeacherPayoutRequest.objects.all().select_related(
        'teacher', 'processed_by'
    ).order_by('-created_at')

    payout_proofs = AdminPayoutProof.objects.all().select_related(
        'payout_request__teacher', 'uploaded_by'
    ).order_by('-uploaded_at')

    completed_payouts = TeacherPayout.objects.all().select_related(
        'teacher', 'processed_by', 'payout_request'
    ).order_by('-completed_at')

    # الإحصائيات
    total_requests = payout_requests.count()
    pending_requests = payout_requests.filter(status='pending').count()
    approved_requests = payout_requests.filter(status='approved').count()
    completed_requests = payout_requests.filter(status='paid').count()
    total_paid = sum([req.amount for req in payout_requests.filter(status='paid')])

    context = {
        'payout_requests': payout_requests,
        'payout_proofs': payout_proofs,
        'completed_payouts': completed_payouts,
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'completed_requests': completed_requests,
        'total_paid': total_paid,
    }

    return render(request, 'admin/teacher_payouts.html', context)


@login_required
def admin_ratings_view(request):
    """صفحة عرض تقييمات المعلمين للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import LessonRating
    from courses.models import TeacherRating
    from django.db.models import Avg, Count

    # جلب تقييمات المعلمين الإجمالية
    teacher_ratings = TeacherRating.objects.filter(
        total_ratings__gt=0
    ).order_by('-average_rating')

    # جلب آخر التقييمات
    recent_ratings = LessonRating.objects.select_related(
        'lesson__enrollment__teacher',
        'lesson__enrollment__student'
    ).order_by('-created_at')[:20]

    # إحصائيات عامة
    total_ratings = LessonRating.objects.count()
    average_rating = LessonRating.objects.aggregate(
        avg=Avg('overall_satisfaction')
    )['avg'] or 0

    context = {
        'teacher_ratings': teacher_ratings,
        'recent_ratings': recent_ratings,
        'total_ratings': total_ratings,
        'average_rating': round(average_rating, 2),
    }

    return render(request, 'admin/ratings_view.html', context)


@login_required
def admin_academy_settings(request):
    """صفحة إعدادات الأكاديمية"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # الحصول على إعدادات الأكاديمية الحالية أو إنشاء إعدادات افتراضية
    academy_settings = AcademySettings.get_settings()

    if request.method == 'POST':
        form = AcademySettingsForm(request.POST, request.FILES, instance=academy_settings)
        if form.is_valid():
            form.save()
            messages.success(request, "تم حفظ إعدادات الأكاديمية بنجاح.")
            return redirect('admin_academy_settings')
    else:
        form = AcademySettingsForm(instance=academy_settings)

    context = {
        'form': form,
        'academy_settings': academy_settings,
    }

    return render(request, 'admin/academy_settings.html', context)


@login_required
def teacher_ratings(request):
    """صفحة تقييمات المعلم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # بيانات تجريبية للتقييمات
    context = {
        'avg_rating': 4.8,
        'total_ratings': 45,
        'teaching_quality': 4.9,
        'interaction': 4.7,
        'punctuality': 4.8,
    }

    return render(request, 'teacher/ratings.html', context)


@login_required
def student_dashboard(request):
    """لوحة تحكم الطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    from courses.models import Enrollment
    from lessons.models import Lesson
    from django.db.models import Count, Avg

    # إحصائيات الحصص
    today = timezone.now().date()
    today_lessons_count = Lesson.objects.filter(
        enrollment__student=request.user,
        scheduled_date__date=today,
        status__in=['scheduled', 'in_progress']
    ).count()

    upcoming_lessons_count = Lesson.objects.filter(
        enrollment__student=request.user,
        scheduled_date__gt=timezone.now(),
        status='scheduled'
    ).count()

    # إحصائيات الدورات
    total_courses = Enrollment.objects.filter(
        student=request.user,
        status='active'
    ).count()

    # إحصائيات الحصص المكتملة والمتبقية
    total_lessons_completed = Lesson.objects.filter(
        enrollment__student=request.user,
        status='completed'
    ).count()

    total_lessons_scheduled = Lesson.objects.filter(
        enrollment__student=request.user,
        status__in=['scheduled', 'in_progress', 'completed']
    ).count()

    total_lessons_remaining = Lesson.objects.filter(
        enrollment__student=request.user,
        status='scheduled'
    ).count()

    # حساب نسبة التقدم
    if total_lessons_scheduled > 0:
        progress_percentage = round((total_lessons_completed / total_lessons_scheduled) * 100)
    else:
        progress_percentage = 0

    # المستوى الحالي (يمكن تحسينه لاحقاً)
    current_level = request.user.student_level or 'مبتدئ'

    context = {
        'today_lessons_count': today_lessons_count,
        'upcoming_lessons_count': upcoming_lessons_count,
        'total_courses': total_courses,
        'total_lessons_completed': total_lessons_completed,
        'total_lessons_remaining': total_lessons_remaining,
        'progress_percentage': progress_percentage,
        'current_level': current_level,
    }

    return render(request, 'dashboard/student.html', context)


@login_required
def student_progress(request):
    """صفحة تقدم الطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # بيانات تجريبية للتقدم
    context = {
        'completed_lessons': 24,
        'total_lessons': 40,
        'memorized_surahs': 18,
        'avg_rating': 4.7,
        'learning_hours': 36,
        'progress_percentage': 60,
        'attendance_rate': 95,
        'average_score': 88,
    }

    return render(request, 'student/progress.html', context)


# Enhanced Admin Views with Real Data Integration
@login_required
def admin_users(request):
    """صفحة إدارة المستخدمين مع البيانات الحقيقية والعلاقات المترابطة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.db.models import Q, Count, Avg
    from courses.models import Enrollment, Course
    from lessons.models import Lesson

    # جلب جميع المستخدمين مع إحصائياتهم
    users = User.objects.all().order_by('-created_at')

    # فلترة حسب النوع إذا تم تحديده
    user_type_filter = request.GET.get('user_type')
    if user_type_filter:
        users = users.filter(user_type=user_type_filter)

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        users = users.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    # إضافة إحصائيات لكل مستخدم
    users_with_stats = []
    for user in users:
        user_stats = {
            'user': user,
            'enrollments_count': 0,
            'lessons_count': 0,
            'avg_rating': 0,
            'status': 'نشط' if user.is_active else 'غير نشط'
        }

        if user.user_type == 'student':
            # إحصائيات الطالب
            enrollments = Enrollment.objects.filter(student=user)
            user_stats['enrollments_count'] = enrollments.count()
            user_stats['lessons_count'] = Lesson.objects.filter(
                enrollment__student=user
            ).count()

        elif user.user_type == 'teacher':
            # إحصائيات المعلم
            enrollments = Enrollment.objects.filter(teacher=user)
            user_stats['enrollments_count'] = enrollments.count()
            user_stats['lessons_count'] = Lesson.objects.filter(
                enrollment__teacher=user
            ).count()

            # متوسط التقييم
            from courses.models import TeacherRating
            teacher_rating = TeacherRating.objects.filter(teacher=user).first()
            if teacher_rating:
                user_stats['avg_rating'] = teacher_rating.average_rating

        users_with_stats.append(user_stats)

    # إحصائيات عامة
    total_users = User.objects.count()
    total_students = User.objects.filter(user_type='student').count()
    total_teachers = User.objects.filter(user_type='teacher').count()
    total_admins = User.objects.filter(user_type='admin').count()
    active_users = User.objects.filter(is_active=True).count()

    # المستخدمين الجدد هذا الشهر
    from django.utils import timezone
    current_month = timezone.now().replace(day=1)
    new_users_this_month = User.objects.filter(
        created_at__gte=current_month
    ).count()

    # حساب النسب المئوية
    students_percentage = round((total_students * 100 / total_users), 1) if total_users > 0 else 0
    active_percentage = round((active_users * 100 / total_users), 1) if total_users > 0 else 0

    context = {
        'users_with_stats': users_with_stats,
        'total_users': total_users,
        'total_students': total_students,
        'total_teachers': total_teachers,
        'total_admins': total_admins,
        'active_users': active_users,
        'new_users_this_month': new_users_this_month,
        'students_percentage': students_percentage,
        'active_percentage': active_percentage,
        'user_type_filter': user_type_filter,
        'search_query': search_query,
    }

    return render(request, 'admin/users.html', context)

@login_required
def admin_lessons(request):
    """صفحة إدارة الحصص مع البيانات الحقيقية والعلاقات المترابطة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.db.models import Q, Count, Avg
    from lessons.models import Lesson, LessonRating
    from courses.models import Enrollment, Course
    from django.utils import timezone

    # جلب جميع الحصص مع إحصائياتهم
    lessons = Lesson.objects.select_related(
        'enrollment__student',
        'enrollment__teacher',
        'enrollment__course'
    ).order_by('-scheduled_date')

    # فلترة حسب الحالة
    status_filter = request.GET.get('status')
    if status_filter:
        lessons = lessons.filter(status=status_filter)

    # فلترة حسب المعلم
    teacher_filter = request.GET.get('teacher')
    if teacher_filter:
        lessons = lessons.filter(enrollment__teacher_id=teacher_filter)

    # فلترة حسب الدورة
    course_filter = request.GET.get('course')
    if course_filter:
        lessons = lessons.filter(enrollment__course_id=course_filter)

    # فلترة حسب التاريخ
    date_filter = request.GET.get('date_filter')
    today = timezone.now().date()
    if date_filter == 'today':
        lessons = lessons.filter(scheduled_date__date=today)
    elif date_filter == 'week':
        week_start = today - timezone.timedelta(days=today.weekday())
        week_end = week_start + timezone.timedelta(days=6)
        lessons = lessons.filter(scheduled_date__date__range=[week_start, week_end])
    elif date_filter == 'month':
        lessons = lessons.filter(
            scheduled_date__year=today.year,
            scheduled_date__month=today.month
        )

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        lessons = lessons.filter(
            Q(title__icontains=search_query) |
            Q(enrollment__student__first_name__icontains=search_query) |
            Q(enrollment__student__last_name__icontains=search_query) |
            Q(enrollment__teacher__first_name__icontains=search_query) |
            Q(enrollment__teacher__last_name__icontains=search_query) |
            Q(enrollment__course__title__icontains=search_query)
        )

    # إضافة إحصائيات لكل حصة
    lessons_with_stats = []
    for lesson in lessons:
        # التقييم إذا كان متاحاً
        rating = None
        if hasattr(lesson, 'rating'):
            rating = lesson.rating

        lesson_stats = {
            'lesson': lesson,
            'student': lesson.enrollment.student,
            'teacher': lesson.enrollment.teacher,
            'course': lesson.enrollment.course,
            'rating': rating,
            'status_display': lesson.get_status_display(),
            'duration_display': f"{lesson.duration_minutes} دقيقة"
        }

        lessons_with_stats.append(lesson_stats)

    # إحصائيات عامة
    total_lessons = Lesson.objects.count()
    today_lessons = Lesson.objects.filter(scheduled_date__date=today).count()
    completed_lessons = Lesson.objects.filter(status='completed').count()
    cancelled_lessons = Lesson.objects.filter(status='cancelled').count()
    upcoming_lessons = Lesson.objects.filter(
        scheduled_date__gt=timezone.now(),
        status='scheduled'
    ).count()

    # إحصائيات الحصص حسب الحالة
    status_stats = {}
    for status_code, status_name in Lesson.STATUS_CHOICES:
        count = Lesson.objects.filter(status=status_code).count()
        status_stats[status_code] = {
            'name': status_name,
            'count': count
        }

    # المعلمين المتاحين
    available_teachers = User.objects.filter(
        user_type='teacher',
        is_active=True,
        is_active_teacher=True
    ).order_by('first_name')

    # الدورات المتاحة
    available_courses = Course.objects.filter(
        is_active=True
    ).order_by('title')

    # متوسط التقييمات
    avg_rating = LessonRating.objects.aggregate(
        avg=Avg('teacher_rating')
    )['avg'] or 0

    context = {
        'lessons_with_stats': lessons_with_stats,
        'total_lessons': total_lessons,
        'today_lessons': today_lessons,
        'completed_lessons': completed_lessons,
        'cancelled_lessons': cancelled_lessons,
        'upcoming_lessons': upcoming_lessons,
        'status_stats': status_stats,
        'available_teachers': available_teachers,
        'available_courses': available_courses,
        'avg_rating': round(avg_rating, 1),
        'status_filter': status_filter,
        'teacher_filter': teacher_filter,
        'course_filter': course_filter,
        'date_filter': date_filter,
        'search_query': search_query,
        'lesson_statuses': Lesson.STATUS_CHOICES,
    }

    return render(request, 'admin/lessons.html', context)

@login_required
def admin_support(request):
    """صفحة إدارة الدعم الفني"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    return render(request, 'common/support.html')

@login_required
def teacher_schedule(request):
    """صفحة جدول المعلم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    return render(request, 'teacher/schedule.html')

@login_required
def student_lessons(request):
    """صفحة حصص الطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    return render(request, 'student/lessons.html')

@login_required
def student_archive(request):
    """صفحة أرشيف حصص الطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')
    return render(request, 'student/archive.html')

@login_required
def student_subscriptions(request):
    """صفحة إدارة اشتراكات الطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from courses.models import PaymentRequest, PaymentProof, StudentSubscription
    from django.utils import timezone
    from django.db.models import Q

    # جلب طلبات الدفع للطالب
    payment_requests = PaymentRequest.objects.filter(
        student=request.user
    ).select_related('subscription_plan', 'discount', 'created_by').order_by('-created_at')

    # جلب إثباتات الدفع المرفوعة
    payment_proofs = PaymentProof.objects.filter(
        payment_request__student=request.user
    ).select_related('payment_request__subscription_plan').order_by('-uploaded_at')

    # جلب الاشتراكات النشطة
    active_subscriptions = StudentSubscription.objects.filter(
        student=request.user,
        status='active'
    ).select_related('subscription_plan', 'payment_request').order_by('-created_at')

    # جلب الاشتراكات المنتهية
    expired_subscriptions = StudentSubscription.objects.filter(
        student=request.user,
        status__in=['expired', 'cancelled']
    ).select_related('subscription_plan', 'payment_request').order_by('-created_at')

    # إحصائيات
    pending_requests = payment_requests.filter(status='pending').count()
    paid_requests = payment_requests.filter(status='paid').count()
    confirmed_requests = payment_requests.filter(status='confirmed').count()
    total_spent = sum([req.final_amount for req in payment_requests.filter(status='confirmed')])

    # معالجة رفع إثبات الدفع
    if request.method == 'POST' and 'upload_proof' in request.POST:
        request_id = request.POST.get('request_id')
        payment_method = request.POST.get('payment_method')
        transaction_id = request.POST.get('transaction_id', '')
        notes = request.POST.get('notes', '')
        proof_image = request.FILES.get('proof_image')

        try:
            payment_request = PaymentRequest.objects.get(
                id=request_id,
                student=request.user,
                status='pending'
            )

            # إنشاء إثبات الدفع
            proof = PaymentProof.objects.create(
                payment_request=payment_request,
                proof_image=proof_image,
                payment_method=payment_method,
                transaction_id=transaction_id,
                notes=notes
            )

            # تحديث حالة طلب الدفع
            payment_request.status = 'paid'
            payment_request.save()

            # إرسال إشعار للمدير
            proof.send_admin_notification()

            messages.success(request, 'تم رفع إثبات الدفع بنجاح! سيتم مراجعته من قبل الإدارة.')
            return redirect('student_subscriptions')

        except PaymentRequest.DoesNotExist:
            messages.error(request, 'طلب الدفع غير موجود أو غير صالح.')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء رفع إثبات الدفع: {str(e)}')

    context = {
        'payment_requests': payment_requests,
        'payment_proofs': payment_proofs,
        'active_subscriptions': active_subscriptions,
        'expired_subscriptions': expired_subscriptions,
        'pending_requests': pending_requests,
        'paid_requests': paid_requests,
        'confirmed_requests': confirmed_requests,
        'total_spent': total_spent,
    }

    return render(request, 'student/subscriptions.html', context)

@login_required
def teacher_payouts(request):
    """صفحة طلبات السحب للمعلم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from courses.models import TeacherPayoutRequest, AdminPayoutProof, TeacherPayout, TeacherEarning
    from django.utils import timezone
    from datetime import datetime

    # جلب طلبات السحب للمعلم
    payout_requests = TeacherPayoutRequest.objects.filter(
        teacher=request.user
    ).order_by('-created_at')

    # تحديد جميع الطلبات كمشاهدة عند زيارة الصفحة
    TeacherPayoutRequest.objects.filter(
        teacher=request.user,
        teacher_viewed_update=False
    ).update(teacher_viewed_update=True)

    # جلب إثباتات الدفع
    payout_proofs = AdminPayoutProof.objects.filter(
        payout_request__teacher=request.user
    ).select_related('payout_request', 'uploaded_by').order_by('-uploaded_at')

    # جلب الدفعات المكتملة
    completed_payouts = TeacherPayout.objects.filter(
        teacher=request.user
    ).select_related('payout_request', 'processed_by').order_by('-completed_at')

    # حساب الأرباح المتاحة للشهر الحالي
    current_month = timezone.now().month
    current_year = timezone.now().year

    # حساب الأرباح المتاحة باستخدام النظام الجديد
    earnings_data = TeacherEarning.get_available_earnings(
        teacher=request.user,
        month=current_month,
        year=current_year
    )

    total_earnings = earnings_data['total']
    available_earnings = earnings_data['available']
    reserved_earnings = earnings_data['reserved']
    withdrawn_earnings = earnings_data['withdrawn']

    # معالجة طلب سحب جديد
    if request.method == 'POST' and 'create_payout_request' in request.POST:
            try:
                from decimal import Decimal
                amount = Decimal(str(request.POST.get('amount', 0)))
                payment_method = request.POST.get('payment_method')
                bank_details = request.POST.get('bank_details', '')
                notes = request.POST.get('notes', '')

                # إعادة حساب الأرباح المتاحة للتأكد
                current_earnings_data = TeacherEarning.get_available_earnings(
                    teacher=request.user,
                    month=current_month,
                    year=current_year
                )
                current_available = current_earnings_data['available']

                if amount <= 0:
                    messages.error(request, 'يجب أن يكون المبلغ أكبر من صفر')
                elif amount > current_available:
                    messages.error(request, f'المبلغ المطلوب (${amount}) أكبر من الأرباح المتاحة (${current_available})')
                elif not payment_method:
                    messages.error(request, 'يرجى اختيار طريقة الدفع')
                elif not bank_details.strip():
                    messages.error(request, 'يرجى إدخال تفاصيل البنك أو الحساب')
                else:
                    # إنشاء طلب السحب (سيتم حجز الأرباح تلقائياً)
                    payout_request = TeacherPayoutRequest.objects.create(
                        teacher=request.user,
                        amount=amount,
                        available_earnings=current_earnings_data['total'],
                        month=current_month,
                        year=current_year,
                        payment_method=payment_method,
                        bank_details=bank_details,
                        notes=notes
                    )

                    messages.success(request, f'تم إرسال طلب السحب بمبلغ ${amount} بنجاح! تم حجز المبلغ من أرباحك وسيتم مراجعته من قبل الإدارة.')
                    return redirect('teacher_payouts')

            except ValueError as e:
                messages.error(request, 'يرجى إدخال مبلغ صحيح')
            except Exception as e:
                messages.error(request, f'حدث خطأ: {str(e)}')

    # الإحصائيات
    total_requests = payout_requests.count()
    pending_requests = payout_requests.filter(status='pending').count()
    approved_requests = payout_requests.filter(status='approved').count()
    completed_requests = payout_requests.filter(status='paid').count()
    total_received = sum([req.amount for req in payout_requests.filter(status='paid')])

    context = {
        'payout_requests': payout_requests,
        'payout_proofs': payout_proofs,
        'completed_payouts': completed_payouts,
        'total_earnings': total_earnings,
        'available_earnings': earnings_data['available'],
        'reserved_earnings': earnings_data['reserved'],
        'withdrawn_earnings': earnings_data['withdrawn'],
        'current_month': current_month,
        'current_year': current_year,
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'completed_requests': completed_requests,
        'total_received': total_received,
    }

    return render(request, 'teacher/payouts.html', context)

@login_required
def calendar_view(request):
    """صفحة التقويم"""
    return render(request, 'common/calendar.html')

@login_required
def notifications_redirect(request):
    """إعادة توجيه لنظام الإشعارات الجديد"""
    return redirect('/notifications/')


# ===============================
# Cross-Dashboard Integration Views
# ===============================

@login_required
def admin_user_detail(request, user_id):
    """عرض تفاصيل مستخدم مع جميع العلاقات المترابطة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.shortcuts import get_object_or_404
    from django.db.models import Count, Avg, Sum
    from courses.models import Enrollment, Course, TeacherRating, TeacherEarning
    from lessons.models import Lesson, LessonRating

    user = get_object_or_404(User, id=user_id)

    # إحصائيات حسب نوع المستخدم
    user_data = {
        'user': user,
        'enrollments': [],
        'lessons': [],
        'ratings': [],
        'earnings': [],
        'stats': {}
    }

    if user.user_type == 'student':
        # بيانات الطالب
        enrollments = Enrollment.objects.filter(student=user).select_related('course', 'teacher')
        lessons = Lesson.objects.filter(enrollment__student=user).select_related('enrollment__course', 'enrollment__teacher')
        ratings_given = LessonRating.objects.filter(student=user).select_related('lesson')

        user_data.update({
            'enrollments': enrollments,
            'lessons': lessons,
            'ratings': ratings_given,
            'stats': {
                'total_enrollments': enrollments.count(),
                'active_enrollments': enrollments.filter(status='active').count(),
                'completed_lessons': lessons.filter(status='completed').count(),
                'total_lessons': lessons.count(),
                'avg_rating_given': ratings_given.aggregate(avg=Avg('teacher_rating'))['avg'] or 0,
            }
        })

    elif user.user_type == 'teacher':
        # بيانات المعلم
        enrollments = Enrollment.objects.filter(teacher=user).select_related('course', 'student')
        lessons = Lesson.objects.filter(enrollment__teacher=user).select_related('enrollment__course', 'enrollment__student')
        ratings_received = LessonRating.objects.filter(lesson__enrollment__teacher=user).select_related('lesson', 'student')
        earnings = TeacherEarning.objects.filter(teacher=user)

        # تقييم المعلم الإجمالي
        teacher_rating = TeacherRating.objects.filter(teacher=user).first()

        user_data.update({
            'enrollments': enrollments,
            'lessons': lessons,
            'ratings': ratings_received,
            'earnings': earnings,
            'teacher_rating': teacher_rating,
            'stats': {
                'total_students': enrollments.values('student').distinct().count(),
                'active_enrollments': enrollments.filter(status='active').count(),
                'completed_lessons': lessons.filter(status='completed').count(),
                'total_lessons': lessons.count(),
                'avg_rating_received': ratings_received.aggregate(avg=Avg('teacher_rating'))['avg'] or 0,
                'total_earnings': earnings.aggregate(total=Sum('amount'))['total'] or 0,
                'this_month_earnings': earnings.filter(
                    month=timezone.now().month,
                    year=timezone.now().year
                ).aggregate(total=Sum('amount'))['total'] or 0,
            }
        })

    return render(request, 'admin/user_detail.html', user_data)


@login_required
def admin_create_enrollment(request):
    """إنشاء تسجيل جديد - ربط طالب بدورة ومعلم"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.http import JsonResponse
    from courses.models import Course, Enrollment
    from courses.utils import create_enrollment

    if request.method == 'POST':
        student_id = request.POST.get('student_id')
        course_id = request.POST.get('course_id')
        teacher_id = request.POST.get('teacher_id')
        start_date = request.POST.get('start_date')

        try:
            student = User.objects.get(id=student_id, user_type='student')
            course = Course.objects.get(id=course_id)
            teacher = User.objects.get(id=teacher_id, user_type='teacher')

            enrollment, error = create_enrollment(
                student=student,
                course=course,
                teacher=teacher,
                start_date=start_date,
                admin_user=request.user
            )

            if enrollment:
                messages.success(request, f"تم تسجيل {student.get_full_name()} في دورة {course.title} بنجاح.")
                return redirect('admin_courses')
            else:
                messages.error(request, error)

        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

    # جلب البيانات للنموذج
    students = User.objects.filter(user_type='student', is_active=True).order_by('first_name', 'last_name')
    courses = Course.objects.filter(is_active=True).order_by('title')
    teachers = User.objects.filter(user_type='teacher', is_active=True, is_active_teacher=True).order_by('first_name', 'last_name')

    # Pre-selection من URL parameters
    selected_student = request.GET.get('student')
    selected_teacher = request.GET.get('teacher')
    selected_course = request.GET.get('course')

    context = {
        'students': students,
        'courses': courses,
        'teachers': teachers,
        'selected_student': selected_student,
        'selected_teacher': selected_teacher,
        'selected_course': selected_course,
        'today': timezone.now().date(),
    }

    return render(request, 'admin/create_enrollment.html', context)


@login_required
def admin_schedule_lesson(request):
    """جدولة حصة جديدة - ربط التسجيل بحصة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from courses.models import Enrollment
    from lessons.models import Lesson
    from django.utils import timezone

    if request.method == 'POST':
        enrollment_id = request.POST.get('enrollment_id')
        title = request.POST.get('title')
        description = request.POST.get('description')
        scheduled_date = request.POST.get('scheduled_date')
        duration_minutes = request.POST.get('duration_minutes')

        try:
            enrollment = Enrollment.objects.get(id=enrollment_id, status='active')

            lesson = Lesson.objects.create(
                enrollment=enrollment,
                title=title,
                description=description,
                scheduled_date=scheduled_date,
                duration_minutes=int(duration_minutes),
                status='scheduled',
                created_by=request.user
            )

            messages.success(request, f"تم جدولة الحصة '{title}' بنجاح.")
            return redirect('admin_lessons')

        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

    # جلب التسجيلات النشطة
    active_enrollments = Enrollment.objects.filter(
        status='active'
    ).select_related('student', 'teacher', 'course')

    context = {
        'active_enrollments': active_enrollments,
        'duration_choices': [(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')],
    }

    return render(request, 'admin/schedule_lesson.html', context)


@login_required
def admin_create_multiple_enrollments(request):
    """تسجيل عدة طلاب في دورة واحدة مع معلم واحد"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from courses.models import Course, Enrollment
    from courses.utils import create_enrollment
    from django.db import transaction

    if request.method == 'POST':
        course_id = request.POST.get('course_id')
        teacher_id = request.POST.get('teacher_id')
        student_ids = request.POST.getlist('student_ids')
        start_date = request.POST.get('start_date')

        if not student_ids:
            messages.error(request, "يرجى اختيار طالب واحد على الأقل.")
            return redirect('admin_create_enrollment')

        try:
            course = Course.objects.get(id=course_id)
            teacher = User.objects.get(id=teacher_id, user_type='teacher')

            successful_enrollments = []
            failed_enrollments = []

            # استخدام transaction لضمان تسجيل جميع الطلاب أو عدم تسجيل أي منهم
            with transaction.atomic():
                for student_id in student_ids:
                    try:
                        student = User.objects.get(id=student_id, user_type='student')

                        # التحقق من عدم وجود تسجيل مسبق
                        existing_enrollment = Enrollment.objects.filter(
                            student=student,
                            course=course,
                            status__in=['active', 'pending']
                        ).first()

                        if existing_enrollment:
                            failed_enrollments.append({
                                'student': student,
                                'reason': 'مسجل بالفعل في هذه الدورة'
                            })
                            continue

                        enrollment, error = create_enrollment(
                            student=student,
                            course=course,
                            teacher=teacher,
                            start_date=start_date,
                            admin_user=request.user
                        )

                        if enrollment:
                            successful_enrollments.append({
                                'student': student,
                                'enrollment': enrollment
                            })
                        else:
                            failed_enrollments.append({
                                'student': student,
                                'reason': error
                            })

                    except User.DoesNotExist:
                        failed_enrollments.append({
                            'student': f"طالب برقم {student_id}",
                            'reason': 'الطالب غير موجود'
                        })

            # إعداد رسائل النتائج
            if successful_enrollments:
                success_names = [enr['student'].get_full_name() for enr in successful_enrollments]
                messages.success(
                    request,
                    f"تم تسجيل {len(successful_enrollments)} طالب بنجاح في دورة {course.title}: {', '.join(success_names)}"
                )

            if failed_enrollments:
                for failed in failed_enrollments:
                    student_name = failed['student'].get_full_name() if hasattr(failed['student'], 'get_full_name') else failed['student']
                    messages.warning(request, f"فشل تسجيل {student_name}: {failed['reason']}")

            return redirect('admin_courses')

        except Course.DoesNotExist:
            messages.error(request, "الدورة غير موجودة.")
        except User.DoesNotExist:
            messages.error(request, "المعلم غير موجود.")
        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

    return redirect('admin_create_enrollment')


@login_required
def admin_enrollment_detail(request, enrollment_id):
    """عرض تفاصيل التسجيل مع إمكانية التعديل"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.shortcuts import get_object_or_404
    from courses.models import Enrollment
    from lessons.models import Lesson
    from django.db.models import Count, Avg

    enrollment = get_object_or_404(Enrollment, id=enrollment_id)

    # إحصائيات التسجيل
    lessons = Lesson.objects.filter(enrollment=enrollment)
    lessons_stats = {
        'total': lessons.count(),
        'completed': lessons.filter(status='completed').count(),
        'scheduled': lessons.filter(status='scheduled').count(),
        'cancelled': lessons.filter(status='cancelled').count(),
    }

    # التقييمات
    from lessons.models import LessonRating
    ratings = LessonRating.objects.filter(lesson__enrollment=enrollment)
    avg_rating = ratings.aggregate(avg=Avg('teacher_rating'))['avg'] or 0

    # معالجة تحديث حالة التسجيل
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'update_status':
            new_status = request.POST.get('status')
            if new_status in dict(Enrollment.STATUS_CHOICES):
                enrollment.status = new_status
                enrollment.save()
                messages.success(request, f"تم تحديث حالة التسجيل إلى {enrollment.get_status_display()}")

        elif action == 'update_teacher':
            teacher_id = request.POST.get('teacher_id')
            try:
                new_teacher = User.objects.get(id=teacher_id, user_type='teacher')
                enrollment.teacher = new_teacher
                enrollment.save()
                messages.success(request, f"تم تغيير المعلم إلى {new_teacher.get_full_name()}")
            except User.DoesNotExist:
                messages.error(request, "المعلم غير موجود")

        return redirect('admin_enrollment_detail', enrollment_id=enrollment.id)

    # المعلمين المتاحين للتغيير
    available_teachers = User.objects.filter(
        user_type='teacher',
        is_active=True,
        is_active_teacher=True
    ).exclude(id=enrollment.teacher.id)

    context = {
        'enrollment': enrollment,
        'lessons': lessons.order_by('-scheduled_date'),
        'lessons_stats': lessons_stats,
        'avg_rating': round(avg_rating, 1),
        'available_teachers': available_teachers,
        'status_choices': Enrollment.STATUS_CHOICES,
    }

    return render(request, 'admin/enrollment_detail.html', context)


@login_required
def messages_view(request):
    """صفحة الرسائل"""
    return render(request, 'common/messages.html')

@login_required
def support_view(request):
    """صفحة الدعم الفني"""
    return render(request, 'common/support.html')

def about(request):
    """صفحة حول الموقع"""
    return render(request, 'static_pages/about.html')

def privacy(request):
    """صفحة سياسة الخصوصية"""
    return render(request, 'static_pages/privacy.html')

def terms(request):
    """صفحة شروط الاستخدام"""
    return render(request, 'static_pages/terms.html')

@login_required
def user_profile(request):
    return render(request, 'users/profile.html')

def register(request):
    """صفحة تسجيل حساب جديد مع نظام التحقق"""

    if request.method == 'POST':
        # جلب البيانات من النموذج
        user_type = request.POST.get('user_type')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        username = request.POST.get('username')
        email = request.POST.get('email')
        phone = request.POST.get('phone')
        password1 = request.POST.get('password1')
        password2 = request.POST.get('password2')
        terms = request.POST.get('terms')
        student_level = request.POST.get('student_level')

        # التحقق من البيانات
        errors = []

        # التحقق من الحقول المطلوبة
        if not all([user_type, first_name, last_name, username, email, password1, password2]):
            errors.append("جميع الحقول مطلوبة")

        # التحقق من نوع المستخدم
        if user_type not in ['student', 'teacher']:
            errors.append("نوع المستخدم غير صحيح")

        # التحقق من تطابق كلمات المرور
        if password1 != password2:
            errors.append("كلمات المرور غير متطابقة")

        # التحقق من طول كلمة المرور
        if len(password1) < 8:
            errors.append("كلمة المرور يجب أن تكون 8 أحرف على الأقل")

        # التحقق من الموافقة على الشروط
        if not terms:
            errors.append("يجب الموافقة على شروط الاستخدام")

        # التحقق من عدم وجود اسم المستخدم (مع استثناء المرفوضين غير المحظورين)
        existing_user_by_username = User.objects.filter(username=username).first()
        if existing_user_by_username:
            if not existing_user_by_username.can_register_again():
                if existing_user_by_username.is_currently_banned():
                    errors.append(f"اسم المستخدم محظور. {existing_user_by_username.get_ban_status_display()}")
                else:
                    errors.append("اسم المستخدم موجود بالفعل")

        # التحقق من عدم وجود البريد الإلكتروني (مع استثناء المرفوضين غير المحظورين)
        existing_user_by_email = User.objects.filter(email=email).first()
        if existing_user_by_email:
            if not existing_user_by_email.can_register_again():
                if existing_user_by_email.is_currently_banned():
                    errors.append(f"البريد الإلكتروني محظور. {existing_user_by_email.get_ban_status_display()}")
                else:
                    errors.append("البريد الإلكتروني مسجل بالفعل")

        if errors:
            for error in errors:
                messages.error(request, error)
            return render(request, 'auth/register.html', {
                'form_data': request.POST
            })

        try:
            # التحقق من وجود مستخدم مرفوض يمكن إعادة تسجيله
            existing_user = None
            if existing_user_by_username and existing_user_by_username.can_register_again():
                existing_user = existing_user_by_username
            elif existing_user_by_email and existing_user_by_email.can_register_again():
                existing_user = existing_user_by_email

            if existing_user:
                # إعادة تسجيل المستخدم المرفوض
                existing_user.first_name = first_name
                existing_user.last_name = last_name
                existing_user.username = username
                existing_user.email = email
                existing_user.phone = phone
                existing_user.user_type = user_type
                existing_user.set_password(password1)
                existing_user.verification_status = 'pending'
                existing_user.is_active = False
                existing_user.rejection_reason = None
                existing_user.verified_by = None
                existing_user.verified_at = None

                # تحديث مستوى الطالب إذا كان المستخدم طالباً
                if user_type == 'student' and student_level:
                    existing_user.student_level = student_level

                existing_user.save()
                user = existing_user

                messages.info(
                    request,
                    "تم تحديث طلبك السابق وإعادة إرساله للمراجعة."
                )
            else:
                # إنشاء المستخدم الجديد
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password1,
                    first_name=first_name,
                    last_name=last_name,
                    user_type=user_type,
                    phone=phone,
                    is_active=False,  # غير نشط حتى الموافقة
                    verification_status='pending'  # في انتظار المراجعة
                )

                # حفظ مستوى الطالب إذا كان المستخدم طالباً
                if user_type == 'student' and student_level:
                    user.student_level = student_level
                    user.save()

            # إشعار المديرين بالتسجيل الجديد
            _notify_admins_new_registration(user)

            # إرسال رسالة ترحيبية للمستخدم الجديد
            _send_welcome_message(user)

            # توجيه لصفحة نجاح التسجيل مع معرف المستخدم
            return redirect('registration_success', user_id=user.id)

        except Exception as e:
            messages.error(request, f"حدث خطأ أثناء إنشاء الحساب: {str(e)}")
            return render(request, 'auth/register.html', {
                'form_data': request.POST
            })

    return render(request, 'auth/register.html')


def registration_success(request, user_id):
    """صفحة نجاح التسجيل"""
    try:
        user = User.objects.get(id=user_id)

        # التأكد من أن المستخدم في حالة انتظار
        if user.verification_status != 'pending':
            return redirect('login')

        context = {
            'user': user,
            'user_type_display': 'معلم' if user.user_type == 'teacher' else 'طالب'
        }

        return render(request, 'auth/registration_success.html', context)

    except User.DoesNotExist:
        messages.error(request, "المستخدم غير موجود.")
        return redirect('register')


def _notify_admins_new_registration(user):
    """إشعار المديرين بتسجيل جديد"""
    try:
        from notifications.models import Notification
        admins = User.objects.filter(user_type='admin', is_active=True)

        user_type_ar = 'معلم' if user.user_type == 'teacher' else 'طالب'

        for admin in admins:
            Notification.objects.create(
                recipient=admin,
                title=f'طلب تسجيل {user_type_ar} جديد',
                message=f'تم تسجيل {user_type_ar} جديد: {user.get_full_name() or user.username} ({user.email}). يرجى مراجعة الطلب.',
                notification_type='user_registration',
                is_read=False
            )
    except Exception as e:
        print(f"خطأ في إرسال الإشعارات: {str(e)}")  # للتطوير فقط


def _send_welcome_message(user, is_approved=False):
    """إرسال رسالة ترحيبية للمستخدم الجديد

    Args:
        user: المستخدم المراد إرسال الرسالة له
        is_approved: هل تم الموافقة على الحساب بالفعل؟
    """
    try:
        from notifications.models import Message
        from django.contrib.auth import get_user_model

        User = get_user_model()

        # الحصول على مستخدم مدير لإرسال الرسالة منه
        admin_user = User.objects.filter(user_type='admin', is_active=True).first()

        if not admin_user:
            print("لا يوجد مدير نشط لإرسال رسالة الترحيب")
            return

        # الحصول على إعدادات الأكاديمية
        from users.models import AcademySettings
        academy_settings = AcademySettings.get_settings()
        academy_name = academy_settings.academy_name
        academy_slogan = academy_settings.academy_slogan
        support_email = academy_settings.academy_support_email

        # تحديد محتوى الرسالة حسب نوع المستخدم ووضع الحساب
        if user.user_type == 'teacher':
            if is_approved:
                subject = f"تهانينا! تم تفعيل حسابك في {academy_name}"
                content = f"""
                <p>السلام عليكم ورحمة الله وبركاته</p>
                <p>أهلاً بك {user.get_full_name() or user.username} في {academy_name}!</p>
                <p>يسرنا إعلامك بأنه تمت الموافقة على حسابك كمعلم في {academy_slogan}. يمكنك الآن الدخول إلى حسابك والاستفادة من جميع مميزات المنصة.</p>
                <p>يمكنك الآن:</p>
                <ul>
                    <li>إدارة الحصص الخاصة بك</li>
                    <li>متابعة تقدم طلابك</li>
                    <li>تسجيل الحضور والغياب</li>
                    <li>التواصل مع الطلاب</li>
                    <li>الاطلاع على جدولك الدراسي</li>
                </ul>
                <p>إذا كان لديك أي استفسار، يرجى التواصل مع الدعم الفني على البريد الإلكتروني: {support_email}</p>
                <p>مع أطيب التمنيات بالتوفيق،</p>
                <p>إدارة {academy_name}</p>
                """
            else:
                subject = f"مرحباً بك في {academy_name}"
                content = f"""
                <p>السلام عليكم ورحمة الله وبركاته</p>
                <p>أهلاً بك {user.get_full_name() or user.username} في {academy_name}!</p>
                <p>نحن سعداء بانضمامك إلى فريق المعلمين لدينا في {academy_slogan}. سيتم مراجعة طلبك من قبل إدارة النظام وسيتم إعلامك عند الموافقة عليه.</p>
                <p>بعد الموافقة على حسابك، ستتمكن من:</p>
                <ul>
                    <li>إدارة الحصص الخاصة بك</li>
                    <li>متابعة تقدم طلابك</li>
                    <li>تسجيل الحضور والغياب</li>
                    <li>التواصل مع الطلاب</li>
                    <li>الاطلاع على جدولك الدراسي</li>
                </ul>
                <p>إذا كان لديك أي استفسار، يرجى التواصل مع الدعم الفني على البريد الإلكتروني: {support_email}</p>
                <p>مع أطيب التمنيات بالتوفيق،</p>
                <p>إدارة {academy_name}</p>
                """
        else:  # طالب
            if is_approved:
                subject = f"تهانينا! تم تفعيل حسابك في {academy_name}"
                content = f"""
                <p>السلام عليكم ورحمة الله وبركاته</p>
                <p>أهلاً بك {user.get_full_name() or user.username} في {academy_name}!</p>
                <p>يسرنا إعلامك بأنه تمت الموافقة على حسابك كطالب في {academy_slogan}. يمكنك الآن الدخول إلى حسابك والاستفادة من جميع مميزات المنصة.</p>
                <p>يمكنك الآن:</p>
                <ul>
                    <li>حضور الحصص المجدولة</li>
                    <li>متابعة تقدمك الشخصي</li>
                    <li>الاطلاع على الدرجات والتقييمات</li>
                    <li>التواصل مع المعلمين</li>
                    <li>الوصول إلى أرشيف الحصص السابقة</li>
                </ul>
                <p>إذا كان لديك أي استفسار، يرجى التواصل مع الدعم الفني على البريد الإلكتروني: {support_email}</p>
                <p>مع أطيب التمنيات بالتوفيق،</p>
                <p>إدارة {academy_name}</p>
                """
            else:
                subject = f"مرحباً بك في {academy_name}"
                content = f"""
                <p>السلام عليكم ورحمة الله وبركاته</p>
                <p>أهلاً بك {user.get_full_name() or user.username} في {academy_name}!</p>
                <p>نحن سعداء بانضمامك إلينا في {academy_slogan}. سيتم مراجعة طلبك من قبل إدارة النظام وسيتم إعلامك عند الموافقة عليه.</p>
                <p>بعد الموافقة على حسابك، ستتمكن من:</p>
                <ul>
                    <li>حضور الحصص المجدولة</li>
                    <li>متابعة تقدمك الشخصي</li>
                    <li>الاطلاع على الدرجات والتقييمات</li>
                    <li>التواصل مع المعلمين</li>
                    <li>الوصول إلى أرشيف الحصص السابقة</li>
                </ul>
                <p>إذا كان لديك أي استفسار، يرجى التواصل مع الدعم الفني على البريد الإلكتروني: {support_email}</p>
                <p>مع أطيب التمنيات بالتوفيق،</p>
                <p>إدارة {academy_name}</p>
                """

        # إنشاء الرسالة
        message = Message.objects.create(
            sender=admin_user,
            recipient=user,
            subject=subject,
            content=content,
            message_type='welcome'
        )

        # إنشاء إشعار للمستخدم (سيظهر عند تفعيل الحساب)
        from notifications.utils import create_notification
        create_notification(
            recipient=user,
            notification_type='new_message',
            title=f'رسالة ترحيبية: {subject}',
            message='تم إرسال رسالة ترحيبية من إدارة النظام',
            sender=admin_user,
            priority='medium',
            action_url=f'/notifications/messages/{message.id}/',
            action_text='عرض الرسالة'
        )

        return message

    except Exception as e:
        print(f"خطأ في إرسال رسالة الترحيب: {str(e)}")  # للتطوير فقط
        return None


def verification_pending(request):
    """صفحة انتظار المراجعة - توجيه للصفحة العامة"""
    # حفظ معلومات المستخدم في الجلسة إذا كان مسجل دخول
    if request.user.is_authenticated:
        request.session['pending_username'] = request.user.username
        request.session['pending_user_type'] = request.user.user_type

    # توجيه للصفحة العامة
    return redirect('public_pending_page')


def verification_rejected(request):
    """صفحة رفض التحقق - توجيه للصفحة العامة"""
    # حفظ معلومات المستخدم في الجلسة إذا كان مسجل دخول
    if request.user.is_authenticated:
        request.session['rejected_username'] = request.user.username
        request.session['rejected_user_type'] = request.user.user_type
        request.session['rejection_reason'] = request.user.rejection_reason

    # توجيه للصفحة العامة
    return redirect('public_rejected_page')


def user_banned(request):
    """صفحة الحظر - توجيه للصفحة العامة"""
    # حفظ معلومات المستخدم في الجلسة إذا كان مسجل دخول
    if request.user.is_authenticated:
        user = request.user
        request.session['banned_username'] = user.username
        request.session['ban_reason'] = user.ban_reason
        request.session['ban_type'] = user.ban_type
        if user.banned_until:
            request.session['banned_until'] = user.banned_until.strftime('%Y-%m-%d %H:%M')

    # توجيه للصفحة العامة
    return redirect('public_banned_page')


@login_required
def admin_user_verifications(request):
    """لوحة إدارة طلبات التحقق"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.db.models import Q

    # فلترة حسب الحالة
    status_filter = request.GET.get('status', 'pending')
    user_type_filter = request.GET.get('user_type', '')
    search_query = request.GET.get('search', '')
    ban_status_filter = request.GET.get('ban_status', '')
    registration_period_filter = request.GET.get('registration_period', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # جلب المستخدمين حسب الفلاتر
    users = User.objects.exclude(user_type='admin').order_by('-created_at')

    # فلترة حسب حالة التحقق
    if status_filter:
        users = users.filter(verification_status=status_filter)

    # فلترة حسب نوع المستخدم
    if user_type_filter:
        users = users.filter(user_type=user_type_filter)

    # فلترة حسب حالة الحظر
    if ban_status_filter:
        if ban_status_filter == 'not_banned':
            users = users.filter(is_banned=False)
        elif ban_status_filter == 'banned':
            users = users.filter(is_banned=True)
        elif ban_status_filter == 'temporary':
            users = users.filter(is_banned=True, ban_type='temporary')
        elif ban_status_filter == 'permanent':
            users = users.filter(is_banned=True, ban_type='permanent')

    # فلترة حسب فترة التسجيل
    from django.utils import timezone
    from datetime import timedelta

    if registration_period_filter:
        now = timezone.now()
        if registration_period_filter == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            users = users.filter(created_at__gte=start_date)
        elif registration_period_filter == 'week':
            start_date = now - timedelta(days=now.weekday())
            start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            users = users.filter(created_at__gte=start_date)
        elif registration_period_filter == 'month':
            start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            users = users.filter(created_at__gte=start_date)
        elif registration_period_filter == 'year':
            start_date = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            users = users.filter(created_at__gte=start_date)

    # فلترة حسب تاريخ التسجيل
    from datetime import datetime

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
            users = users.filter(created_at__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').replace(hour=23, minute=59, second=59, tzinfo=timezone.get_current_timezone())
            users = users.filter(created_at__lte=date_to_obj)
        except ValueError:
            pass

    # فلترة حسب البحث
    if search_query:
        users = users.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(phone__icontains=search_query)
        )

    # إحصائيات
    stats = {
        # إحصائيات حالة التحقق
        'pending': User.objects.filter(verification_status='pending').exclude(user_type='admin').count(),
        'approved': User.objects.filter(verification_status='approved').exclude(user_type='admin').count(),
        'rejected': User.objects.filter(verification_status='rejected').exclude(user_type='admin').count(),
        'under_review': User.objects.filter(verification_status='under_review').exclude(user_type='admin').count(),

        # إحصائيات حالة الحظر
        'temporary_banned': User.objects.filter(is_banned=True, ban_type='temporary').exclude(user_type='admin').count(),
        'permanent_banned': User.objects.filter(is_banned=True, ban_type='permanent').exclude(user_type='admin').count(),
    }

    # معالجة الإجراءات
    if request.method == 'POST':
        action = request.POST.get('action')

        # معالجة الإجراءات الجماعية
        if action.startswith('bulk_'):
            user_ids = request.POST.get('user_ids', '').split(',')
            user_ids = [uid.strip() for uid in user_ids if uid.strip()]

            if not user_ids:
                messages.error(request, "لم يتم تحديد أي حسابات.")
                return redirect('admin_user_verifications')

            users = User.objects.filter(id__in=user_ids)

            if action == 'bulk_approve':
                notes = request.POST.get('notes', '')
                success_count = 0
                for user in users:
                    try:
                        user.approve_verification(request.user, notes)
                        success_count += 1
                    except Exception as e:
                        messages.warning(request, f"فشل في الموافقة على حساب {user.get_full_name()}: {str(e)}")

                if success_count > 0:
                    messages.success(request, f"تم قبول {success_count} حسابات بنجاح.")

            elif action == 'bulk_reject':
                reason = request.POST.get('reason', '')
                notes = request.POST.get('notes', '')
                if not reason:
                    messages.error(request, "يرجى إدخال سبب الرفض.")
                else:
                    success_count = 0
                    for user in users:
                        try:
                            user.reject_verification(request.user, reason, notes)
                            success_count += 1
                        except Exception as e:
                            messages.warning(request, f"فشل في رفض حساب {user.get_full_name()}: {str(e)}")

                    if success_count > 0:
                        messages.success(request, f"تم رفض {success_count} حسابات.")

            elif action == 'bulk_under_review':
                success_count = 0
                for user in users:
                    try:
                        user.verification_status = 'under_review'
                        user.save()
                        success_count += 1
                    except Exception as e:
                        messages.warning(request, f"فشل في وضع حساب {user.get_full_name()} قيد المراجعة: {str(e)}")

                if success_count > 0:
                    messages.info(request, f"تم وضع {success_count} حسابات قيد المراجعة.")

            elif action == 'bulk_ban':
                ban_type = request.POST.get('ban_type', '')
                ban_reason = request.POST.get('ban_reason', '')

                if not ban_reason:
                    messages.error(request, "يرجى إدخال سبب الحظر.")
                else:
                    banned_until = None
                    if ban_type == 'temporary':
                        ban_duration = request.POST.get('ban_duration', '')
                        ban_unit = request.POST.get('ban_unit', 'days')

                        if not ban_duration:
                            messages.error(request, "يرجى إدخال مدة الحظر.")
                            return redirect('admin_user_verifications')

                        try:
                            from django.utils import timezone
                            from datetime import timedelta

                            duration = int(ban_duration)
                            if ban_unit == 'hours':
                                banned_until = timezone.now() + timedelta(hours=duration)
                            elif ban_unit == 'days':
                                banned_until = timezone.now() + timedelta(days=duration)
                            elif ban_unit == 'weeks':
                                banned_until = timezone.now() + timedelta(weeks=duration)
                            elif ban_unit == 'months':
                                banned_until = timezone.now() + timedelta(days=duration * 30)
                            else:
                                banned_until = timezone.now() + timedelta(days=duration)

                        except ValueError:
                            messages.error(request, "مدة الحظر يجب أن تكون رقماً صحيحاً.")
                            return redirect('admin_user_verifications')

                    success_count = 0
                    for user in users:
                        try:
                            user.ban_user(request.user, ban_type, ban_reason, banned_until)
                            success_count += 1
                        except Exception as e:
                            messages.warning(request, f"فشل في حظر حساب {user.get_full_name()}: {str(e)}")

                    if success_count > 0:
                        ban_type_text = "مؤقتاً" if ban_type == 'temporary' else "نهائياً"
                        messages.success(request, f"تم حظر {success_count} حسابات {ban_type_text}.")

            elif action == 'bulk_delete':
                delete_reason = request.POST.get('delete_reason', '')
                confirm_delete = request.POST.get('confirm_delete', '')

                if not delete_reason or not confirm_delete:
                    messages.error(request, 'يرجى تحديد سبب الحذف وتأكيد العملية')
                else:
                    success_count = 0
                    for user in users:
                        try:
                            # حفظ معلومات الحذف في سجل النظام
                            from django.utils import timezone

                            user_info = {
                                'id': user.id,
                                'username': user.username,
                                'email': user.email,
                                'full_name': user.get_full_name(),
                                'user_type': user.user_type,
                                'verification_status': user.verification_status,
                                'is_active': user.is_active,
                                'is_banned': user.is_banned,
                                'deleted_at': timezone.now().isoformat(),
                                'deleted_by': request.user.username,
                                'delete_reason': delete_reason
                            }

                            # تسجيل الحدث في سجل النظام
                            from logs.models import SystemLog
                            SystemLog.objects.create(
                                user=request.user,
                                action='delete_user',
                                details=f'تم حذف المستخدم {user.get_full_name() or user.username} (ID: {user.id}) بواسطة {request.user.get_full_name() or request.user.username}',
                                data=user_info,
                                ip_address=request.META.get('REMOTE_ADDR')
                            )

                            # حذف المستخدم
                            user.delete()
                            success_count += 1
                        except Exception as e:
                            messages.warning(request, f"فشل في حذف حساب {user.get_full_name()}: {str(e)}")

                    if success_count > 0:
                        messages.success(request, f'تم حذف {success_count} حسابات بنجاح')

            return redirect('admin_user_verifications')

        # معالجة الإجراءات الفردية
        user_id = request.POST.get('user_id')

        try:
            user = User.objects.get(id=user_id)

            if action == 'approve':
                notes = request.POST.get('notes', '')
                user.approve_verification(request.user, notes)
                messages.success(request, f"تم قبول طلب {user.get_full_name()} بنجاح.")

            elif action == 'reject':
                reason = request.POST.get('reason', '')
                notes = request.POST.get('notes', '')
                if not reason:
                    messages.error(request, "يرجى إدخال سبب الرفض.")
                else:
                    user.reject_verification(request.user, reason, notes)
                    messages.success(request, f"تم رفض طلب {user.get_full_name()}.")

            elif action == 'under_review':
                user.verification_status = 'under_review'
                user.save()
                messages.info(request, f"تم وضع طلب {user.get_full_name()} قيد المراجعة.")

            elif action == 'ban_temporary':
                ban_reason = request.POST.get('ban_reason', '')
                ban_duration = request.POST.get('ban_duration', '')
                ban_unit = request.POST.get('ban_unit', 'days')

                if not ban_reason:
                    messages.error(request, "يرجى إدخال سبب الحظر.")
                elif not ban_duration:
                    messages.error(request, "يرجى إدخال مدة الحظر.")
                else:
                    try:
                        from django.utils import timezone
                        from datetime import timedelta

                        duration = int(ban_duration)
                        if ban_unit == 'hours':
                            banned_until = timezone.now() + timedelta(hours=duration)
                        elif ban_unit == 'days':
                            banned_until = timezone.now() + timedelta(days=duration)
                        elif ban_unit == 'weeks':
                            banned_until = timezone.now() + timedelta(weeks=duration)
                        elif ban_unit == 'months':
                            banned_until = timezone.now() + timedelta(days=duration * 30)
                        else:
                            banned_until = timezone.now() + timedelta(days=duration)

                        user.ban_user(request.user, 'temporary', ban_reason, banned_until)
                        messages.success(request, f"تم حظر {user.get_full_name()} مؤقتاً حتى {banned_until.strftime('%Y-%m-%d %H:%M')}.")

                    except ValueError:
                        messages.error(request, "مدة الحظر يجب أن تكون رقماً صحيحاً.")
                    except Exception as e:
                        messages.error(request, f"حدث خطأ أثناء الحظر: {str(e)}")

            elif action == 'ban_permanent':
                ban_reason = request.POST.get('ban_reason', '')

                if not ban_reason:
                    messages.error(request, "يرجى إدخال سبب الحظر.")
                else:
                    user.ban_user(request.user, 'permanent', ban_reason)
                    messages.success(request, f"تم حظر {user.get_full_name()} نهائياً.")

            elif action == 'unban':
                user.unban_user()
                messages.success(request, f"تم إلغاء حظر {user.get_full_name()}.")

            elif action == 'delete_user':
                delete_reason = request.POST.get('delete_reason', '')
                confirm_delete = request.POST.get('confirm_delete', '')

                if delete_reason and confirm_delete:
                    # حفظ معلومات الحذف في سجل النظام
                    from django.utils import timezone

                    # تخزين معلومات المستخدم قبل الحذف
                    user_info = {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'full_name': user.get_full_name(),
                        'user_type': user.user_type,
                        'verification_status': user.verification_status,
                        'is_active': user.is_active,
                        'is_banned': user.is_banned,
                        'deleted_at': timezone.now().isoformat(),
                        'deleted_by': request.user.username,
                        'delete_reason': delete_reason
                    }

                    # تسجيل الحدث في سجل النظام
                    from logs.models import SystemLog
                    SystemLog.objects.create(
                        user=request.user,
                        action='delete_user',
                        details=f'تم حذف المستخدم {user.get_full_name() or user.username} (ID: {user.id}) بواسطة {request.user.get_full_name() or request.user.username}',
                        data=user_info,
                        ip_address=request.META.get('REMOTE_ADDR')
                    )

                    # حذف المستخدم
                    username = user.username
                    user.delete()
                    messages.success(request, f'تم حذف المستخدم {username} بنجاح')
                else:
                    messages.error(request, 'يرجى تحديد سبب الحذف وتأكيد العملية')

        except User.DoesNotExist:
            messages.error(request, "المستخدم غير موجود.")
        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

        return redirect('admin_user_verifications')

    context = {
        'users': users,
        'stats': stats,
        'status_filter': status_filter,
        'user_type_filter': user_type_filter,
        'search_query': search_query,
        'ban_status_filter': ban_status_filter,
        'registration_period_filter': registration_period_filter,
        'date_from': date_from,
        'date_to': date_to,
        'status_choices': User.VERIFICATION_STATUS_CHOICES,
        'user_type_choices': [('student', 'طالب'), ('teacher', 'معلم')],
    }

    return render(request, 'admin/user_verifications.html', context)


