# Generated by Django 4.2.7 on 2025-05-22 18:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('messaging', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='محتوى الرسالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='message_attachments/', verbose_name='مرفق')),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chat_messages', to='messaging.conversation', verbose_name='المحادثة')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_chat_messages', to=settings.AUTH_USER_MODEL, verbose_name='المرسل')),
            ],
            options={
                'verbose_name': 'رسالة',
                'verbose_name_plural': 'الرسائل',
                'ordering': ['created_at'],
            },
        ),
        migrations.DeleteModel(
            name='Message',
        ),
    ]
