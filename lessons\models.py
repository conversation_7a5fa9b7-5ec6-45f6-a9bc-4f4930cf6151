from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.utils import timezone
from courses.models import Enrollment
import uuid

User = get_user_model()


class Lesson(models.Model):
    """نموذج الحصة"""

    STATUS_CHOICES = (
        ('scheduled', _('مجدولة')),
        ('in_progress', _('جارية')),
        ('completed', _('مكتملة')),
        ('cancelled', _('ملغية')),
        ('missed', _('فائتة')),
    )

    enrollment = models.ForeignKey(
        Enrollment,
        on_delete=models.CASCADE,
        related_name='lessons',
        verbose_name=_('التسجيل')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الحصة')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف الحصة')
    )

    scheduled_date = models.DateTimeField(
        verbose_name=_('موعد الحصة')
    )

    duration_minutes = models.PositiveIntegerField(
        choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')],
        default=45,
        verbose_name=_('مدة الحصة (بالدقائق)')
    )

    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_('حالة الحصة')
    )

    jitsi_room_id = models.CharField(
        max_length=100,
        unique=True,
        blank=True,
        null=True,
        verbose_name=_('معرف غرفة Jitsi')
    )

    jitsi_password = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('كلمة مرور Jitsi')
    )

    actual_start_time = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت البداية الفعلي')
    )

    actual_end_time = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت النهاية الفعلي')
    )

    teacher_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات المعلم')
    )

    admin_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات الإدارة')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_lessons',
        verbose_name=_('منشئ الحصة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('حصة')
        verbose_name_plural = _('الحصص')
        ordering = ['scheduled_date']

    def __str__(self):
        return f"{self.title} - {self.scheduled_date.strftime('%Y-%m-%d %H:%M')}"

    def save(self, *args, **kwargs):
        """إنشاء معرف غرفة Jitsi تلقائياً وإرسال إشعارات"""
        is_new = self.pk is None

        if not self.jitsi_room_id:
            self.jitsi_room_id = f"qurania_{uuid.uuid4().hex[:8]}"

        super().save(*args, **kwargs)

        # إرسال إشعار عند إنشاء حصة جديدة
        if is_new:
            from notifications.utils import NotificationService
            NotificationService.notify_lesson_created(self)

    def get_jitsi_url(self):
        """إنشاء رابط غرفة Jitsi"""
        from django.conf import settings
        domain = getattr(settings, 'JITSI_DOMAIN', 'meet.jit.si')
        return f"https://{domain}/{self.jitsi_room_id}"

    def is_upcoming(self):
        """التحقق من كون الحصة قادمة"""
        return self.scheduled_date > timezone.now() and self.status == 'scheduled'

    def is_today(self):
        """التحقق من كون الحصة اليوم"""
        return self.scheduled_date.date() == timezone.now().date()

    def can_join(self):
        """التحقق من إمكانية الدخول للحصة"""
        now = timezone.now()
        # يمكن الدخول قبل 15 دقيقة من الموعد
        start_time = self.scheduled_date - timezone.timedelta(minutes=15)
        end_time = self.scheduled_date + timezone.timedelta(minutes=self.duration_minutes + 30)
        return start_time <= now <= end_time and self.status in ['scheduled', 'in_progress']


class LessonAttendance(models.Model):
    """نموذج حضور الحصة"""

    lesson = models.ForeignKey(
        Lesson,
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('الحصة')
    )

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lesson_attendance',
        verbose_name=_('المستخدم')
    )

    joined_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('وقت الدخول')
    )

    left_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت الخروج')
    )

    duration_minutes = models.PositiveIntegerField(
        default=0,
        verbose_name=_('مدة الحضور (بالدقائق)')
    )

    class Meta:
        verbose_name = _('حضور الحصة')
        verbose_name_plural = _('حضور الحصص')
        unique_together = ['lesson', 'user']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.lesson.title}"

    def calculate_duration(self):
        """حساب مدة الحضور"""
        if self.left_at:
            duration = self.left_at - self.joined_at
            self.duration_minutes = int(duration.total_seconds() / 60)
            self.save()


class LessonContent(models.Model):
    """نموذج محتوى الحصة"""

    CONTENT_TYPES = (
        ('memorization', _('حفظ')),
        ('review', _('مراجعة')),
        ('recitation', _('تلاوة')),
        ('correction', _('تصحيح')),
    )

    lesson = models.ForeignKey(
        Lesson,
        on_delete=models.CASCADE,
        related_name='content_records',
        verbose_name=_('الحصة')
    )

    content_type = models.CharField(
        max_length=15,
        choices=CONTENT_TYPES,
        verbose_name=_('نوع المحتوى')
    )

    surah_name = models.CharField(
        max_length=100,
        verbose_name=_('اسم السورة')
    )

    from_verse = models.PositiveIntegerField(
        verbose_name=_('من الآية')
    )

    to_verse = models.PositiveIntegerField(
        verbose_name=_('إلى الآية')
    )

    quality_score = models.PositiveIntegerField(
        choices=[(i, f"{i}/10") for i in range(1, 11)],
        blank=True,
        null=True,
        verbose_name=_('تقييم الجودة')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    recorded_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='recorded_lesson_content',
        verbose_name=_('مسجل بواسطة')
    )

    recorded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التسجيل')
    )

    class Meta:
        verbose_name = _('محتوى الحصة')
        verbose_name_plural = _('محتوى الحصص')
        ordering = ['-recorded_at']

    def __str__(self):
        return f"{self.lesson.title} - {self.surah_name} ({self.from_verse}-{self.to_verse})"


class LessonRating(models.Model):
    """نموذج تقييم الحصة"""

    lesson = models.OneToOneField(
        Lesson,
        on_delete=models.CASCADE,
        related_name='rating',
        verbose_name=_('الحصة')
    )

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lesson_ratings',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    teacher_rating = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('تقييم المعلم')
    )

    lesson_quality = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('جودة الحصة')
    )

    technical_quality = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('الجودة التقنية')
    )

    overall_satisfaction = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('الرضا العام')
    )

    comment = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('تعليق')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التقييم')
    )

    class Meta:
        verbose_name = _('تقييم الحصة')
        verbose_name_plural = _('تقييمات الحصص')

    def __str__(self):
        return f"تقييم {self.lesson.title} - {self.student.get_full_name()}"

    def get_average_rating(self):
        """حساب متوسط التقييم"""
        total = self.teacher_rating + self.lesson_quality + self.technical_quality + self.overall_satisfaction
        return round(total / 4, 1)

    def save(self, *args, **kwargs):
        """حفظ التقييم مع إرسال إشعارات"""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            # إرسال إشعار للمعلم
            from notifications.utils import NotificationService
            NotificationService.notify_lesson_rating(self)

            # تحديث تقييم المعلم الإجمالي
            from courses.models import TeacherRating
            teacher_rating, created = TeacherRating.objects.get_or_create(
                teacher=self.lesson.enrollment.teacher
            )
            teacher_rating.update_rating()
