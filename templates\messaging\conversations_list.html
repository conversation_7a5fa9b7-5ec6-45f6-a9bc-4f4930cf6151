{% extends 'base.html' %}
{% load static %}

{% block title %}الرسائل{% endblock %}

{% block extra_css %}
<style>
/* مؤشرات حالة المستخدمين */
.status-indicator {
    position: relative;
}

.status-indicator.green {
    background-color: #10b981; /* أخضر - متصل */
    box-shadow: 0 0 0 2px #10b981;
}

.status-indicator.yellow {
    background-color: #f59e0b; /* أصفر - متصل مؤخراً */
    box-shadow: 0 0 0 2px #f59e0b;
}

.status-indicator.gray {
    background-color: #6b7280; /* رمادي - غير متصل */
    box-shadow: 0 0 0 2px #6b7280;
}

/* نص حالة المستخدم */
.status-text-green {
    background-color: #d1fae5;
    color: #065f46;
}

.status-text-yellow {
    background-color: #fef3c7;
    color: #92400e;
}

.status-text-gray {
    background-color: #f3f4f6;
    color: #374151;
}

/* تأثير النبض للمستخدمين المتصلين */
.status-indicator.green::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background-color: #10b981;
    animation: pulse 2s infinite;
    opacity: 0.6;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.3;
    }
    100% {
        transform: scale(1);
        opacity: 0.6;
    }
}

/* تحسين عرض الإشعارات */
.alert {
    transition: opacity 0.3s ease;
}

.unread-count {
    transition: all 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-islamic-light-blue to-islamic-light-green py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-islamic-primary rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-comments text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">الرسائل</h1>
                        <p class="text-gray-600">تواصل مع
                            {% if user.user_type == 'student' %}المعلمين{% elif user.user_type == 'teacher' %}الطلاب{% else %}الجميع{% endif %}
                        </p>
                    </div>
                </div>

                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="{% url 'start_conversation' %}"
                       class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        محادثة جديدة
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-comments text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">{{ total_conversations }}</h3>
                        <p class="text-gray-600">إجمالي المحادثات</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-envelope text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">{{ unread_conversations }}</h3>
                        <p class="text-gray-600">محادثات غير مقروءة</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-users text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">
                            {% if user.user_type == 'student' %}المعلمين{% elif user.user_type == 'teacher' %}الطلاب{% else %}المستخدمين{% endif %}
                        </h3>
                        <p class="text-gray-600">المتاحين للتراسل</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conversations List -->
        {% if conversations %}
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">
                    <i class="fas fa-list text-islamic-primary ml-2"></i>
                    المحادثات
                </h2>
            </div>

            <div class="divide-y divide-gray-200">
                {% for conversation in conversations %}
                <div class="p-6 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center flex-1">
                            <!-- Avatar with Status -->
                            <div class="relative ml-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-islamic-primary to-islamic-secondary rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-lg">
                                        {{ conversation.get_other_participant.get_full_name|first }}
                                    </span>
                                </div>
                                <!-- Status Indicator -->
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white status-indicator {{ conversation.other_participant_status.status_color }}"
                                     data-user-status="{{ conversation.get_other_participant.id }}"
                                     title="{{ conversation.other_participant_status.status_display }}">
                                </div>
                            </div>

                            <!-- Conversation Info -->
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-bold text-gray-900">
                                        {{ conversation.get_other_participant.get_full_name }}
                                    </h3>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        {% if conversation.unread_count > 0 %}
                                        <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                                            {{ conversation.unread_count }}
                                        </span>
                                        {% endif %}
                                        <span class="text-sm text-gray-500">
                                            {% if conversation.last_message_time %}
                                                {{ conversation.last_message_time|timesince }} مضت
                                            {% else %}
                                                لا توجد رسائل
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>

                                <p class="text-gray-600 text-sm">
                                    <span class="text-xs bg-gray-100 px-2 py-1 rounded ml-2">
                                        {{ conversation.get_other_participant.get_user_type_display }}
                                    </span>
                                    <span class="text-xs px-2 py-1 rounded status-text-{{ conversation.other_participant_status.status_color }}">
                                        {{ conversation.other_participant_status.status_display }}
                                    </span>
                                </p>

                                {% with last_message=conversation.get_last_message %}
                                {% if last_message %}
                                <p class="text-gray-600 text-sm mt-2 truncate">
                                    <strong>{{ last_message.sender.get_full_name }}:</strong>
                                    {{ last_message.content|truncatechars:50 }}
                                </p>
                                {% endif %}
                                {% endwith %}
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <a href="{% url 'conversation_detail' conversation.id %}"
                               class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-secondary transition-colors">
                                <i class="fas fa-eye ml-1"></i>
                                عرض
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Pagination -->
        {% if conversations.has_other_pages %}
        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if conversations.has_previous %}
                    <a href="?page={{ conversations.previous_page_number }}"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        السابق
                    </a>
                    {% endif %}
                    {% if conversations.has_next %}
                    <a href="?page={{ conversations.next_page_number }}"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        التالي
                    </a>
                    {% endif %}
                </div>

                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            عرض
                            <span class="font-medium">{{ conversations.start_index }}</span>
                            إلى
                            <span class="font-medium">{{ conversations.end_index }}</span>
                            من
                            <span class="font-medium">{{ conversations.paginator.count }}</span>
                            محادثة
                        </p>
                    </div>

                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if conversations.has_previous %}
                            <a href="?page={{ conversations.previous_page_number }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            {% endif %}

                            {% for num in conversations.paginator.page_range %}
                            {% if conversations.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-islamic-primary bg-islamic-primary text-sm font-medium text-white">
                                {{ num }}
                            </span>
                            {% else %}
                            <a href="?page={{ num }}"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                {{ num }}
                            </a>
                            {% endif %}
                            {% endfor %}

                            {% if conversations.has_next %}
                            <a href="?page={{ conversations.next_page_number }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        {% else %}
        <!-- Empty State -->
        <div class="bg-white rounded-xl shadow-lg p-12 text-center">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-comments text-gray-400 text-4xl"></i>
            </div>

            <h3 class="text-xl font-bold text-gray-900 mb-4">لا توجد محادثات</h3>
            <p class="text-gray-600 mb-6 max-w-md mx-auto">
                لم تبدأ أي محادثات حتى الآن. ابدأ محادثة جديدة مع
                {% if user.user_type == 'student' %}معلميك{% elif user.user_type == 'teacher' %}طلابك{% else %}المستخدمين{% endif %}.
            </p>

            <a href="{% url 'start_conversation' %}"
               class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors">
                <i class="fas fa-plus ml-2"></i>
                بدء محادثة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
// تحديث المحادثات بدون إعادة تحميل الصفحة - بدون إشعارات منبثقة
let lastUpdateTime = Date.now();

function updateConversations() {
    fetch('/messages/api/conversations-update/', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.has_updates) {
            // تحديث عداد الرسائل غير المقروءة فقط - بدون إشعارات منبثقة
            const unreadElement = document.querySelector('.unread-count');
            if (unreadElement && data.unread_count !== undefined) {
                unreadElement.textContent = data.unread_count;
            }

            // تحديث حالة المستخدمين فقط
            updateUserStatuses(data.user_statuses);

            // لا نعرض إشعارات منبثقة هنا لمنع التكرار
        }
    })
    .catch(error => console.log('تحديث المحادثات:', error));
}

function updateUserStatuses(statuses) {
    if (!statuses) return;

    Object.keys(statuses).forEach(userId => {
        const statusElement = document.querySelector(`[data-user-status="${userId}"]`);
        if (statusElement) {
            const status = statuses[userId];
            statusElement.className = `status-indicator ${status.status_color}`;
            statusElement.title = status.status_display;
        }
    });
}

// تحديث كل 15 ثانية بدلاً من إعادة تحميل الصفحة
setInterval(updateConversations, 15000);

// Mark conversation as read when clicked
document.querySelectorAll('[data-conversation-id]').forEach(element => {
    element.addEventListener('click', function() {
        const conversationId = this.dataset.conversationId;
        fetch(`/messages/conversation/${conversationId}/mark-read/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        });
    });
});

// إخفاء الإشعارات تلقائياً بعد 5 ثوان
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 300);
        }, 5000);
    });
});
</script>
{% endblock %}
