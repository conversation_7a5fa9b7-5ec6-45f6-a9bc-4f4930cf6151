{% extends 'base.html' %}
{% load static %}

{% block title %}طلبات السحب - قرآنيا{% endblock %}

{% block extra_css %}
<style>
    .tab-button {
        @apply px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 transition-colors duration-200;
    }

    .tab-button.active {
        @apply text-islamic-primary border-islamic-primary;
    }

    .tab-content {
        @apply transition-all duration-300;
    }

    .tab-content.hidden {
        @apply opacity-0 pointer-events-none;
    }

    .earnings-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .request-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">طلبات السحب</h1>
                    <p class="mt-2 text-gray-600">إدارة طلبات سحب أرباحك الشهرية</p>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="{% url 'teacher_earnings' %}" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200">
                        <i class="fas fa-chart-line ml-2"></i>
                        عرض الأرباح
                    </a>
                    <a href="{% url 'teacher_dashboard' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- Current Month Earnings Card -->
        <div class="mb-8">
            <div class="earnings-card rounded-lg shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium mb-2">أرباح الشهر الحالي</h3>
                        <p class="text-3xl font-bold">${{ total_earnings }}</p>
                        <p class="text-sm opacity-90">{{ current_month }}/{{ current_year }}</p>
                    </div>
                    <div class="text-right">
                        {% if available_earnings > 0 %}
                            <button onclick="openRequestModal()" class="bg-white text-purple-600 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                                <i class="fas fa-hand-holding-usd ml-2"></i>
                                طلب سحب
                            </button>
                        {% elif total_earnings > 0 and available_earnings == 0 %}
                            <div class="bg-white bg-opacity-20 rounded-lg p-3">
                                <p class="text-xs">جميع الأرباح محجوزة أو مسحوبة</p>
                            </div>
                        {% else %}
                            <div class="bg-white bg-opacity-20 rounded-lg p-3">
                                <p class="text-xs">لا توجد أرباح متاحة للسحب</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-file-invoice-dollar text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">إجمالي الطلبات</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_requests }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">في الانتظار</p>
                        <p class="text-2xl font-bold text-gray-900">{{ pending_requests }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">مدفوعات مكتملة</p>
                        <p class="text-2xl font-bold text-gray-900">{{ completed_requests }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">إجمالي المستلم</p>
                        <p class="text-2xl font-bold text-gray-900">${{ total_received }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8 space-x-reverse">
                <button onclick="showTab('my-requests')" id="tab-my-requests" class="tab-button active">
                    <i class="fas fa-list ml-2"></i>
                    طلباتي
                </button>
                <button onclick="showTab('payment-proofs')" id="tab-payment-proofs" class="tab-button">
                    <i class="fas fa-receipt ml-2"></i>
                    إثباتات الدفع
                </button>
                <button onclick="showTab('completed-payouts')" id="tab-completed-payouts" class="tab-button">
                    <i class="fas fa-history ml-2"></i>
                    المدفوعات المكتملة
                </button>
            </nav>
        </div>

        <!-- My Requests Tab -->
        <div id="my-requests-tab" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">طلبات السحب الخاصة بي</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأرباح المتاحة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الشهر/السنة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الطلب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ المعالجة</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for request in payout_requests %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-bold text-gray-900">${{ request.amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">${{ request.available_earnings }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ request.month }}/{{ request.year }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if request.status == 'paid' %}bg-green-100 text-green-800
                                        {% elif request.status == 'approved' %}bg-blue-100 text-blue-800
                                        {% elif request.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif request.status == 'rejected' %}bg-red-100 text-red-800
                                        {% elif request.status == 'cancelled' %}bg-gray-100 text-gray-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ request.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ request.created_at|date:"Y-m-d H:i" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {% if request.processed_at %}
                                        {{ request.processed_at|date:"Y-m-d H:i" }}
                                        {% if request.processed_by %}
                                            <div class="text-xs text-gray-500">بواسطة: {{ request.processed_by.get_full_name }}</div>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-gray-400">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-hand-holding-usd text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات سحب</h3>
                                    <p class="text-gray-500">لم تقم بإنشاء أي طلبات سحب بعد</p>
                                    {% if available_earnings > 0 %}
                                        <button onclick="openRequestModal()" class="mt-4 bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors">
                                            إنشاء طلب سحب
                                        </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Payment Proofs Tab -->
        <div id="payment-proofs-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">إثباتات الدفع المستلمة</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة الدفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم المعاملة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإرسال</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for proof in payout_proofs %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-bold text-green-600">${{ proof.payout_request.amount }}</div>
                                    <div class="text-xs text-gray-500">{{ proof.payout_request.month }}/{{ proof.payout_request.year }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ proof.payment_method }}</div>
                                    {% if proof.bank_name %}
                                        <div class="text-xs text-gray-500">{{ proof.bank_name }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ proof.transaction_id|default:"غير محدد" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ proof.uploaded_at|date:"Y-m-d H:i" }}
                                    <div class="text-xs text-gray-500">بواسطة: {{ proof.uploaded_by.get_full_name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2 space-x-reverse">
                                        <button onclick="viewProofImage('{{ proof.proof_image.url }}')" class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-image ml-1"></i>
                                            عرض الإثبات
                                        </button>
                                        {% if proof.notes %}
                                            <button onclick="alert('{{ proof.notes }}')" class="text-purple-600 hover:text-purple-900">
                                                <i class="fas fa-comment ml-1"></i>
                                                ملاحظات
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="px-6 py-12 text-center">
                                    <i class="fas fa-receipt text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد إثباتات دفع</h3>
                                    <p class="text-gray-500">ستظهر إثباتات الدفع هنا عند إرسالها من الإدارة</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Completed Payouts Tab -->
        <div id="completed-payouts-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">المدفوعات المكتملة</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الشهر/السنة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة الدفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإكمال</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">معالج بواسطة</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for payout in completed_payouts %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-bold text-green-600">${{ payout.amount }}</div>
                                    <div class="text-xs text-gray-500">طلب #{{ payout.payout_request.id }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ payout.month }}/{{ payout.year }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ payout.payment_method }}</div>
                                    {% if payout.payout_request.payout_proof.bank_name %}
                                        <div class="text-xs text-gray-500">{{ payout.payout_request.payout_proof.bank_name }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ payout.completed_at|date:"Y-m-d H:i" }}
                                    <div class="text-xs text-gray-500">
                                        طلب في: {{ payout.payout_request.created_at|date:"Y-m-d" }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ payout.processed_by.get_full_name }}
                                    <div class="text-xs text-green-600">✓ مكتمل</div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="px-6 py-12 text-center">
                                    <i class="fas fa-history text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مدفوعات مكتملة</h3>
                                    <p class="text-gray-500">ستظهر المدفوعات المكتملة هنا</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Request Payout Modal -->
<div id="requestModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">طلب سحب جديد</h3>
                <button onclick="closeRequestModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-3">تفاصيل الأرباح للشهر الحالي ({{ current_month }}/{{ current_year }})</h4>

                <div class="grid grid-cols-2 gap-4 mb-3">
                    <div class="text-center">
                        <p class="text-sm text-gray-600">إجمالي الأرباح</p>
                        <p class="text-lg font-bold text-blue-600">${{ total_earnings|floatformat:2 }}</p>
                    </div>
                    <div class="text-center">
                        <p class="text-sm text-gray-600">المتاح للسحب</p>
                        <p class="text-lg font-bold text-green-600">${{ available_earnings|floatformat:2 }}</p>
                    </div>
                </div>

                {% if reserved_earnings > 0 or withdrawn_earnings > 0 %}
                <div class="grid grid-cols-2 gap-4 pt-2 border-t border-blue-200">
                    <div class="text-center">
                        <p class="text-xs text-gray-500">محجوز للطلبات</p>
                        <p class="text-sm font-medium text-orange-600">${{ reserved_earnings|floatformat:2 }}</p>
                    </div>
                    <div class="text-center">
                        <p class="text-xs text-gray-500">تم سحبه</p>
                        <p class="text-sm font-medium text-gray-600">${{ withdrawn_earnings|floatformat:2 }}</p>
                    </div>
                </div>
                {% endif %}
            </div>

            <form method="post" id="requestForm" onsubmit="return validateForm()">
                {% csrf_token %}
                <input type="hidden" name="create_payout_request" value="1">

                <div class="space-y-4">
                    <!-- Amount -->
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">المبلغ المطلوب سحبه</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" name="amount" id="amount" step="0.01" min="1" max="{{ available_earnings }}"
                                   class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary"
                                   placeholder="0.00" required>
                        </div>
                        <div class="mt-1 text-xs">
                            <p class="text-gray-500">الحد الأقصى المتاح: <span class="font-medium text-green-600">${{ available_earnings|floatformat:2 }}</span></p>
                            {% if reserved_earnings > 0 %}
                                <p class="text-orange-600">محجوز حالياً: ${{ reserved_earnings|floatformat:2 }}</p>
                            {% endif %}
                        </div>

                        <!-- Quick Amount Buttons -->
                        <div class="mt-2 flex space-x-2 space-x-reverse">
                            <button type="button" onclick="setAmountPercentage(100)"
                                    class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200">
                                كامل المبلغ
                            </button>
                            <button type="button" onclick="setAmountPercentage(75)"
                                    class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                75%
                            </button>
                            <button type="button" onclick="setAmountPercentage(50)"
                                    class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                                50%
                            </button>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع المفضلة</label>
                        <select name="payment_method" id="payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">شيك</option>
                            <option value="paypal">PayPal</option>
                            <option value="western_union">ويسترن يونيون</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>

                    <!-- Bank Details -->
                    <div>
                        <label for="bank_details" class="block text-sm font-medium text-gray-700 mb-2">تفاصيل البنك أو الحساب</label>
                        <textarea name="bank_details" id="bank_details" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary"
                                  placeholder="اسم البنك، رقم الحساب، اسم صاحب الحساب، إلخ..." required></textarea>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية (اختياري)</label>
                        <textarea name="notes" id="notes" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary"
                                  placeholder="أي ملاحظات أو تفاصيل إضافية..."></textarea>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse mt-6">
                    <button type="button" onclick="closeRequestModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-islamic-primary text-white rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-paper-plane ml-1"></i>
                        إرسال الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 w-full max-w-4xl">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-lg font-medium text-gray-900">إثبات الدفع</h3>
                <button onclick="closeImageModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-4 text-center">
                <img id="modalImage" src="" alt="إثبات الدفع" class="max-w-full h-auto mx-auto rounded">
            </div>
        </div>
    </div>
</div>

<script>
// Tab Management
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all buttons
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName + '-tab').classList.remove('hidden');

    // Add active class to selected button
    document.getElementById('tab-' + tabName).classList.add('active');
}

// Request Modal
function openRequestModal() {
    console.log('فتح نموذج طلب السحب');
    const modal = document.getElementById('requestModal');
    if (modal) {
        modal.classList.remove('hidden');
        console.log('تم فتح النموذج بنجاح');
    } else {
        console.error('لم يتم العثور على النموذج');
    }
}

function closeRequestModal() {
    console.log('إغلاق نموذج طلب السحب');
    const modal = document.getElementById('requestModal');
    const form = document.getElementById('requestForm');

    if (modal) {
        modal.classList.add('hidden');
    }
    if (form) {
        form.reset();
    }
}

// Image Modal
function viewProofImage(imageUrl) {
    document.getElementById('modalImage').src = imageUrl;
    document.getElementById('imageModal').classList.remove('hidden');
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
}

// Form Validation
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    const maxAmount = {{ available_earnings }};

    if (amountInput) {
        amountInput.addEventListener('input', function() {
            const value = parseFloat(this.value);
            if (value > maxAmount) {
                this.value = maxAmount;
            }
        });
    }
});

// Set Amount Function
function setAmount(amount) {
    const amountInput = document.getElementById('amount');
    if (amountInput) {
        amountInput.value = amount.toFixed(2);
    }
}

// Set Amount by Percentage
function setAmountPercentage(percentage) {
    const maxAmount = {{ available_earnings }};
    const amount = (maxAmount * percentage) / 100;
    setAmount(amount);
}

// Form Validation
function validateForm() {
    console.log('بدء التحقق من صحة النموذج');

    const amount = parseFloat(document.getElementById('amount').value);
    const paymentMethod = document.getElementById('payment_method').value;
    const bankDetails = document.getElementById('bank_details').value.trim();
    const maxAmount = {{ available_earnings }};

    console.log(`البيانات: المبلغ=${amount}, الطريقة=${paymentMethod}, الحد الأقصى=${maxAmount}`);

    // التحقق من المبلغ
    if (!amount || amount <= 0) {
        alert('يرجى إدخال مبلغ صحيح');
        console.log('❌ مبلغ غير صحيح');
        return false;
    }

    if (amount > maxAmount) {
        alert(`المبلغ المطلوب ($${amount}) أكبر من الأرباح المتاحة ($${maxAmount})`);
        console.log('❌ مبلغ أكبر من المتاح');
        return false;
    }

    // التحقق من طريقة الدفع
    if (!paymentMethod) {
        alert('يرجى اختيار طريقة الدفع');
        console.log('❌ طريقة دفع غير محددة');
        return false;
    }

    // التحقق من تفاصيل البنك
    if (!bankDetails) {
        alert('يرجى إدخال تفاصيل البنك أو الحساب');
        console.log('❌ تفاصيل البنك فارغة');
        return false;
    }

    console.log('✅ جميع البيانات صحيحة');

    // تأكيد الإرسال
    const confirmed = confirm(`هل أنت متأكد من طلب سحب $${amount.toFixed(2)}؟\nسيتم حجز هذا المبلغ من أرباحك فور الإرسال.`);
    console.log(`تأكيد المستخدم: ${confirmed}`);

    return confirmed;
}

// Modal close listeners
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('requestModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeRequestModal();
        }
    });

    document.getElementById('imageModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeImageModal();
        }
    });
});
</script>
{% endblock %}
