@echo off
echo 🕌 نظام قرآنيا التعليمي
echo ==================
echo.
echo 🚀 بدء تشغيل الخادم...
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo ⚠️  البيئة الافتراضية غير موجودة. يرجى تشغيل setup.bat أولاً
    pause
    exit /b 1
)

REM Activate virtual environment
call venv\Scripts\activate

REM Run migrations if needed
echo 📊 فحص قاعدة البيانات...
python manage.py makemigrations --check --dry-run > nul 2>&1
if errorlevel 1 (
    echo 🔄 تطبيق تحديثات قاعدة البيانات...
    python manage.py makemigrations
    python manage.py migrate
)

REM Collect static files if needed
echo 📁 فحص الملفات الثابتة...
python manage.py collectstatic --noinput > nul 2>&1

REM Start the server
echo.
echo ✅ تم بدء الخادم بنجاح!
echo 🌐 يمكنك الوصول للنظام على: http://localhost:8080
echo.
echo 📋 بيانات تسجيل الدخول الافتراضية:
echo    المدير: admin / [كلمة المرور التي أدخلتها]
echo    معلم: teacher1 / teacher123
echo    طالب: student1 / student123
echo.
echo 🛑 لإيقاف الخادم اضغط Ctrl+C
echo.

python manage.py runserver 8080
