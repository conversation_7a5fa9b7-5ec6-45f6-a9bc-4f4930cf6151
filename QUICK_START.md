# 🚀 دليل البدء السريع - نظام قرآنيا التعليمي

## 📋 متطلبات النظام
- Python 3.8 أو أحدث
- Windows 10/11
- اتصال بالإنترنت (للتثبيت الأولي)

## ⚡ البدء السريع

### 1️⃣ الإعداد الأولي (مرة واحدة فقط)
```bash
# تشغيل ملف الإعداد
setup.bat
```

### 2️⃣ تشغيل النظام يومياً
```bash
# تشغيل النظام
start_qurania.bat
```

### 3️⃣ الوصول للنظام
افتح المتصفح وانتقل إلى: **http://localhost:8080**

## 👤 بيانات تسجيل الدخول

### المدير
- **اسم المستخدم**: admin
- **كلمة المرور**: [التي أدخلتها عند الإعداد]

### معلم تجريبي
- **اسم المستخدم**: teacher1
- **كلمة المرور**: teacher123

### طالب تجريبي
- **اسم المستخدم**: student1
- **كلمة المرور**: student123

## 🎯 الميزات الرئيسية

### للمدير
- ✅ إدارة المستخدمين والدورات
- ✅ عرض التقارير والإحصائيات
- ✅ إدارة الدعم الفني
- ✅ إعدادات النظام

### للمعلم
- ✅ جدولة وإدارة الحصص
- ✅ تتبع تقدم الطلاب
- ✅ إدارة الأرباح والتقييمات
- ✅ التواصل مع الطلاب

### للطالب
- ✅ حضور الحصص المجدولة
- ✅ تتبع التقدم الشخصي
- ✅ أرشيف الحصص السابقة
- ✅ التواصل مع المعلمين

## 🔧 استكشاف الأخطاء

### مشكلة: Python غير موجود
**الحل**: قم بتثبيت Python من https://www.python.org/downloads/

### مشكلة: خطأ في تشغيل setup.bat
**الحل**: 
1. تأكد من تشغيل Command Prompt كمدير
2. تأكد من وجود اتصال بالإنترنت

### مشكلة: لا يمكن الوصول للموقع
**الحل**:
1. تأكد من تشغيل start_qurania.bat
2. انتظر حتى ظهور رسالة "Starting development server"
3. تأكد من الرابط: http://localhost:8080

### مشكلة: خطأ في تسجيل الدخول
**الحل**:
1. تأكد من صحة اسم المستخدم وكلمة المرور
2. للمدير: استخدم البيانات التي أدخلتها عند الإعداد
3. للمعلم/الطالب: استخدم البيانات التجريبية المذكورة أعلاه

## 📞 الدعم الفني

### إذا واجهت أي مشاكل:
1. اقرأ ملف README.md للتفاصيل الكاملة
2. تحقق من ملف TROUBLESHOOTING.md
3. تواصل مع فريق الدعم

## 🎉 نصائح للاستخدام

### للمديرين:
- ابدأ بإضافة معلمين جدد من "إدارة المستخدمين"
- أنشئ دورات جديدة من "إدارة الدورات"
- راجع التقارير بانتظام

### للمعلمين:
- حدث جدول حصصك من "جدول الحصص"
- تابع تقدم طلابك من "طلابي"
- راجع أرباحك من "الأرباح"

### للطلاب:
- تحقق من حصصك اليومية من "حصصي"
- تابع تقدمك من "تقدمي"
- راجع حصصك السابقة من "الأرشيف"

## 🔄 تحديث النظام

لتحديث النظام مستقبلاً:
1. أوقف الخادم (Ctrl+C)
2. شغل `git pull` لتحديث الكود
3. شغل `start_qurania.bat` مرة أخرى

---

**"وَقُلْ رَبِّ زِدْنِي عِلْمًا"** - سورة طه، آية 114

🕌 **نظام قرآنيا التعليمي** - منصة تعليم القرآن الكريم
