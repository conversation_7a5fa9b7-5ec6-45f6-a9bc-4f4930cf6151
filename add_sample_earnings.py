#!/usr/bin/env python
"""
Script لإضافة أرباح تجريبية للمعلمين لاختبار النظام
"""

import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedelta
import random

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania_lms.settings')
django.setup()

from django.utils import timezone
from users.models import User
from courses.models import TeacherEarning, Course, Enrollment, Payment
from lessons.models import Lesson

def create_sample_earnings():
    """إنشاء أرباح تجريبية للمعلمين"""

    print("🚀 بدء إضافة أرباح تجريبية للمعلمين...")

    # جلب جميع المعلمين
    teachers = User.objects.filter(user_type='teacher', is_active=True)

    if not teachers.exists():
        print("❌ لا يوجد معلمين في النظام!")
        return

    print(f"📚 تم العثور على {teachers.count()} معلم")

    # جلب الطلاب والدورات
    students = User.objects.filter(user_type='student', is_active=True)
    courses = Course.objects.filter(is_active=True)

    if not students.exists() or not courses.exists():
        print("❌ يجب وجود طلاب ودورات في النظام أولاً!")
        return

    # التواريخ للشهرين الماضيين والشهر الحالي
    current_date = timezone.now()
    current_month = current_date.month
    current_year = current_date.year

    # الشهر الماضي
    last_month_date = current_date - timedelta(days=30)
    last_month = last_month_date.month
    last_year = last_month_date.year

    # الشهر قبل الماضي
    two_months_ago = current_date - timedelta(days=60)
    two_months_ago_month = two_months_ago.month
    two_months_ago_year = two_months_ago.year

    months_data = [
        (two_months_ago_month, two_months_ago_year, "قبل شهرين"),
        (last_month, last_year, "الشهر الماضي"),
        (current_month, current_year, "الشهر الحالي")
    ]

    for teacher in teachers:
        print(f"\n👨‍🏫 إضافة أرباح للمعلم: {teacher.get_full_name() or teacher.username}")

        # إنشاء تسجيلات تجريبية للمعلم إذا لم تكن موجودة
        enrollments = Enrollment.objects.filter(teacher=teacher)
        if not enrollments.exists():
            print("  📝 إنشاء تسجيلات تجريبية...")
            for i, student in enumerate(students[:3]):  # 3 طلاب لكل معلم
                course = courses[i % courses.count()]
                enrollment = Enrollment.objects.create(
                    student=student,
                    teacher=teacher,
                    course=course,
                    start_date=current_date.date(),
                    amount_paid=course.price,
                    remaining_lessons=course.lessons_per_week * course.duration_weeks,
                    status='active'
                )

                # إنشاء دفعة تجريبية
                payment = Payment.objects.create(
                    enrollment=enrollment,
                    amount=course.price,
                    payment_method='cash',
                    status='completed',
                    processed_by=None
                )
                print(f"    ✅ تسجيل: {student.get_full_name()} في {course.title}")

        # الحصول على التسجيلات
        enrollments = list(Enrollment.objects.filter(teacher=teacher))

        for month, year, month_name in months_data:
            print(f"  📅 {month_name} ({month}/{year})")

            # حذف الأرباح الموجودة لهذا الشهر (لتجنب التكرار)
            TeacherEarning.objects.filter(
                teacher=teacher,
                month=month,
                year=year
            ).delete()

            # إنشاء أرباح لكل مدة حصة
            for duration in [30, 45, 60]:
                # عدد حصص عشوائي (بين 5-20 حصة)
                lessons_count = random.randint(5, 20)

                # سعر الحصة حسب المدة
                lesson_prices = {30: 50, 45: 70, 60: 90}
                lesson_price = Decimal(str(lesson_prices[duration]))

                # نسبة العمولة (70%)
                commission_rate = Decimal('70.00')

                # حساب الربح الإجمالي
                earning_per_lesson = (lesson_price * commission_rate) / Decimal('100')
                total_earning = earning_per_lesson * lessons_count

                # اختيار تسجيل عشوائي للربط
                enrollment = random.choice(enrollments)
                payment = Payment.objects.filter(enrollment=enrollment).first()

                # إنشاء سجل الربح
                earning = TeacherEarning.objects.create(
                    teacher=teacher,
                    enrollment=enrollment,
                    payment=payment,
                    amount=total_earning,
                    commission_rate=commission_rate,
                    lesson_duration=duration,
                    lesson_price=lesson_price,
                    lessons_completed=lessons_count,
                    month=month,
                    year=year,
                    is_paid=month != current_month,  # الشهر الحالي غير مدفوع
                    paid_date=timezone.now() if month != current_month else None
                )

                print(f"    ✅ {duration} دقيقة: {lessons_count} حصة = ${total_earning:.2f}")

    print("\n🎉 تم إضافة الأرباح التجريبية بنجاح!")

    # عرض ملخص الأرباح
    print("\n📊 ملخص الأرباح:")
    for teacher in teachers:
        total_earnings = TeacherEarning.objects.filter(teacher=teacher).aggregate(
            total=django.db.models.Sum('amount')
        )['total'] or Decimal('0')

        current_month_earnings = TeacherEarning.objects.filter(
            teacher=teacher,
            month=current_month,
            year=current_year
        ).aggregate(
            total=django.db.models.Sum('amount')
        )['total'] or Decimal('0')

        print(f"  👨‍🏫 {teacher.get_full_name() or teacher.username}:")
        print(f"    💰 إجمالي الأرباح: ${total_earnings:.2f}")
        print(f"    📅 أرباح الشهر الحالي: ${current_month_earnings:.2f}")

def create_sample_courses_and_enrollments():
    """إنشاء دورات وتسجيلات تجريبية إذا لم تكن موجودة"""

    print("\n📚 التحقق من وجود دورات وتسجيلات...")

    # إنشاء دورات تجريبية إذا لم تكن موجودة
    if not Course.objects.exists():
        print("📖 إنشاء دورات تجريبية...")

        courses_data = [
            {
                'title': 'حفظ جزء عم',
                'description': 'دورة لحفظ جزء عم كاملاً مع التجويد',
                'course_type': 'quran_memorization',
                'difficulty_level': 'beginner',
                'price': Decimal('200.00'),
                'duration_weeks': 12,
                'lessons_per_week': 3,
                'lesson_duration': 45
            },
            {
                'title': 'تحسين التلاوة',
                'description': 'دورة لتحسين التلاوة وتطبيق أحكام التجويد',
                'course_type': 'quran_recitation',
                'difficulty_level': 'intermediate',
                'price': Decimal('300.00'),
                'duration_weeks': 16,
                'lessons_per_week': 2,
                'lesson_duration': 60
            },
            {
                'title': 'مراجعة القرآن الكريم',
                'description': 'دورة لمراجعة وتثبيت الحفظ',
                'course_type': 'quran_review',
                'difficulty_level': 'advanced',
                'price': Decimal('150.00'),
                'duration_weeks': 8,
                'lessons_per_week': 4,
                'lesson_duration': 30
            }
        ]

        for course_data in courses_data:
            Course.objects.create(**course_data)
            print(f"  ✅ تم إنشاء دورة: {course_data['title']}")

    # إنشاء طلاب تجريبيين إذا لم يكونوا موجودين
    students = User.objects.filter(user_type='student')
    if students.count() < 3:
        print("👨‍🎓 إنشاء طلاب تجريبيين...")

        for i in range(1, 4):
            if not User.objects.filter(username=f'student{i}').exists():
                User.objects.create_user(
                    username=f'student{i}',
                    email=f'student{i}@example.com',
                    password='student123',
                    first_name=f'طالب',
                    last_name=f'{i}',
                    user_type='student'
                )
                print(f"  ✅ تم إنشاء طالب: student{i}")

def main():
    """الدالة الرئيسية"""
    print("🎯 إعداد بيانات تجريبية لنظام الأرباح")
    print("=" * 50)

    try:
        # إنشاء دورات وطلاب تجريبيين إذا لم يكونوا موجودين
        create_sample_courses_and_enrollments()

        # إنشاء أرباح تجريبية
        create_sample_earnings()

        print("\n" + "=" * 50)
        print("✅ تم إعداد البيانات التجريبية بنجاح!")
        print("\n🔗 يمكنك الآن اختبار النظام على:")
        print("   📊 لوحة تحكم المعلم: http://localhost:8080/dashboard/teacher/earnings/")
        print("   👨‍💼 لوحة تحكم المدير: http://localhost:8080/dashboard/admin/teacher-payouts/")

    except Exception as e:
        print(f"\n❌ حدث خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
