# Generated by Django 4.2.7 on 2025-05-22 23:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('support', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الرسالة')),
                ('content', models.TextField(verbose_name='محتوى الرسالة')),
                ('message_type', models.CharField(choices=[('announcement', 'إعلان عام'), ('warning', 'تحذير مهم'), ('reminder', 'تذكير'), ('update', 'تحديث النظام'), ('maintenance', 'إشعار صيانة'), ('policy', 'سياسة جديدة'), ('feature', 'ميزة جديدة'), ('urgent', 'عاجل')], default='announcement', max_length=20, verbose_name='نوع الرسالة')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('urgent', 'عاجل')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'رسالة النظام',
                'verbose_name_plural': 'رسائل النظام',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemMessageRecipient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الاستلام')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='system_message_receipts', to=settings.AUTH_USER_MODEL, verbose_name='المستقبل')),
                ('system_message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_recipients', to='support.systemmessage', verbose_name='رسالة النظام')),
            ],
            options={
                'verbose_name': 'مستقبل رسالة النظام',
                'verbose_name_plural': 'مستقبلو رسائل النظام',
                'unique_together': {('system_message', 'recipient')},
            },
        ),
        migrations.AddField(
            model_name='systemmessage',
            name='recipients',
            field=models.ManyToManyField(related_name='received_system_messages', through='support.SystemMessageRecipient', to=settings.AUTH_USER_MODEL, verbose_name='المستقبلون'),
        ),
        migrations.AddField(
            model_name='systemmessage',
            name='sent_by',
            field=models.ForeignKey(limit_choices_to={'user_type': 'admin'}, on_delete=django.db.models.deletion.CASCADE, related_name='sent_system_messages', to=settings.AUTH_USER_MODEL, verbose_name='مُرسل بواسطة'),
        ),
    ]
