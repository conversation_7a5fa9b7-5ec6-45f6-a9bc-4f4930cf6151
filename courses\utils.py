"""
وظائف مساعدة لإدارة الدورات والحصص
"""

from django.utils import timezone
from datetime import timedelta, datetime
from django.contrib.auth import get_user_model
from .models import Course, Enrollment, Payment, TeacherEarning, TeacherRating
from lessons.models import Lesson
from notifications.models import Notification

User = get_user_model()


def create_enrollment(student, course, teacher, start_date=None, admin_user=None):
    """
    إنشاء تسجيل جديد للطالب في دورة مع معلم محدد
    """
    if start_date is None:
        start_date = timezone.now().date()

    # التحقق من عدم وجود تسجيل مسبق
    existing_enrollment = Enrollment.objects.filter(
        student=student,
        course=course,
        status__in=['active', 'pending']
    ).first()

    if existing_enrollment:
        return None, "الطالب مسجل بالفعل في هذه الدورة"

    # إنشاء التسجيل
    enrollment = Enrollment.objects.create(
        student=student,
        course=course,
        teacher=teacher,
        start_date=start_date,
        amount_paid=course.price,
        remaining_lessons=course.get_total_lessons(),
        status='pending'  # في انتظار الدفع
    )

    # إنشاء إشعارات
    create_enrollment_notifications(enrollment, admin_user)

    return enrollment, "تم إنشاء التسجيل بنجاح"


def create_payment(enrollment, amount, payment_method='cash', admin_user=None):
    """
    إنشاء دفعة جديدة للتسجيل
    """
    payment = Payment.objects.create(
        enrollment=enrollment,
        amount=amount,
        payment_method=payment_method,
        status='completed',
        processed_by=admin_user
    )

    # تحديث حالة التسجيل
    enrollment.status = 'active'
    enrollment.save()

    # إنشاء أرباح المعلم
    create_teacher_earning(payment)

    # إنشاء الحصص التلقائية
    create_automatic_lessons(enrollment, admin_user)

    # إنشاء إشعارات
    create_payment_notifications(payment)

    return payment


def create_teacher_earning(payment):
    """
    إنشاء ربح المعلم من الدفعة
    """
    enrollment = payment.enrollment
    teacher = enrollment.teacher

    # حساب نسبة العمولة (70% للمعلم)
    from decimal import Decimal
    commission_rate = Decimal('70.00')
    # تأكد من أن payment.amount هو Decimal
    payment_amount = Decimal(str(payment.amount))
    earning_amount = (payment_amount * commission_rate) / Decimal('100')

    # إنشاء ربح المعلم
    current_date = timezone.now()
    teacher_earning = TeacherEarning.objects.create(
        teacher=teacher,
        enrollment=enrollment,
        payment=payment,
        amount=earning_amount,
        commission_rate=commission_rate,
        month=current_date.month,
        year=current_date.year
    )

    return teacher_earning


def create_automatic_lessons(enrollment, admin_user=None):
    """
    إنشاء الحصص التلقائية للتسجيل
    """
    course = enrollment.course
    start_date = enrollment.start_date

    lessons_created = []
    current_date = start_date

    # إنشاء الحصص لكل أسبوع
    for week in range(course.duration_weeks):
        # إنشاء الحصص لكل أسبوع حسب عدد الحصص المطلوبة
        for lesson_num in range(course.lessons_per_week):
            # حساب تاريخ الحصة (مثلاً: الأحد والثلاثاء)
            days_to_add = (week * 7) + (lesson_num * 2)  # كل يومين
            lesson_date = current_date + timedelta(days=days_to_add)

            # تحديد وقت الحصة (افتراضي: 10:00 صباحاً)
            lesson_datetime = datetime.combine(lesson_date, datetime.min.time().replace(hour=10))
            lesson_datetime = timezone.make_aware(lesson_datetime)

            # إنشاء الحصة
            lesson = Lesson.objects.create(
                enrollment=enrollment,
                title=f"حصة {course.title} - الأسبوع {week + 1} - الحصة {lesson_num + 1}",
                description=f"حصة من دورة {course.title}",
                scheduled_date=lesson_datetime,
                duration_minutes=course.lesson_duration,
                created_by=admin_user or enrollment.teacher
            )

            lessons_created.append(lesson)

    # إنشاء إشعارات للحصص الجديدة
    for lesson in lessons_created[:3]:  # أول 3 حصص فقط
        create_lesson_notifications(lesson)

    return lessons_created


def create_enrollment_notifications(enrollment, admin_user=None):
    """
    إنشاء إشعارات التسجيل الجديد
    """
    # إشعار للطالب
    Notification.objects.create(
        recipient=enrollment.student,
        sender=admin_user,
        notification_type='system_announcement',
        title='تسجيل جديد في دورة',
        message=f'تم تسجيلك في دورة {enrollment.course.title} مع الأستاذ {enrollment.teacher.get_full_name()}',
        priority='medium'
    )

    # إشعار للمعلم
    Notification.objects.create(
        recipient=enrollment.teacher,
        sender=admin_user,
        notification_type='system_announcement',
        title='طالب جديد',
        message=f'تم تسجيل الطالب {enrollment.student.get_full_name()} في دورة {enrollment.course.title}',
        priority='medium'
    )


def create_payment_notifications(payment):
    """
    إنشاء إشعارات الدفع
    """
    enrollment = payment.enrollment

    # إشعار للطالب
    Notification.objects.create(
        recipient=enrollment.student,
        notification_type='system_announcement',
        title='تم تأكيد الدفع',
        message=f'تم تأكيد دفع {payment.amount} ريال لدورة {enrollment.course.title}',
        priority='high'
    )

    # إشعار للمعلم
    Notification.objects.create(
        recipient=enrollment.teacher,
        notification_type='system_announcement',
        title='تم تأكيد دفع الطالب',
        message=f'تم تأكيد دفع الطالب {enrollment.student.get_full_name()} لدورة {enrollment.course.title}',
        priority='medium'
    )


def create_lesson_notifications(lesson):
    """
    إنشاء إشعارات الحصة الجديدة
    """
    enrollment = lesson.enrollment

    # إشعار للطالب
    Notification.objects.create(
        recipient=enrollment.student,
        notification_type='lesson_reminder',
        title='حصة جديدة مجدولة',
        message=f'تم جدولة حصة جديدة: {lesson.title} في {lesson.scheduled_date.strftime("%Y-%m-%d %H:%M")}',
        priority='medium'
    )

    # إشعار للمعلم
    Notification.objects.create(
        recipient=enrollment.teacher,
        notification_type='lesson_reminder',
        title='حصة جديدة مجدولة',
        message=f'تم جدولة حصة جديدة مع {enrollment.student.get_full_name()}: {lesson.title}',
        priority='medium'
    )


def update_teacher_rating(teacher):
    """
    تحديث تقييم المعلم الإجمالي
    """
    teacher_rating, created = TeacherRating.objects.get_or_create(teacher=teacher)
    teacher_rating.update_rating()
    return teacher_rating


def get_dashboard_stats(user):
    """
    الحصول على إحصائيات لوحة التحكم حسب نوع المستخدم
    """
    if user.is_admin():
        return get_admin_stats()
    elif user.is_teacher():
        return get_teacher_stats(user)
    elif user.is_student():
        return get_student_stats(user)
    return {}


def get_admin_stats():
    """
    إحصائيات لوحة المدير
    """
    from django.db.models import Sum, Count
    from support.models import SupportTicket

    today = timezone.now().date()
    week_ago = timezone.now() - timedelta(days=7)
    month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    return {
        'total_users': User.objects.count(),
        'total_students': User.objects.filter(user_type='student').count(),
        'total_teachers': User.objects.filter(user_type='teacher').count(),
        'total_courses': Course.objects.filter(is_active=True).count(),
        'active_enrollments': Enrollment.objects.filter(status='active').count(),
        'today_lessons': Lesson.objects.filter(
            scheduled_date__date=today,
            status__in=['scheduled', 'in_progress']
        ).count(),
        'open_tickets': SupportTicket.objects.filter(
            status__in=['open', 'in_progress', 'waiting_response']
        ).count(),
        'new_users_this_week': User.objects.filter(created_at__gte=week_ago).count(),
        'total_revenue': Payment.objects.filter(status='completed').aggregate(
            total=Sum('amount')
        )['total'] or 0,
        'completed_lessons_this_month': Lesson.objects.filter(
            status='completed',
            actual_end_time__gte=month_start
        ).count(),
    }


def get_teacher_stats(teacher):
    """
    إحصائيات لوحة المعلم
    """
    from django.db.models import Sum

    today = timezone.now().date()
    current_month = timezone.now().month
    current_year = timezone.now().year

    # إحصائيات التقييم
    try:
        teacher_rating = TeacherRating.objects.get(teacher=teacher)
        avg_rating = float(teacher_rating.average_rating)
        total_ratings = teacher_rating.total_ratings
    except TeacherRating.DoesNotExist:
        avg_rating = 0.0
        total_ratings = 0

    return {
        'today_lessons_count': Lesson.objects.filter(
            enrollment__teacher=teacher,
            scheduled_date__date=today,
            status__in=['scheduled', 'in_progress']
        ).count(),
        'upcoming_lessons_count': Lesson.objects.filter(
            enrollment__teacher=teacher,
            scheduled_date__gt=timezone.now(),
            status='scheduled'
        ).count(),
        'total_students': Enrollment.objects.filter(
            teacher=teacher,
            status='active'
        ).values('student').distinct().count(),
        'avg_rating': avg_rating,
        'total_ratings': total_ratings,
        'monthly_earnings': TeacherEarning.objects.filter(
            teacher=teacher,
            month=current_month,
            year=current_year
        ).aggregate(total=Sum('amount'))['total'] or 0,
        'completed_lessons': Lesson.objects.filter(
            enrollment__teacher=teacher,
            status='completed'
        ).count(),
    }


def get_student_stats(student):
    """
    إحصائيات لوحة الطالب
    """
    today = timezone.now().date()

    # إحصائيات الحصص
    total_lessons_completed = Lesson.objects.filter(
        enrollment__student=student,
        status='completed'
    ).count()

    total_lessons_scheduled = Lesson.objects.filter(
        enrollment__student=student,
        status__in=['scheduled', 'in_progress', 'completed']
    ).count()

    # حساب نسبة التقدم
    if total_lessons_scheduled > 0:
        progress_percentage = round((total_lessons_completed / total_lessons_scheduled) * 100)
    else:
        progress_percentage = 0

    return {
        'today_lessons_count': Lesson.objects.filter(
            enrollment__student=student,
            scheduled_date__date=today,
            status__in=['scheduled', 'in_progress']
        ).count(),
        'upcoming_lessons_count': Lesson.objects.filter(
            enrollment__student=student,
            scheduled_date__gt=timezone.now(),
            status='scheduled'
        ).count(),
        'total_courses': Enrollment.objects.filter(
            student=student,
            status='active'
        ).count(),
        'total_lessons_completed': total_lessons_completed,
        'total_lessons_remaining': Lesson.objects.filter(
            enrollment__student=student,
            status='scheduled'
        ).count(),
        'progress_percentage': progress_percentage,
        'current_level': student.student_level or 'مبتدئ',
    }
