# Generated by Django 4.2.7 on 2025-05-23 18:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0004_user_rejection_reason_user_verification_notes_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='ban_reason',
            field=models.TextField(blank=True, null=True, verbose_name='سبب الحظر'),
        ),
        migrations.AddField(
            model_name='user',
            name='ban_type',
            field=models.CharField(blank=True, choices=[('temporary', 'مؤقت'), ('permanent', 'دائم')], max_length=20, null=True, verbose_name='نوع الحظر'),
        ),
        migrations.AddField(
            model_name='user',
            name='banned_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحظر'),
        ),
        migrations.AddField(
            model_name='user',
            name='banned_by',
            field=models.ForeignKey(blank=True, limit_choices_to={'user_type': 'admin'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='banned_users', to=settings.AUTH_USER_MODEL, verbose_name='تم الحظر بواسطة'),
        ),
        migrations.AddField(
            model_name='user',
            name='banned_until',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء الحظر'),
        ),
        migrations.AddField(
            model_name='user',
            name='is_banned',
            field=models.BooleanField(default=False, verbose_name='محظور'),
        ),
    ]
