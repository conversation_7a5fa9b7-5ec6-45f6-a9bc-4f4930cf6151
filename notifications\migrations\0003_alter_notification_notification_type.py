# Generated by Django 4.2.7 on 2025-05-22 16:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0002_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(choices=[('lesson_reminder', 'تذكير بالحصة'), ('lesson_cancelled', 'إلغاء الحصة'), ('lesson_rescheduled', 'إعادة جدولة الحصة'), ('lesson_completed', 'اكتمال حصة'), ('lesson_created', 'إنشاء حصة جديدة'), ('lesson_starting_soon', 'الحصة تبدأ قريباً'), ('payment_received', 'استلام دفعة'), ('payment_confirmed', 'تأكيد دفعة'), ('payment_reminder', 'تذكير بالدفع'), ('payment_failed', 'فشل في الدفع'), ('payment_status_changed', 'تغيير حالة الدفع'), ('new_enrollment', 'تسجيل جديد'), ('enrollment_activated', 'تفعيل التسجيل'), ('course_completed', 'اكتمال الدورة'), ('rating_received', 'استلام تقييم'), ('rating_request', 'طلب تقييم'), ('new_student_assigned', 'تعيين طالب جديد'), ('teacher_earnings_updated', 'تحديث أرباح المعلم'), ('teacher_rate_changed', 'تغيير أسعار المعلم'), ('new_message', 'رسالة جديدة'), ('support_ticket', 'تذكرة دعم'), ('support_ticket_updated', 'تحديث تذكرة دعم'), ('system_announcement', 'إعلان النظام'), ('admin_new_user', 'مستخدم جديد'), ('admin_payment_pending', 'دفعة في الانتظار'), ('admin_low_rating', 'تقييم منخفض')], max_length=30, verbose_name='نوع الإشعار'),
        ),
    ]
