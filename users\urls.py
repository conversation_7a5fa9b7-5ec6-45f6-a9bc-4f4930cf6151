from django.urls import path
from . import views

urlpatterns = [
    # Dashboard URLs
    path('', views.dashboard, name='dashboard'),
    path('admin/', views.admin_dashboard, name='admin_dashboard'),
    path('teacher/', views.teacher_dashboard, name='teacher_dashboard'),
    path('student/', views.student_dashboard, name='student_dashboard'),

    # Additional URLs
    path('profile/', views.user_profile, name='profile'),
    path('register/', views.register, name='register'),

    # Admin specific URLs
    path('admin/users/', views.admin_users, name='admin_users'),
    path('admin/courses/', views.admin_courses, name='admin_courses'),
    path('admin/lessons/', views.admin_lessons, name='admin_lessons'),
    path('admin/reports/', views.admin_reports, name='admin_reports'),
    path('admin/support/', views.admin_support, name='admin_support'),
    path('admin/academy-settings/', views.admin_academy_settings, name='admin_academy_settings'),

    # New admin URLs
    path('admin/teacher-rates/', views.admin_teacher_rates, name='admin_teacher_rates'),
    path('admin/payment-management/', views.admin_payment_management, name='admin_payment_management'),
    path('admin/teacher-payouts/', views.admin_teacher_payouts, name='admin_teacher_payouts'),
    path('admin/ratings/', views.admin_ratings_view, name='admin_ratings_view'),

    # Cross-Dashboard Integration URLs
    path('admin/user/<int:user_id>/', views.admin_user_detail, name='admin_user_detail'),
    path('admin/create-enrollment/', views.admin_create_enrollment, name='admin_create_enrollment'),
    path('admin/create-multiple-enrollments/', views.admin_create_multiple_enrollments, name='admin_create_multiple_enrollments'),
    path('admin/enrollment/<int:enrollment_id>/', views.admin_enrollment_detail, name='admin_enrollment_detail'),
    path('admin/schedule-lesson/', views.admin_schedule_lesson, name='admin_schedule_lesson'),

    # User Verification URLs
    path('admin/user-verifications/', views.admin_user_verifications, name='admin_user_verifications'),
    path('verification-pending/', views.verification_pending, name='verification_pending'),
    path('verification-rejected/', views.verification_rejected, name='verification_rejected'),
    path('registration-success/<int:user_id>/', views.registration_success, name='registration_success'),
    path('banned/', views.user_banned, name='user_banned'),

    # Teacher specific URLs
    path('teacher/schedule/', views.teacher_schedule, name='teacher_schedule'),
    path('teacher/students/', views.teacher_students, name='teacher_students'),
    path('teacher/earnings/', views.teacher_earnings, name='teacher_earnings'),
    path('teacher/payouts/', views.teacher_payouts, name='teacher_payouts'),
    path('teacher/ratings/', views.teacher_ratings, name='teacher_ratings'),

    # Student specific URLs
    path('student/lessons/', views.student_lessons, name='student_lessons'),
    path('student/progress/', views.student_progress, name='student_progress'),
    path('student/archive/', views.student_archive, name='student_archive'),
    path('student/subscriptions/', views.student_subscriptions, name='student_subscriptions'),

    # Common URLs
    path('calendar/', views.calendar_view, name='calendar'),
    path('notifications/', views.notifications_redirect, name='notifications'),
    path('messages/', views.messages_view, name='messages'),
    path('support/', views.support_view, name='support'),

    # Static pages
    path('about/', views.about, name='about'),
    path('privacy/', views.privacy, name='privacy'),
    path('terms/', views.terms, name='terms'),
]
