{% extends 'base.html' %}
{% load static %}

{% block title %}مدفوعات الطلاب - قرآنيا{% endblock %}

{% block extra_css %}
<style>
    .tab-button {
        @apply px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 transition-colors duration-200;
    }

    .tab-button.active {
        @apply text-islamic-primary border-islamic-primary;
    }

    .tab-content {
        @apply transition-all duration-300;
    }

    .tab-content.hidden {
        @apply opacity-0 pointer-events-none;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">مدفوعات الطلاب</h1>
                    <p class="mt-2 text-gray-600">إدارة طلبات الدفع والاشتراكات</p>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <button onclick="openCreateRequestModal()" class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-secondary transition-colors duration-200">
                        <i class="fas fa-plus ml-2"></i>
                        إنشاء طلب دفع
                    </button>
                    <a href="{% url 'admin_dashboard' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-file-invoice-dollar text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">طلبات الدفع</p>
                        <p class="text-2xl font-bold text-gray-900">{{ payment_requests_count }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">مدفوعات مؤكدة</p>
                        <p class="text-2xl font-bold text-gray-900">{{ confirmed_payments }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">في الانتظار</p>
                        <p class="text-2xl font-bold text-gray-900">{{ pending_payments }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">إجمالي الإيرادات</p>
                        <p class="text-2xl font-bold text-gray-900">${{ total_revenue }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8 space-x-reverse">
                <button onclick="showTab('payment-requests')" id="tab-payment-requests" class="tab-button active">
                    <i class="fas fa-file-invoice ml-2"></i>
                    طلبات الدفع
                </button>
                <button onclick="showTab('payment-proofs')" id="tab-payment-proofs" class="tab-button">
                    <i class="fas fa-image ml-2"></i>
                    إثباتات الدفع
                </button>
                <button onclick="showTab('old-payments')" id="tab-old-payments" class="tab-button">
                    <i class="fas fa-history ml-2"></i>
                    المدفوعات القديمة
                </button>
            </nav>
        </div>

        <!-- Payment Requests Tab -->
        <div id="payment-requests-tab" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">طلبات الدفع</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">خطة الاشتراك</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخصم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ النهائي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الاستحقاق</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for request in payment_requests %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-user text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ request.student.get_full_name }}</div>
                                            <div class="text-sm text-gray-500">{{ request.student.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ request.subscription_plan.name }}</div>
                                    <div class="text-sm text-gray-500">{{ request.subscription_plan.lessons_count }} حصة - {{ request.subscription_plan.lesson_duration }} دقيقة</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">${{ request.amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if request.discount %}
                                        <div class="text-sm text-green-600">${{ request.discount_amount }}</div>
                                        <div class="text-xs text-gray-500">{{ request.discount.code }}</div>
                                    {% else %}
                                        <span class="text-sm text-gray-400">-</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-bold text-gray-900">${{ request.final_amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if request.status == 'confirmed' %}bg-green-100 text-green-800
                                        {% elif request.status == 'paid' %}bg-blue-100 text-blue-800
                                        {% elif request.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif request.status == 'rejected' %}bg-red-100 text-red-800
                                        {% elif request.status == 'expired' %}bg-gray-100 text-gray-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ request.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ request.due_date|date:"Y-m-d H:i" }}
                                    {% if request.is_expired %}
                                        <span class="text-red-500 text-xs block">منتهي الصلاحية</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2 space-x-reverse">
                                        {% if request.status == 'paid' %}
                                            <button onclick="confirmPayment({{ request.id }})" class="text-green-600 hover:text-green-900">
                                                <i class="fas fa-check ml-1"></i>
                                                تأكيد
                                            </button>
                                            <button onclick="rejectPayment({{ request.id }})" class="text-red-600 hover:text-red-900">
                                                <i class="fas fa-times ml-1"></i>
                                                رفض
                                            </button>
                                        {% endif %}
                                        <button onclick="viewRequestDetails({{ request.id }})" class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye ml-1"></i>
                                            عرض
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="8" class="px-6 py-12 text-center">
                                    <i class="fas fa-file-invoice text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات دفع</h3>
                                    <p class="text-gray-500">لم يتم إنشاء أي طلبات دفع بعد</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Payment Proofs Tab -->
        <div id="payment-proofs-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">إثباتات الدفع المرفوعة</h3>
                    <p class="text-sm text-gray-600 mt-1">لتأكيد أو رفض الدفعات، انتقل إلى تبويب "طلبات الدفع" أو اضغط على "عرض التفاصيل"</p>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة الدفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم المعاملة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الرفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for proof in payment_proofs %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-user text-green-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ proof.payment_request.student.get_full_name }}</div>
                                            <div class="text-sm text-gray-500">{{ proof.payment_request.subscription_plan.name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">${{ proof.payment_request.final_amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ proof.payment_method }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ proof.transaction_id|default:"-" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ proof.uploaded_at|date:"Y-m-d H:i" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2 space-x-reverse">
                                        <button onclick="viewProofImage('{{ proof.proof_image.url }}')" class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-image ml-1"></i>
                                            عرض الصورة
                                        </button>
                                        <button onclick="viewRequestDetails({{ proof.payment_request.id }})" class="text-purple-600 hover:text-purple-900">
                                            <i class="fas fa-eye ml-1"></i>
                                            عرض التفاصيل
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-image text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد إثباتات دفع</h3>
                                    <p class="text-gray-500">لم يرفع الطلاب أي إثباتات دفع بعد</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Old Payments Tab -->
        <div id="old-payments-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">المدفوعات القديمة</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الدورة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعلم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة الدفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الدفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for payment in payments %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                            <i class="fas fa-user text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ payment.enrollment.student.get_full_name }}</div>
                                            <div class="text-sm text-gray-500">{{ payment.enrollment.student.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ payment.enrollment.course.title }}</div>
                                    <div class="text-sm text-gray-500">{{ payment.enrollment.course.lesson_duration }} دقيقة</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ payment.enrollment.teacher.get_full_name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ payment.amount }} ر.س</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if payment.payment_method == 'credit_card' %}bg-blue-100 text-blue-800
                                        {% elif payment.payment_method == 'bank_transfer' %}bg-green-100 text-green-800
                                        {% elif payment.payment_method == 'cash' %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ payment.get_payment_method_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if payment.status == 'completed' %}bg-green-100 text-green-800
                                        {% elif payment.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif payment.status == 'processing' %}bg-blue-100 text-blue-800
                                        {% elif payment.status == 'failed' %}bg-red-100 text-red-800
                                        {% elif payment.status == 'refunded' %}bg-purple-100 text-purple-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ payment.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ payment.payment_date|date:"Y-m-d H:i" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="openStatusModal({{ payment.id }}, '{{ payment.status }}', '{{ payment.enrollment.student.get_full_name }}', '{{ payment.amount }}')"
                                            class="text-islamic-primary hover:text-islamic-light">
                                        <i class="fas fa-edit ml-1"></i>
                                        تغيير الحالة
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="8" class="px-6 py-12 text-center">
                                    <i class="fas fa-credit-card text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مدفوعات</h3>
                                    <p class="text-gray-500">لم يتم العثور على أي مدفوعات في النظام</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Payment Request Modal -->
<div id="createRequestModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">إنشاء طلب دفع جديد</h3>
                <button onclick="closeCreateRequestModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="post" action="{% url 'admin_payment_management' %}" id="createRequestForm">
                {% csrf_token %}
                <input type="hidden" name="action" value="create_request">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Student Selection -->
                    <div>
                        <label for="students" class="block text-sm font-medium text-gray-700 mb-2">الطلاب</label>
                        <select name="students" id="students" multiple class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary h-32">
                            {% for student in students %}
                            <option value="{{ student.id }}">{{ student.get_full_name }} - {{ student.email }}</option>
                            {% endfor %}
                        </select>
                        <p class="text-xs text-gray-500 mt-1">اضغط Ctrl لاختيار عدة طلاب</p>
                    </div>

                    <!-- Subscription Plan -->
                    <div>
                        <label for="subscription_plan" class="block text-sm font-medium text-gray-700 mb-2">خطة الاشتراك</label>
                        <select name="subscription_plan" id="subscription_plan" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" required>
                            <option value="">اختر خطة الاشتراك</option>
                            {% for plan in subscription_plans %}
                            <option value="{{ plan.id }}" data-price="{{ plan.price }}">
                                {{ plan.name }} - {{ plan.lessons_count }} حصة ({{ plan.lesson_duration }} دقيقة) - ${{ plan.price }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Discount -->
                    <div>
                        <label for="discount" class="block text-sm font-medium text-gray-700 mb-2">كود الخصم (اختياري)</label>
                        <select name="discount" id="discount" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary">
                            <option value="">بدون خصم</option>
                            {% for discount in discounts %}
                            <option value="{{ discount.id }}" data-type="{{ discount.discount_type }}" data-value="{{ discount.value }}">
                                {{ discount.name }} ({{ discount.code }}) -
                                {% if discount.discount_type == 'percentage' %}{{ discount.value }}%{% else %} ${{ discount.value }}{% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Due Date -->
                    <div>
                        <label for="due_date" class="block text-sm font-medium text-gray-700 mb-2">تاريخ الاستحقاق</label>
                        <input type="datetime-local" name="due_date" id="due_date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" required>
                    </div>

                    <!-- Start Date -->
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">تاريخ بداية الاشتراك</label>
                        <input type="date" name="start_date" id="start_date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" required>
                    </div>

                    <!-- End Date -->
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">تاريخ نهاية الاشتراك</label>
                        <input type="date" name="end_date" id="end_date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" required>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mt-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" placeholder="ملاحظات إضافية..."></textarea>
                </div>

                <!-- Price Summary -->
                <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-medium text-gray-900 mb-2">ملخص السعر</h4>
                    <div class="flex justify-between text-sm">
                        <span>السعر الأساسي:</span>
                        <span id="basePrice">$0</span>
                    </div>
                    <div class="flex justify-between text-sm text-green-600">
                        <span>الخصم:</span>
                        <span id="discountAmount">$0</span>
                    </div>
                    <div class="flex justify-between text-lg font-bold border-t pt-2 mt-2">
                        <span>المجموع النهائي:</span>
                        <span id="finalPrice">$0</span>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse mt-6">
                    <button type="button" onclick="closeCreateRequestModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-islamic-primary text-white rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-save ml-1"></i>
                        إنشاء طلب الدفع
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Request Details Modal -->
<div id="requestDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">تفاصيل طلب الدفع</h3>
            <button onclick="closeRequestDetailsModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div id="requestDetailsContent">
            <!-- سيتم ملء المحتوى بواسطة JavaScript -->
        </div>
    </div>
</div>

<!-- Image Viewer Modal -->
<div id="imageModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">إثبات الدفع</h3>
            <button onclick="closeImageModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="text-center">
            <img id="proofImage" src="" alt="إثبات الدفع" class="max-w-full h-auto rounded-lg shadow-lg">
        </div>
    </div>
</div>

<!-- Status Change Modal -->
<div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تغيير حالة الدفع</h3>
                <button onclick="closeStatusModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4">
                <p class="text-sm text-gray-600 mb-2">الطالب: <span id="modalStudentName" class="font-medium"></span></p>
                <p class="text-sm text-gray-600 mb-2">المبلغ: <span id="modalAmount" class="font-medium"></span> ر.س</p>
                <p class="text-sm text-gray-600">الحالة الحالية: <span id="modalCurrentStatus" class="font-medium"></span></p>
            </div>

            <form method="post" id="statusForm">
                {% csrf_token %}
                <input type="hidden" name="payment_id" id="modalPaymentId">

                <div class="mb-4">
                    <label for="new_status" class="block text-sm font-medium text-gray-700 mb-2">الحالة الجديدة</label>
                    <select name="new_status" id="new_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary">
                        {% for value, label in status_choices %}
                        <option value="{{ value }}">{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" onclick="closeStatusModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-islamic-primary text-white rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-save ml-1"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Tab Management
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all buttons
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName + '-tab').classList.remove('hidden');

    // Add active class to selected button
    document.getElementById('tab-' + tabName).classList.add('active');
}

// Create Request Modal
function openCreateRequestModal() {
    document.getElementById('createRequestModal').classList.remove('hidden');

    // Set default dates
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const nextWeek = new Date(now);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const nextMonth = new Date(now);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    document.getElementById('due_date').value = tomorrow.toISOString().slice(0, 16);
    document.getElementById('start_date').value = nextWeek.toISOString().slice(0, 10);
    document.getElementById('end_date').value = nextMonth.toISOString().slice(0, 10);
}

function closeCreateRequestModal() {
    document.getElementById('createRequestModal').classList.add('hidden');
}

// Price Calculation
function updatePriceCalculation() {
    const planSelect = document.getElementById('subscription_plan');
    const discountSelect = document.getElementById('discount');

    const selectedPlan = planSelect.options[planSelect.selectedIndex];
    const selectedDiscount = discountSelect.options[discountSelect.selectedIndex];

    let basePrice = 0;
    let discountAmount = 0;

    if (selectedPlan && selectedPlan.dataset.price) {
        basePrice = parseFloat(selectedPlan.dataset.price);
    }

    if (selectedDiscount && selectedDiscount.dataset.value) {
        const discountType = selectedDiscount.dataset.type;
        const discountValue = parseFloat(selectedDiscount.dataset.value);

        if (discountType === 'percentage') {
            discountAmount = basePrice * (discountValue / 100);
        } else {
            discountAmount = Math.min(discountValue, basePrice);
        }
    }

    const finalPrice = basePrice - discountAmount;

    document.getElementById('basePrice').textContent = '$' + basePrice.toFixed(2);
    document.getElementById('discountAmount').textContent = '$' + discountAmount.toFixed(2);
    document.getElementById('finalPrice').textContent = '$' + finalPrice.toFixed(2);
}

// Image Viewer
function viewProofImage(imageUrl) {
    document.getElementById('proofImage').src = imageUrl;
    document.getElementById('imageModal').classList.remove('hidden');
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
}

// Payment Actions
function confirmPayment(requestId) {
    if (confirm('هل أنت متأكد من تأكيد هذه الدفعة؟')) {
        // Send AJAX request to confirm payment
        fetch('{% url "admin_payment_management" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `action=confirm_payment&request_id=${requestId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function rejectPayment(requestId) {
    const reason = prompt('سبب الرفض (اختياري):');
    if (reason !== null) {
        // Send AJAX request to reject payment
        fetch('{% url "admin_payment_management" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `action=reject_payment&request_id=${requestId}&reason=${encodeURIComponent(reason)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function viewRequestDetails(requestId) {
    // جلب تفاصيل الطلب من الخادم
    fetch(`{% url "admin_payment_management" %}?action=get_request_details&request_id=${requestId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayRequestDetails(data.request);
        } else {
            alert('حدث خطأ في جلب التفاصيل: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال بالخادم');
    });
}

function displayRequestDetails(request) {
    const content = `
        <div class="space-y-6">
            <!-- معلومات الطلب الأساسية -->
            <div class="bg-blue-50 rounded-lg p-4">
                <h4 class="font-bold text-blue-900 mb-3">معلومات الطلب</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">رقم الطلب:</span>
                        <span class="text-gray-900">#${request.id}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">الحالة:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(request.status)}">
                            ${getStatusLabel(request.status)}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">تاريخ الإنشاء:</span>
                        <span class="text-gray-900">${formatDate(request.created_at)}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">تاريخ الاستحقاق:</span>
                        <span class="text-gray-900">${formatDate(request.due_date)}</span>
                    </div>
                </div>
            </div>

            <!-- معلومات الطالب -->
            <div class="bg-green-50 rounded-lg p-4">
                <h4 class="font-bold text-green-900 mb-3">معلومات الطالب</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">الاسم:</span>
                        <span class="text-gray-900">${request.student_name}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">البريد الإلكتروني:</span>
                        <span class="text-gray-900">${request.student_email}</span>
                    </div>
                </div>
            </div>

            <!-- معلومات الخطة والمبلغ -->
            <div class="bg-purple-50 rounded-lg p-4">
                <h4 class="font-bold text-purple-900 mb-3">تفاصيل الخطة والمبلغ</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">خطة الاشتراك:</span>
                        <span class="text-gray-900">${request.subscription_plan}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">عدد الحصص:</span>
                        <span class="text-gray-900">${request.lessons_count} حصة</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">مدة الحصة:</span>
                        <span class="text-gray-900">${request.lesson_duration} دقيقة</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">المبلغ الأساسي:</span>
                        <span class="text-gray-900">$${request.amount}</span>
                    </div>
                    ${request.discount_code ? `
                    <div>
                        <span class="font-medium text-gray-700">كود الخصم:</span>
                        <span class="text-green-600">${request.discount_code}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">مبلغ الخصم:</span>
                        <span class="text-green-600">$${request.discount_amount}</span>
                    </div>
                    ` : ''}
                    <div class="col-span-2 border-t pt-2">
                        <span class="font-bold text-gray-700">المبلغ النهائي:</span>
                        <span class="text-xl font-bold text-purple-600">$${request.final_amount}</span>
                    </div>
                </div>
            </div>

            <!-- تفاصيل إثبات الدفع (إذا كان موجوداً) -->
            ${request.proof ? `
            <div class="bg-yellow-50 rounded-lg p-4">
                <h4 class="font-bold text-yellow-900 mb-3">تفاصيل إثبات الدفع</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">طريقة الدفع:</span>
                        <span class="text-gray-900">${request.proof.payment_method}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">رقم المعاملة:</span>
                        <span class="text-gray-900">${request.proof.transaction_id || 'غير محدد'}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">تاريخ الرفع:</span>
                        <span class="text-gray-900">${formatDate(request.proof.uploaded_at)}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">ملاحظات الطالب:</span>
                        <span class="text-gray-900">${request.proof.notes || 'لا توجد ملاحظات'}</span>
                    </div>
                </div>

                <!-- صورة إثبات الدفع -->
                <div class="mt-4">
                    <span class="font-medium text-gray-700 block mb-2">صورة إثبات الدفع:</span>
                    <div class="border rounded-lg p-2 bg-white">
                        <img src="${request.proof.image_url}" alt="إثبات الدفع"
                             class="max-w-full h-auto max-h-64 mx-auto rounded cursor-pointer"
                             onclick="viewProofImage('${request.proof.image_url}')">
                    </div>
                    <p class="text-xs text-gray-500 mt-1 text-center">اضغط على الصورة لعرضها بحجم أكبر</p>
                </div>
            </div>
            ` : '<div class="bg-gray-50 rounded-lg p-4 text-center text-gray-500">لم يتم رفع إثبات دفع بعد</div>'}

            <!-- ملاحظات إضافية -->
            ${request.notes ? `
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-bold text-gray-900 mb-3">ملاحظات إضافية</h4>
                <p class="text-gray-700 text-sm">${request.notes}</p>
            </div>
            ` : ''}

            <!-- أزرار الإجراءات -->
            ${request.status === 'paid' ? `
            <div class="flex justify-center space-x-4 space-x-reverse pt-4 border-t">
                <button onclick="confirmPayment(${request.id}); closeRequestDetailsModal();"
                        class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-check ml-2"></i>
                    تأكيد الدفع
                </button>
                <button onclick="rejectPayment(${request.id}); closeRequestDetailsModal();"
                        class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-times ml-2"></i>
                    رفض الدفع
                </button>
            </div>
            ` : ''}
        </div>
    `;

    document.getElementById('requestDetailsContent').innerHTML = content;
    document.getElementById('requestDetailsModal').classList.remove('hidden');
}

function closeRequestDetailsModal() {
    document.getElementById('requestDetailsModal').classList.add('hidden');
}

function getStatusBadgeClass(status) {
    const classes = {
        'confirmed': 'bg-green-100 text-green-800',
        'paid': 'bg-blue-100 text-blue-800',
        'pending': 'bg-yellow-100 text-yellow-800',
        'rejected': 'bg-red-100 text-red-800',
        'expired': 'bg-gray-100 text-gray-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
}

function getStatusLabel(status) {
    const labels = {
        'confirmed': 'مؤكد',
        'paid': 'تم الدفع',
        'pending': 'في الانتظار',
        'rejected': 'مرفوض',
        'expired': 'منتهي الصلاحية'
    };
    return labels[status] || status;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
}

// Old Status Modal Functions
function openStatusModal(paymentId, currentStatus, studentName, amount) {
    document.getElementById('modalPaymentId').value = paymentId;
    document.getElementById('modalStudentName').textContent = studentName;
    document.getElementById('modalAmount').textContent = amount;
    document.getElementById('modalCurrentStatus').textContent = getStatusLabel(currentStatus);
    document.getElementById('new_status').value = currentStatus;
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

function getStatusLabel(status) {
    const statusLabels = {
        'pending': 'في الانتظار',
        'processing': 'قيد المعالجة',
        'completed': 'مكتمل',
        'failed': 'فاشل',
        'refunded': 'مسترد',
        'cancelled': 'ملغي'
    };
    return statusLabels[status] || status;
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Price calculation listeners
    document.getElementById('subscription_plan').addEventListener('change', updatePriceCalculation);
    document.getElementById('discount').addEventListener('change', updatePriceCalculation);

    // Modal close listeners
    document.getElementById('createRequestModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeCreateRequestModal();
        }
    });

    document.getElementById('imageModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeImageModal();
        }
    });

    document.getElementById('statusModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeStatusModal();
        }
    });

    document.getElementById('requestDetailsModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeRequestDetailsModal();
        }
    });
});
</script>
{% endblock %}
