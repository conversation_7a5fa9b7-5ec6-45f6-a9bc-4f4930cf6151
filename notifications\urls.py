from django.urls import path
from . import views

app_name = 'notifications'

urlpatterns = [
    # Notifications
    path('', views.notification_list, name='list'),
    path('<int:notification_id>/read/', views.mark_notification_read, name='mark_read'),
    path('mark-all-read/', views.mark_all_notifications_read, name='mark_all_read'),

    # Messages
    path('messages/', views.message_list, name='message_list'),
    path('messages/compose/', views.compose_message, name='compose_message'),
    path('messages/<int:message_id>/', views.message_detail, name='message_detail'),
    path('messages/<int:message_id>/reply/', views.reply_message, name='reply_message'),

    # Bulk Messages (Admin only)
    path('messages/bulk/compose/', views.compose_bulk_message, name='compose_bulk_message'),
    path('messages/bulk/', views.bulk_message_list, name='bulk_message_list'),

    # Support functionality moved to support app
]
