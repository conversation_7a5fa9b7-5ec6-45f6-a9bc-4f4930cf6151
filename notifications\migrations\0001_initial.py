# Generated by Django 4.2.7 on 2025-05-22 12:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('private', 'رسالة خاصة'), ('support', 'رسالة دعم'), ('announcement', 'إعلان')], default='private', max_length=15, verbose_name='نوع الرسالة')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
            ],
            options={
                'verbose_name': 'رسالة',
                'verbose_name_plural': 'الرسائل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('lesson_reminder', 'تذكير بالحصة'), ('lesson_cancelled', 'إلغاء الحصة'), ('lesson_rescheduled', 'إعادة جدولة الحصة'), ('payment_reminder', 'تذكير بالدفع'), ('course_completed', 'اكتمال الدورة'), ('new_message', 'رسالة جديدة'), ('system_announcement', 'إعلان النظام'), ('rating_request', 'طلب تقييم'), ('support_ticket', 'تذكرة دعم')], max_length=20, verbose_name='نوع الإشعار')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('urgent', 'عاجل')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت القراءة')),
                ('action_url', models.URLField(blank=True, null=True, verbose_name='رابط الإجراء')),
                ('action_text', models.CharField(blank=True, max_length=100, null=True, verbose_name='نص الإجراء')),
                ('email_sent', models.BooleanField(default=False, verbose_name='تم إرسال إيميل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SupportTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket_number', models.CharField(max_length=20, unique=True, verbose_name='رقم التذكرة')),
                ('category', models.CharField(choices=[('technical', 'مشكلة تقنية'), ('account', 'مشكلة في الحساب'), ('payment', 'مشكلة في الدفع'), ('lesson', 'مشكلة في الحصة'), ('general', 'استفسار عام'), ('complaint', 'شكوى'), ('suggestion', 'اقتراح')], max_length=15, verbose_name='الفئة')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('urgent', 'عاجل')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('open', 'مفتوح'), ('in_progress', 'قيد المعالجة'), ('waiting_response', 'في انتظار الرد'), ('resolved', 'محلول'), ('closed', 'مغلق')], default='open', max_length=20, verbose_name='الحالة')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('resolution', models.TextField(blank=True, null=True, verbose_name='الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
            ],
            options={
                'verbose_name': 'تذكرة دعم',
                'verbose_name_plural': 'تذاكر الدعم',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TicketReply',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('is_internal', models.BooleanField(default=False, verbose_name='ملاحظة داخلية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرد')),
                ('ticket', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='notifications.supportticket', verbose_name='التذكرة')),
            ],
            options={
                'verbose_name': 'رد التذكرة',
                'verbose_name_plural': 'ردود التذاكر',
                'ordering': ['created_at'],
            },
        ),
    ]
