#!/usr/bin/env python
"""
Script لاختبار نظام الحظر وإعادة التسجيل
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania_lms.settings')
django.setup()

from users.models import User
from django.utils import timezone
from datetime import timedelta

def create_test_scenarios():
    """إنشاء سيناريوهات اختبار مختلفة"""
    
    print("🧪 إنشاء سيناريوهات اختبار نظام الحظر وإعادة التسجيل")
    print("=" * 70)
    
    # حذف المستخدمين التجريبيين الموجودين
    test_users = ['rejected_user', 'banned_temp_user', 'banned_perm_user', 'normal_user']
    for username in test_users:
        if User.objects.filter(username=username).exists():
            User.objects.filter(username=username).delete()
            print(f"  🗑️ تم حذف المستخدم التجريبي: {username}")
    
    # السيناريو 1: مستخدم مرفوض يمكنه إعادة التسجيل
    rejected_user = User.objects.create_user(
        username='rejected_user',
        email='<EMAIL>',
        password='test123',
        first_name='أحمد',
        last_name='المرفوض',
        user_type='teacher',
        phone='0501111111',
        is_active=False,
        verification_status='rejected',
        rejection_reason='لا تتوفر المؤهلات المطلوبة'
    )
    print(f"  ✅ مستخدم مرفوض: {rejected_user.username} - يمكنه إعادة التسجيل")
    
    # السيناريو 2: مستخدم محظور مؤقتاً
    banned_temp_user = User.objects.create_user(
        username='banned_temp_user',
        email='<EMAIL>',
        password='test123',
        first_name='فاطمة',
        last_name='المحظورة مؤقتاً',
        user_type='student',
        phone='0502222222',
        is_active=False,
        verification_status='rejected'
    )
    # حظر مؤقت لمدة 7 أيام
    banned_temp_user.ban_user(
        admin_user=None,  # سنضع None للاختبار
        ban_type='temporary',
        reason='مخالفة قوانين المنصة',
        banned_until=timezone.now() + timedelta(days=7)
    )
    print(f"  ⏰ مستخدم محظور مؤقتاً: {banned_temp_user.username} - لا يمكنه إعادة التسجيل")
    
    # السيناريو 3: مستخدم محظور نهائياً
    banned_perm_user = User.objects.create_user(
        username='banned_perm_user',
        email='<EMAIL>',
        password='test123',
        first_name='محمد',
        last_name='المحظور نهائياً',
        user_type='teacher',
        phone='0503333333',
        is_active=False,
        verification_status='rejected'
    )
    # حظر نهائي
    banned_perm_user.ban_user(
        admin_user=None,
        ban_type='permanent',
        reason='انتهاك خطير لسياسات المنصة'
    )
    print(f"  🚫 مستخدم محظور نهائياً: {banned_perm_user.username} - لا يمكنه إعادة التسجيل")
    
    # السيناريو 4: مستخدم عادي للمقارنة
    normal_user = User.objects.create_user(
        username='normal_user',
        email='<EMAIL>',
        password='test123',
        first_name='علي',
        last_name='العادي',
        user_type='student',
        phone='0504444444',
        is_active=True,
        verification_status='approved'
    )
    print(f"  ✅ مستخدم عادي: {normal_user.username} - حساب نشط")
    
    return [rejected_user, banned_temp_user, banned_perm_user, normal_user]

def test_registration_logic():
    """اختبار منطق إعادة التسجيل"""
    
    print("\n🔍 اختبار منطق إعادة التسجيل:")
    print("-" * 50)
    
    test_cases = [
        ('rejected_user', '<EMAIL>'),
        ('banned_temp_user', '<EMAIL>'),
        ('banned_perm_user', '<EMAIL>'),
        ('normal_user', '<EMAIL>'),
    ]
    
    for username, email in test_cases:
        user = User.objects.filter(username=username).first()
        if user:
            can_register = user.can_register_again()
            is_banned = user.is_currently_banned()
            ban_status = user.get_ban_status_display()
            
            print(f"\n👤 {username}:")
            print(f"   حالة التحقق: {user.verification_status}")
            print(f"   محظور حالياً: {is_banned}")
            print(f"   حالة الحظر: {ban_status}")
            print(f"   يمكن إعادة التسجيل: {can_register}")
            
            if can_register:
                print(f"   ✅ يمكن لهذا المستخدم إعادة التسجيل بنفس البيانات")
            else:
                if is_banned:
                    print(f"   ❌ لا يمكن إعادة التسجيل - محظور")
                else:
                    print(f"   ❌ لا يمكن إعادة التسجيل - حساب نشط أو معلق")

def test_ban_system():
    """اختبار نظام الحظر"""
    
    print("\n🔒 اختبار نظام الحظر:")
    print("-" * 50)
    
    # اختبار المستخدمين المحظورين
    banned_users = User.objects.filter(is_banned=True)
    
    for user in banned_users:
        print(f"\n🚫 {user.username}:")
        print(f"   نوع الحظر: {user.ban_type}")
        print(f"   سبب الحظر: {user.ban_reason}")
        print(f"   تاريخ الحظر: {user.banned_at}")
        if user.banned_until:
            print(f"   ينتهي الحظر: {user.banned_until}")
        print(f"   محظور حالياً: {user.is_currently_banned()}")
        print(f"   حالة الحظر: {user.get_ban_status_display()}")

def display_test_credentials():
    """عرض بيانات الاختبار"""
    
    print("\n🔑 بيانات تسجيل الدخول للاختبار:")
    print("=" * 70)
    
    scenarios = [
        {
            'title': '👤 مستخدم مرفوض (يمكن إعادة التسجيل)',
            'username': 'rejected_user',
            'password': 'test123',
            'email': '<EMAIL>',
            'status': 'مرفوض - يمكن إعادة التسجيل',
            'test': 'جرب التسجيل بنفس البيانات'
        },
        {
            'title': '⏰ مستخدم محظور مؤقتاً',
            'username': 'banned_temp_user',
            'password': 'test123',
            'email': '<EMAIL>',
            'status': 'محظور مؤقتاً - لا يمكن إعادة التسجيل',
            'test': 'جرب التسجيل - يجب أن يظهر رسالة حظر'
        },
        {
            'title': '🚫 مستخدم محظور نهائياً',
            'username': 'banned_perm_user',
            'password': 'test123',
            'email': '<EMAIL>',
            'status': 'محظور نهائياً - لا يمكن إعادة التسجيل',
            'test': 'جرب التسجيل - يجب أن يظهر رسالة حظر'
        },
        {
            'title': '✅ مستخدم عادي',
            'username': 'normal_user',
            'password': 'test123',
            'email': '<EMAIL>',
            'status': 'نشط - لا يمكن إعادة التسجيل',
            'test': 'جرب التسجيل - يجب أن يظهر "البريد مسجل بالفعل"'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['title']}:")
        print(f"  اسم المستخدم: {scenario['username']}")
        print(f"  كلمة المرور: {scenario['password']}")
        print(f"  البريد الإلكتروني: {scenario['email']}")
        print(f"  الحالة: {scenario['status']}")
        print(f"  الاختبار: {scenario['test']}")

def display_test_urls():
    """عرض روابط الاختبار"""
    
    print("\n🔗 روابط الاختبار:")
    print("=" * 70)
    
    print("\n📝 صفحات التسجيل والدخول:")
    print("  التسجيل: http://localhost:8080/register/")
    print("  تسجيل الدخول: http://localhost:8080/login/")
    
    print("\n👨‍💼 لوحة تحكم المدير:")
    print("  طلبات التحقق: http://localhost:8080/dashboard/admin/user-verifications/")
    print("  إدارة المستخدمين: http://localhost:8080/dashboard/admin/users/")
    
    print("\n📄 صفحات الحالات:")
    print("  انتظار المراجعة: http://localhost:8080/dashboard/verification-pending/")
    print("  رفض الطلب: http://localhost:8080/dashboard/verification-rejected/")
    print("  حساب محظور: http://localhost:8080/dashboard/banned/")

def main():
    """الدالة الرئيسية"""
    
    print("🎯 اختبار نظام الحظر وإعادة التسجيل")
    print("=" * 70)
    
    try:
        # إنشاء سيناريوهات الاختبار
        test_users = create_test_scenarios()
        
        # اختبار منطق إعادة التسجيل
        test_registration_logic()
        
        # اختبار نظام الحظر
        test_ban_system()
        
        # عرض بيانات الاختبار
        display_test_credentials()
        display_test_urls()
        
        print("\n" + "=" * 70)
        print("✅ تم إعداد بيانات الاختبار بنجاح!")
        
        print("\n🧪 خطوات الاختبار المقترحة:")
        print("1. جرب تسجيل مستخدم مرفوض بنفس البيانات (يجب أن ينجح)")
        print("2. جرب تسجيل مستخدم محظور (يجب أن يفشل مع رسالة حظر)")
        print("3. جرب تسجيل الدخول بمستخدم محظور (يجب توجيه لصفحة الحظر)")
        print("4. اختبر خيارات الحظر في لوحة المدير")
        print("5. اختبر إلغاء الحظر من لوحة المدير")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
