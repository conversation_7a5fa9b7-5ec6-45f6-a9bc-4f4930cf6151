from django.contrib import admin
from .models import SystemLog

@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = ('action', 'user', 'timestamp', 'ip_address')
    list_filter = ('action', 'timestamp')
    search_fields = ('action', 'details', 'user__username', 'user__email')
    readonly_fields = ('timestamp', 'ip_address', 'user', 'action', 'details', '_data')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False
