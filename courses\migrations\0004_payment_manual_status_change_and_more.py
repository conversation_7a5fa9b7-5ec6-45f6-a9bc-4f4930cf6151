# Generated by Django 4.2.7 on 2025-05-22 16:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0003_payment_teacherrating_teacherearning'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='manual_status_change',
            field=models.BooleanField(default=False, verbose_name='تم تغيير الحالة يدوياً'),
        ),
        migrations.AddField(
            model_name='payment',
            name='status_changed_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ تغيير الحالة'),
        ),
        migrations.AddField(
            model_name='payment',
            name='status_changed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_status_changes', to=settings.AUTH_USER_MODEL, verbose_name='تم تغيير الحالة بواسطة'),
        ),
    ]
