{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة أسعار المعلمين - قرآنيا{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">إدارة أسعار المعلمين</h1>
                    <p class="mt-2 text-gray-600">تحديد أسعار ونسب عمولة المعلمين حسب مدة الحصة</p>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="{% url 'admin_dashboard' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- Teachers List -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {% for teacher in teachers %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-islamic-light-blue rounded-full flex items-center justify-center ml-4">
                        <i class="fas fa-chalkboard-teacher text-islamic-primary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">{{ teacher.get_full_name }}</h3>
                        <p class="text-sm text-gray-600">{{ teacher.email }}</p>
                    </div>
                </div>

                <form method="post" class="space-y-4">
                    {% csrf_token %}
                    <input type="hidden" name="teacher_id" value="{{ teacher.id }}">

                    <!-- أسعار الحصص -->
                    <div class="border-b border-gray-200 pb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3">أسعار الحصص</h4>
                        <div class="grid grid-cols-3 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">30 دقيقة</label>
                                <div class="relative">
                                    <input type="number" name="rate_30" value="{{ teacher.hourly_rate_30|default:'50.00' }}"
                                           step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary">
                                    <span class="absolute left-3 top-2 text-gray-500 text-sm">ر.س</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">45 دقيقة</label>
                                <div class="relative">
                                    <input type="number" name="rate_45" value="{{ teacher.hourly_rate_45|default:'70.00' }}"
                                           step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary">
                                    <span class="absolute left-3 top-2 text-gray-500 text-sm">ر.س</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">60 دقيقة</label>
                                <div class="relative">
                                    <input type="number" name="rate_60" value="{{ teacher.hourly_rate_60|default:'90.00' }}"
                                           step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary">
                                    <span class="absolute left-3 top-2 text-gray-500 text-sm">ر.س</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نسب العمولة -->
                    <div class="pb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3">نسب العمولة للمعلم</h4>
                        <div class="grid grid-cols-3 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">30 دقيقة</label>
                                <div class="relative">
                                    <input type="number" name="commission_30" value="{{ teacher.commission_rate_30|default:'70.00' }}"
                                           step="0.01" min="0" max="100" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary">
                                    <span class="absolute left-3 top-2 text-gray-500 text-sm">%</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">45 دقيقة</label>
                                <div class="relative">
                                    <input type="number" name="commission_45" value="{{ teacher.commission_rate_45|default:'70.00' }}"
                                           step="0.01" min="0" max="100" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary">
                                    <span class="absolute left-3 top-2 text-gray-500 text-sm">%</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">60 دقيقة</label>
                                <div class="relative">
                                    <input type="number" name="commission_60" value="{{ teacher.commission_rate_60|default:'70.00' }}"
                                           step="0.01" min="0" max="100" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary">
                                    <span class="absolute left-3 top-2 text-gray-500 text-sm">%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأرباح الفعلية هذا الشهر -->
                    <div class="bg-blue-50 rounded-lg p-3 mb-3">
                        <h5 class="text-sm font-medium text-blue-700 mb-2">الأرباح الفعلية هذا الشهر:</h5>
                        <div class="grid grid-cols-3 gap-2 text-xs">
                            <div class="text-center">
                                <span class="block text-gray-600">30 دقيقة</span>
                                <span class="block font-medium text-blue-600">
                                    {{ teacher.earnings_data.30.completed_lessons }} حصة
                                </span>
                                <span class="block font-bold text-green-600">
                                    {{ teacher.earnings_data.30.earnings|floatformat:0 }} ر.س
                                </span>
                            </div>
                            <div class="text-center">
                                <span class="block text-gray-600">45 دقيقة</span>
                                <span class="block font-medium text-blue-600">
                                    {{ teacher.earnings_data.45.completed_lessons }} حصة
                                </span>
                                <span class="block font-bold text-green-600">
                                    {{ teacher.earnings_data.45.earnings|floatformat:0 }} ر.س
                                </span>
                            </div>
                            <div class="text-center">
                                <span class="block text-gray-600">60 دقيقة</span>
                                <span class="block font-medium text-blue-600">
                                    {{ teacher.earnings_data.60.completed_lessons }} حصة
                                </span>
                                <span class="block font-bold text-green-600">
                                    {{ teacher.earnings_data.60.earnings|floatformat:0 }} ر.س
                                </span>
                            </div>
                        </div>
                        <div class="border-t border-blue-200 mt-2 pt-2 text-center">
                            <span class="text-sm font-bold text-blue-700">
                                إجمالي الأرباح: {{ teacher.total_monthly_earnings|floatformat:0 }} ر.س
                            </span>
                        </div>
                    </div>

                    <!-- أرباح متوقعة للحصة الواحدة -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <h5 class="text-sm font-medium text-gray-700 mb-2">ربح الحصة الواحدة:</h5>
                        <div class="grid grid-cols-3 gap-2 text-xs">
                            <div class="text-center">
                                <span class="block text-gray-600">30 دقيقة</span>
                                <span class="block font-medium text-green-600" id="earning_30_{{ teacher.id }}">
                                    {{ teacher.hourly_rate_30|default:'50.00'|floatformat:0 }} × {{ teacher.commission_rate_30|default:'70'|floatformat:0 }}% =
                                    {% widthratio teacher.hourly_rate_30|default:'50.00' 100 teacher.commission_rate_30|default:'70' %} ر.س
                                </span>
                            </div>
                            <div class="text-center">
                                <span class="block text-gray-600">45 دقيقة</span>
                                <span class="block font-medium text-green-600" id="earning_45_{{ teacher.id }}">
                                    {{ teacher.hourly_rate_45|default:'70.00'|floatformat:0 }} × {{ teacher.commission_rate_45|default:'70'|floatformat:0 }}% =
                                    {% widthratio teacher.hourly_rate_45|default:'70.00' 100 teacher.commission_rate_45|default:'70' %} ر.س
                                </span>
                            </div>
                            <div class="text-center">
                                <span class="block text-gray-600">60 دقيقة</span>
                                <span class="block font-medium text-green-600" id="earning_60_{{ teacher.id }}">
                                    {{ teacher.hourly_rate_60|default:'90.00'|floatformat:0 }} × {{ teacher.commission_rate_60|default:'70'|floatformat:0 }}% =
                                    {% widthratio teacher.hourly_rate_60|default:'90.00' 100 teacher.commission_rate_60|default:'70' %} ر.س
                                </span>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="w-full bg-islamic-primary text-white py-2 px-4 rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-save ml-2"></i>
                        حفظ التغييرات
                    </button>
                </form>
            </div>
            {% empty %}
            <div class="col-span-full text-center py-12">
                <i class="fas fa-chalkboard-teacher text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا يوجد معلمون</h3>
                <p class="text-gray-500">لم يتم العثور على أي معلمين في النظام</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
// تحديث الأرباح المتوقعة عند تغيير القيم
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        const inputs = form.querySelectorAll('input[type="number"]');
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                updateExpectedEarnings(form);
            });
        });
    });

    function updateExpectedEarnings(form) {
        const teacherId = form.querySelector('input[name="teacher_id"]').value;

        ['30', '45', '60'].forEach(duration => {
            const rate = parseFloat(form.querySelector(`input[name="rate_${duration}"]`).value) || 0;
            const commission = parseFloat(form.querySelector(`input[name="commission_${duration}"]`).value) || 0;
            const earning = (rate * commission / 100).toFixed(2);

            const earningElement = document.getElementById(`earning_${duration}_${teacherId}`);
            if (earningElement) {
                earningElement.innerHTML = `${rate.toFixed(0)} × ${commission.toFixed(0)}% = ${earning} ر.س`;
            }
        });
    }
});
</script>
{% endblock %}
