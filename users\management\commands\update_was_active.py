from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'تحديث حقل was_active للمستخدمين الحاليين'

    def handle(self, *args, **options):
        # تحديث المستخدمين النشطين
        active_users = User.objects.filter(is_active=True)
        active_users.update(was_active=True)
        
        # تحديث المستخدمين المعتمدين
        approved_users = User.objects.filter(verification_status='approved')
        approved_users.update(was_active=True)
        
        self.stdout.write(self.style.SUCCESS(f'تم تحديث {active_users.count()} مستخدم نشط و {approved_users.count()} مستخدم معتمد'))
