{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الاشتراكات - قرآنيا{% endblock %}

{% block extra_css %}
<style>
    .tab-button {
        @apply px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 transition-colors duration-200;
    }

    .tab-button.active {
        @apply text-islamic-primary border-islamic-primary;
    }

    .tab-content {
        @apply transition-all duration-300;
    }

    .tab-content.hidden {
        @apply opacity-0 pointer-events-none;
    }

    .status-badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">إدارة الاشتراكات</h1>
                    <p class="mt-2 text-gray-600">تابع طلبات الدفع واشتراكاتك النشطة</p>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="{% url 'student_dashboard' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">طلبات في الانتظار</p>
                        <p class="text-2xl font-bold text-gray-900">{{ pending_requests }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-upload text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">تم رفع الإثبات</p>
                        <p class="text-2xl font-bold text-gray-900">{{ paid_requests }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">مدفوعات مؤكدة</p>
                        <p class="text-2xl font-bold text-gray-900">{{ confirmed_requests }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">إجمالي المدفوع</p>
                        <p class="text-2xl font-bold text-gray-900">${{ total_spent }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8 space-x-reverse">
                <button onclick="showTab('payment-requests')" id="tab-payment-requests" class="tab-button active">
                    <i class="fas fa-file-invoice ml-2"></i>
                    طلبات الدفع
                </button>
                <button onclick="showTab('active-subscriptions')" id="tab-active-subscriptions" class="tab-button">
                    <i class="fas fa-star ml-2"></i>
                    الاشتراكات النشطة
                </button>
                <button onclick="showTab('subscription-history')" id="tab-subscription-history" class="tab-button">
                    <i class="fas fa-history ml-2"></i>
                    تاريخ الاشتراكات
                </button>
            </nav>
        </div>

        <!-- Payment Requests Tab -->
        <div id="payment-requests-tab" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">طلبات الدفع</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">خطة الاشتراك</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخصم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ النهائي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الاستحقاق</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for request in payment_requests %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ request.subscription_plan.name }}</div>
                                    <div class="text-sm text-gray-500">{{ request.subscription_plan.lessons_count }} حصة - {{ request.subscription_plan.lesson_duration }} دقيقة</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">${{ request.amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if request.discount %}
                                        <div class="text-sm text-green-600">${{ request.discount_amount }}</div>
                                        <div class="text-xs text-gray-500">{{ request.discount.code }}</div>
                                    {% else %}
                                        <span class="text-sm text-gray-400">-</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-bold text-gray-900">${{ request.final_amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="status-badge
                                        {% if request.status == 'confirmed' %}bg-green-100 text-green-800
                                        {% elif request.status == 'paid' %}bg-blue-100 text-blue-800
                                        {% elif request.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif request.status == 'rejected' %}bg-red-100 text-red-800
                                        {% elif request.status == 'expired' %}bg-gray-100 text-gray-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ request.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ request.due_date|date:"Y-m-d H:i" }}
                                    {% if request.is_expired %}
                                        <span class="text-red-500 text-xs block">منتهي الصلاحية</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    {% if request.status == 'pending' %}
                                        <button onclick="openUploadModal({{ request.id }}, '{{ request.subscription_plan.name }}', '{{ request.final_amount }}')"
                                                class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-upload ml-1"></i>
                                            رفع إثبات الدفع
                                        </button>
                                    {% elif request.status == 'paid' %}
                                        <span class="text-blue-600">
                                            <i class="fas fa-clock ml-1"></i>
                                            قيد المراجعة
                                        </span>
                                    {% elif request.status == 'confirmed' %}
                                        <span class="text-green-600">
                                            <i class="fas fa-check ml-1"></i>
                                            مؤكد
                                        </span>
                                    {% elif request.status == 'rejected' %}
                                        <button onclick="openUploadModal({{ request.id }}, '{{ request.subscription_plan.name }}', '{{ request.final_amount }}')"
                                                class="text-orange-600 hover:text-orange-900">
                                            <i class="fas fa-redo ml-1"></i>
                                            إعادة الرفع
                                        </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center">
                                    <i class="fas fa-file-invoice text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات دفع</h3>
                                    <p class="text-gray-500">لم يتم إنشاء أي طلبات دفع لك بعد</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Active Subscriptions Tab -->
        <div id="active-subscriptions-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">الاشتراكات النشطة</h3>
                </div>

                <div class="p-6">
                    {% for subscription in active_subscriptions %}
                    <div class="bg-gradient-to-r from-islamic-primary to-islamic-secondary rounded-lg p-6 mb-6 text-white">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h4 class="text-xl font-bold">{{ subscription.subscription_plan.name }}</h4>
                                <p class="text-islamic-light">{{ subscription.subscription_plan.description }}</p>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold">${{ subscription.payment_request.final_amount }}</div>
                                <div class="text-sm text-islamic-light">المبلغ المدفوع</div>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold">{{ subscription.lessons_remaining }}</div>
                                <div class="text-sm text-islamic-light">حصة متبقية</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold">{{ subscription.lessons_used }}</div>
                                <div class="text-sm text-islamic-light">حصة مستخدمة</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold">{{ subscription.subscription_plan.lesson_duration }}</div>
                                <div class="text-sm text-islamic-light">دقيقة/حصة</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold">{{ subscription.end_date|date:"M d" }}</div>
                                <div class="text-sm text-islamic-light">تاريخ الانتهاء</div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between text-sm mb-1">
                                <span>التقدم</span>
                                <span>{{ subscription.lessons_used }}/{{ subscription.subscription_plan.lessons_count }}</span>
                            </div>
                            <div class="w-full bg-white bg-opacity-30 rounded-full h-2">
                                <div class="bg-white h-2 rounded-full" style="width: {% widthratio subscription.lessons_used subscription.subscription_plan.lessons_count 100 %}%"></div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center">
                            <div class="text-sm">
                                <span class="text-islamic-light">من:</span> {{ subscription.start_date|date:"Y-m-d" }}
                                <span class="text-islamic-light mr-4">إلى:</span> {{ subscription.end_date|date:"Y-m-d" }}
                            </div>
                            {% if subscription.is_expiring_soon %}
                                <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm">
                                    <i class="fas fa-exclamation-triangle ml-1"></i>
                                    ينتهي قريباً
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-12">
                        <i class="fas fa-star text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد اشتراكات نشطة</h3>
                        <p class="text-gray-500">ستظهر اشتراكاتك النشطة هنا بعد تأكيد الدفع</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Subscription History Tab -->
        <div id="subscription-history-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">تاريخ الاشتراكات</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخطة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المدة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحصص المستخدمة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الانتهاء</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for subscription in expired_subscriptions %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ subscription.subscription_plan.name }}</div>
                                    <div class="text-sm text-gray-500">{{ subscription.subscription_plan.lesson_duration }} دقيقة/حصة</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ subscription.start_date|date:"Y-m-d" }} - {{ subscription.end_date|date:"Y-m-d" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ subscription.lessons_used }}/{{ subscription.subscription_plan.lessons_count }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    ${{ subscription.payment_request.final_amount }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="status-badge
                                        {% if subscription.status == 'expired' %}bg-gray-100 text-gray-800
                                        {% elif subscription.status == 'cancelled' %}bg-red-100 text-red-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ subscription.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ subscription.end_date|date:"Y-m-d" }}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-history text-gray-400 text-6xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا يوجد تاريخ اشتراكات</h3>
                                    <p class="text-gray-500">ستظهر اشتراكاتك المنتهية هنا</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Payment Proof Modal -->
<div id="uploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">رفع إثبات الدفع</h3>
                <button onclick="closeUploadModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-2">تفاصيل الطلب</h4>
                <p class="text-sm text-blue-800">الخطة: <span id="modalPlanName" class="font-medium"></span></p>
                <p class="text-sm text-blue-800">المبلغ المطلوب: <span id="modalAmount" class="font-medium"></span></p>
            </div>

            <form method="post" enctype="multipart/form-data" id="uploadForm">
                {% csrf_token %}
                <input type="hidden" name="upload_proof" value="1">
                <input type="hidden" name="request_id" id="modalRequestId">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Payment Method -->
                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع</label>
                        <select name="payment_method" id="payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="credit_card">بطاقة ائتمان</option>
                            <option value="paypal">PayPal</option>
                            <option value="western_union">ويسترن يونيون</option>
                            <option value="cash_deposit">إيداع نقدي</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>

                    <!-- Transaction ID -->
                    <div>
                        <label for="transaction_id" class="block text-sm font-medium text-gray-700 mb-2">رقم المعاملة (اختياري)</label>
                        <input type="text" name="transaction_id" id="transaction_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" placeholder="رقم المعاملة أو المرجع">
                    </div>
                </div>

                <!-- Proof Image -->
                <div class="mt-4">
                    <label for="proof_image" class="block text-sm font-medium text-gray-700 mb-2">صورة إثبات الدفع</label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="proof_image" class="relative cursor-pointer bg-white rounded-md font-medium text-islamic-primary hover:text-islamic-secondary focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-islamic-primary">
                                    <span>ارفع صورة</span>
                                    <input id="proof_image" name="proof_image" type="file" class="sr-only" accept="image/*" required>
                                </label>
                                <p class="pr-1">أو اسحب وأفلت</p>
                            </div>
                            <p class="text-xs text-gray-500">PNG, JPG, GIF حتى 10MB</p>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mt-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-islamic-primary focus:border-islamic-primary" placeholder="أي ملاحظات أو تفاصيل إضافية..."></textarea>
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse mt-6">
                    <button type="button" onclick="closeUploadModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-islamic-primary text-white rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-upload ml-1"></i>
                        رفع إثبات الدفع
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Tab Management
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all buttons
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName + '-tab').classList.remove('hidden');

    // Add active class to selected button
    document.getElementById('tab-' + tabName).classList.add('active');
}

// Upload Modal
function openUploadModal(requestId, planName, amount) {
    document.getElementById('modalRequestId').value = requestId;
    document.getElementById('modalPlanName').textContent = planName;
    document.getElementById('modalAmount').textContent = '$' + amount;
    document.getElementById('uploadModal').classList.remove('hidden');
}

function closeUploadModal() {
    document.getElementById('uploadModal').classList.add('hidden');
    document.getElementById('uploadForm').reset();
}

// File upload preview
document.getElementById('proof_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // You can add image preview here if needed
            console.log('Image selected:', file.name);
        };
        reader.readAsDataURL(file);
    }
});

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Modal close listeners
    document.getElementById('uploadModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeUploadModal();
        }
    });
});
</script>
{% endblock %}
