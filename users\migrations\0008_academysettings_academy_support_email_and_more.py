# Generated by Django 4.2.7 on 2025-05-23 21:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0007_academysettings'),
    ]

    operations = [
        migrations.AddField(
            model_name='academysettings',
            name='academy_support_email',
            field=models.EmailField(blank=True, default='<EMAIL>', max_length=100, null=True, verbose_name='البريد الإلكتروني للدعم الفني'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='academy_whatsapp',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم واتساب الأكاديمية'),
        ),
        migrations.AddField(
            model_name='academysettings',
            name='academy_working_hours',
            field=models.TextField(blank=True, null=True, verbose_name='ساعات العمل'),
        ),
    ]
