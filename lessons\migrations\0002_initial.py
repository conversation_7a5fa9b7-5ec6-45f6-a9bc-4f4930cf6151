# Generated by Django 4.2.7 on 2025-05-22 12:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0002_initial'),
        ('lessons', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='lessonrating',
            name='student',
            field=models.ForeignKey(limit_choices_to={'user_type': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='lesson_ratings', to=settings.AUTH_USER_MODEL, verbose_name='الطالب'),
        ),
        migrations.AddField(
            model_name='lessoncontent',
            name='lesson',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_records', to='lessons.lesson', verbose_name='الحصة'),
        ),
        migrations.AddField(
            model_name='lessoncontent',
            name='recorded_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recorded_lesson_content', to=settings.AUTH_USER_MODEL, verbose_name='مسجل بواسطة'),
        ),
        migrations.AddField(
            model_name='lessonattendance',
            name='lesson',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='lessons.lesson', verbose_name='الحصة'),
        ),
        migrations.AddField(
            model_name='lessonattendance',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_attendance', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم'),
        ),
        migrations.AddField(
            model_name='lesson',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_lessons', to=settings.AUTH_USER_MODEL, verbose_name='منشئ الحصة'),
        ),
        migrations.AddField(
            model_name='lesson',
            name='enrollment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lessons', to='courses.enrollment', verbose_name='التسجيل'),
        ),
        migrations.AlterUniqueTogether(
            name='lessonattendance',
            unique_together={('lesson', 'user')},
        ),
    ]
