# Generated by Django 4.2.7 on 2025-05-22 12:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الدورة')),
                ('description', models.TextField(verbose_name='وصف الدورة')),
                ('course_type', models.CharField(choices=[('quran_memorization', 'حفظ القرآن'), ('quran_recitation', 'تلاوة القرآن'), ('quran_review', 'مراجعة القرآن'), ('islamic_studies', 'دراسات إسلامية')], max_length=20, verbose_name='نوع الدورة')),
                ('difficulty_level', models.CharField(choices=[('beginner', 'مبتدئ'), ('intermediate', 'متوسط'), ('advanced', 'متقدم')], max_length=15, verbose_name='مستوى الصعوبة')),
                ('duration_weeks', models.PositiveIntegerField(verbose_name='مدة الدورة (بالأسابيع)')),
                ('lessons_per_week', models.PositiveIntegerField(default=2, verbose_name='عدد الحصص في الأسبوع')),
                ('lesson_duration', models.PositiveIntegerField(choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')], default=45, verbose_name='مدة الحصة (بالدقائق)')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الدورة')),
                ('max_students', models.PositiveIntegerField(default=1, verbose_name='الحد الأقصى للطلاب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'دورة',
                'verbose_name_plural': 'الدورات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Enrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('active', 'نشط'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('suspended', 'معلق')], default='pending', max_length=15, verbose_name='الحالة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النهاية')),
                ('amount_paid', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ المدفوع')),
                ('remaining_lessons', models.PositiveIntegerField(verbose_name='الحصص المتبقية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التسجيل')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'تسجيل',
                'verbose_name_plural': 'التسجيلات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StudentProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('progress_type', models.CharField(choices=[('memorization', 'حفظ'), ('review', 'مراجعة'), ('recitation', 'تلاوة')], max_length=15, verbose_name='نوع التقدم')),
                ('surah_name', models.CharField(max_length=100, verbose_name='اسم السورة')),
                ('from_verse', models.PositiveIntegerField(verbose_name='من الآية')),
                ('to_verse', models.PositiveIntegerField(verbose_name='إلى الآية')),
                ('completion_percentage', models.PositiveIntegerField(default=0, verbose_name='نسبة الإنجاز')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('recorded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التسجيل')),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress_records', to='courses.enrollment', verbose_name='التسجيل')),
            ],
            options={
                'verbose_name': 'تقدم الطالب',
                'verbose_name_plural': 'تقدم الطلاب',
                'ordering': ['-recorded_at'],
            },
        ),
    ]
