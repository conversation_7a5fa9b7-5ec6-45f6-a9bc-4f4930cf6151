# Generated by Django 4.2.7 on 2025-05-22 19:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('messaging', '0002_chatmessage_delete_message'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='conversation',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='conversation',
            name='participant1',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conversations_as_participant1', to=settings.AUTH_USER_MODEL, verbose_name='المشارك الأول'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='participant2',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conversations_as_participant2', to=settings.AUTH_USER_MODEL, verbose_name='المشارك الثاني'),
        ),
        migrations.AlterField(
            model_name='conversation',
            name='student',
            field=models.ForeignKey(blank=True, limit_choices_to={'user_type': 'student'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='student_conversations', to=settings.AUTH_USER_MODEL, verbose_name='الطالب'),
        ),
        migrations.AlterField(
            model_name='conversation',
            name='teacher',
            field=models.ForeignKey(blank=True, limit_choices_to={'user_type': 'teacher'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='teacher_conversations', to=settings.AUTH_USER_MODEL, verbose_name='المعلم'),
        ),
    ]
