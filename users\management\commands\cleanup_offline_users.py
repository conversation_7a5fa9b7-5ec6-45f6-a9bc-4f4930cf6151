"""
أمر Django لتنظيف المستخدمين غير المتصلين
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from users.models import UserStatus


class Command(BaseCommand):
    help = 'تنظيف المستخدمين غير المتصلين'

    def add_arguments(self, parser):
        parser.add_argument(
            '--minutes',
            type=int,
            default=5,
            help='عدد الدقائق لاعتبار المستخدم غير متصل (افتراضي: 5)'
        )

    def handle(self, *args, **options):
        minutes = options['minutes']
        offline_threshold = timezone.now() - timedelta(minutes=minutes)
        
        # تحديث المستخدمين غير المتصلين
        updated_count = UserStatus.objects.filter(
            last_seen__lt=offline_threshold,
            is_online=True
        ).update(is_online=False)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'تم تحديث {updated_count} مستخدم إلى حالة غير متصل'
            )
        )
        
        # إحصائيات
        total_online = UserStatus.objects.filter(is_online=True).count()
        total_offline = UserStatus.objects.filter(is_online=False).count()
        
        self.stdout.write(f'المستخدمون المتصلون: {total_online}')
        self.stdout.write(f'المستخدمون غير المتصلين: {total_offline}')
