# Generated by Django 4.2.7 on 2025-05-22 16:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0004_payment_manual_status_change_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='teacherearning',
            name='lesson_duration',
            field=models.PositiveIntegerField(default=45, verbose_name='مدة الحصة (دقيقة)'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='teacherearning',
            name='lesson_price',
            field=models.DecimalField(decimal_places=2, default=70.0, max_digits=10, verbose_name='سعر الحصة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='teacherearning',
            name='lessons_completed',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد الحصص المكتملة'),
        ),
    ]
