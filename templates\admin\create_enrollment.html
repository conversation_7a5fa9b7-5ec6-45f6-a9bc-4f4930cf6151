{% extends 'base.html' %}

{% block title %}تسجيل الطلاب في الدورات - نظام قرآنيا التعليمي{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-6">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="{% url 'admin_courses' %}" class="text-gray-500 hover:text-gray-700 ml-4">
                        <i class="fas fa-arrow-right text-xl"></i>
                    </a>
                    <div>
                        <h1 class="text-2xl font-bold text-islamic-dark">تسجيل الطلاب في الدورات</h1>
                        <p class="text-gray-600 mt-1">ربط الطلاب بالدورات والمعلمين</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="{% url 'admin_courses' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-list ml-2"></i>
                        عرض التسجيلات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Enrollment Type Selection -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Single Student Enrollment -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                    <i class="fas fa-user text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">تسجيل طالب واحد</h3>
                    <p class="text-gray-600">تسجيل طالب واحد في دورة مع معلم محدد</p>
                </div>
            </div>
            <button onclick="showSingleEnrollment()" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                <i class="fas fa-plus ml-2"></i>
                بدء التسجيل
            </button>
        </div>

        <!-- Multiple Students Enrollment -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                    <i class="fas fa-users text-green-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">تسجيل عدة طلاب</h3>
                    <p class="text-gray-600">تسجيل عدة طلاب مع معلم واحد في دورة</p>
                </div>
            </div>
            <button onclick="showMultipleEnrollment()" class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200">
                <i class="fas fa-plus ml-2"></i>
                بدء التسجيل
            </button>
        </div>
    </div>

    <!-- Single Student Enrollment Form -->
    <div id="single-enrollment-form" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6" style="display: none;">
        <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-user text-blue-600 ml-3"></i>
            تسجيل طالب واحد
        </h3>

        <form method="POST" action="{% url 'admin_create_enrollment' %}">
            {% csrf_token %}
            <input type="hidden" name="enrollment_type" value="single">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Student Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اختيار الطالب</label>
                    <select name="student_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">اختر الطالب</option>
                        {% for student in students %}
                        <option value="{{ student.id }}" {% if selected_student == student.id|stringformat:"s" %}selected{% endif %}>
                            {{ student.get_full_name|default:student.username }} - {{ student.email }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Course Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اختيار الدورة</label>
                    <select name="course_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" onchange="updateCourseInfo(this)">
                        <option value="">اختر الدورة</option>
                        {% for course in courses %}
                        <option value="{{ course.id }}"
                                data-price="{{ course.price }}"
                                data-duration="{{ course.duration_weeks }}"
                                data-lessons="{{ course.lessons_per_week }}"
                                data-type="{{ course.get_course_type_display }}"
                                {% if selected_course == course.id|stringformat:"s" %}selected{% endif %}>
                            {{ course.title }} - {{ course.get_difficulty_level_display }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Teacher Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اختيار المعلم</label>
                    <select name="teacher_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">اختر المعلم</option>
                        {% for teacher in teachers %}
                        <option value="{{ teacher.id }}" {% if selected_teacher == teacher.id|stringformat:"s" %}selected{% endif %}>
                            {{ teacher.get_full_name|default:teacher.username }}
                            {% if teacher.overall_rating %}
                                - ⭐ {{ teacher.overall_rating.average_rating|floatformat:1 }}
                            {% endif %}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Start Date -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ البداية</label>
                    <input type="date" name="start_date" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                           min="{{ today|date:'Y-m-d' }}">
                </div>
            </div>

            <!-- Course Info Display -->
            <div id="course-info" class="mt-6 p-4 bg-gray-50 rounded-lg" style="display: none;">
                <h4 class="font-semibold text-gray-900 mb-2">معلومات الدورة:</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600">السعر:</span>
                        <span id="course-price" class="font-medium text-green-600"></span>
                    </div>
                    <div>
                        <span class="text-gray-600">المدة:</span>
                        <span id="course-duration" class="font-medium"></span>
                    </div>
                    <div>
                        <span class="text-gray-600">الحصص الأسبوعية:</span>
                        <span id="course-lessons" class="font-medium"></span>
                    </div>
                    <div>
                        <span class="text-gray-600">النوع:</span>
                        <span id="course-type" class="font-medium"></span>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="mt-6 flex justify-end space-x-4 space-x-reverse">
                <button type="button" onclick="hideForms()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                    إلغاء
                </button>
                <button type="submit" class="bg-islamic-primary text-white px-6 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200">
                    <i class="fas fa-save ml-2"></i>
                    تسجيل الطالب
                </button>
            </div>
        </form>
    </div>

    <!-- Multiple Students Enrollment Form -->
    <div id="multiple-enrollment-form" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6" style="display: none;">
        <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-users text-green-600 ml-3"></i>
            تسجيل عدة طلاب
        </h3>

        <form method="POST" action="{% url 'admin_create_multiple_enrollments' %}">
            {% csrf_token %}
            <input type="hidden" name="enrollment_type" value="multiple">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- Course Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اختيار الدورة</label>
                    <select name="course_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent" onchange="updateCourseInfoMultiple(this)">
                        <option value="">اختر الدورة</option>
                        {% for course in courses %}
                        <option value="{{ course.id }}"
                                data-price="{{ course.price }}"
                                data-duration="{{ course.duration_weeks }}"
                                data-lessons="{{ course.lessons_per_week }}"
                                data-type="{{ course.get_course_type_display }}">
                            {{ course.title }} - {{ course.get_difficulty_level_display }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Teacher Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اختيار المعلم</label>
                    <select name="teacher_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">اختر المعلم</option>
                        {% for teacher in teachers %}
                        <option value="{{ teacher.id }}">
                            {{ teacher.get_full_name|default:teacher.username }}
                            {% if teacher.overall_rating %}
                                - ⭐ {{ teacher.overall_rating.average_rating|floatformat:1 }}
                            {% endif %}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <!-- Students Selection -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">اختيار الطلاب</label>
                <div class="border border-gray-300 rounded-lg p-4 max-h-60 overflow-y-auto">
                    <div class="mb-3">
                        <button type="button" onclick="selectAllStudents()" class="text-sm bg-blue-100 text-blue-700 px-3 py-1 rounded mr-2">تحديد الكل</button>
                        <button type="button" onclick="deselectAllStudents()" class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded">إلغاء التحديد</button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {% for student in students %}
                        <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                            <input type="checkbox" name="student_ids" value="{{ student.id }}" class="ml-2">
                            <div class="flex-1">
                                <div class="text-sm font-medium">{{ student.get_full_name|default:student.username }}</div>
                                <div class="text-xs text-gray-500">{{ student.email }}</div>
                            </div>
                        </label>
                        {% endfor %}
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-2">اختر الطلاب المراد تسجيلهم في الدورة</p>
            </div>

            <!-- Start Date -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ البداية</label>
                <input type="date" name="start_date" required
                       class="w-full md:w-1/3 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent"
                       min="{{ today|date:'Y-m-d' }}">
            </div>

            <!-- Course Info Display -->
            <div id="course-info-multiple" class="mb-6 p-4 bg-gray-50 rounded-lg" style="display: none;">
                <h4 class="font-semibold text-gray-900 mb-2">معلومات الدورة:</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600">السعر لكل طالب:</span>
                        <span id="course-price-multiple" class="font-medium text-green-600"></span>
                    </div>
                    <div>
                        <span class="text-gray-600">المدة:</span>
                        <span id="course-duration-multiple" class="font-medium"></span>
                    </div>
                    <div>
                        <span class="text-gray-600">الحصص الأسبوعية:</span>
                        <span id="course-lessons-multiple" class="font-medium"></span>
                    </div>
                    <div>
                        <span class="text-gray-600">النوع:</span>
                        <span id="course-type-multiple" class="font-medium"></span>
                    </div>
                </div>
                <div class="mt-3 p-3 bg-blue-50 rounded">
                    <span class="text-blue-700 font-medium">إجمالي التكلفة: </span>
                    <span id="total-cost" class="text-blue-900 font-bold">0 ريال</span>
                    <span class="text-blue-600 text-sm mr-2">(<span id="selected-count">0</span> طالب)</span>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 space-x-reverse">
                <button type="button" onclick="hideForms()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                    إلغاء
                </button>
                <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200">
                    <i class="fas fa-save ml-2"></i>
                    تسجيل الطلاب
                </button>
            </div>
        </form>
    </div>

    <!-- Recent Enrollments -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">آخر التسجيلات</h3>
        </div>
        <div class="p-6">
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-clipboard-list text-4xl mb-4"></i>
                <p>سيتم عرض آخر التسجيلات هنا</p>
                <a href="{% url 'admin_courses' %}" class="text-islamic-primary hover:text-islamic-light">عرض جميع التسجيلات</a>
            </div>
        </div>
    </div>
</div>

<script>
function showSingleEnrollment() {
    hideForms();
    document.getElementById('single-enrollment-form').style.display = 'block';
}

function showMultipleEnrollment() {
    hideForms();
    document.getElementById('multiple-enrollment-form').style.display = 'block';
}

function hideForms() {
    document.getElementById('single-enrollment-form').style.display = 'none';
    document.getElementById('multiple-enrollment-form').style.display = 'none';
}

function updateCourseInfo(select) {
    const option = select.options[select.selectedIndex];
    const infoDiv = document.getElementById('course-info');

    if (option.value) {
        document.getElementById('course-price').textContent = option.dataset.price + ' ريال';
        document.getElementById('course-duration').textContent = option.dataset.duration + ' أسبوع';
        document.getElementById('course-lessons').textContent = option.dataset.lessons + ' حصة';
        document.getElementById('course-type').textContent = option.dataset.type;
        infoDiv.style.display = 'block';
    } else {
        infoDiv.style.display = 'none';
    }
}

function updateCourseInfoMultiple(select) {
    const option = select.options[select.selectedIndex];
    const infoDiv = document.getElementById('course-info-multiple');

    if (option.value) {
        document.getElementById('course-price-multiple').textContent = option.dataset.price + ' ريال';
        document.getElementById('course-duration-multiple').textContent = option.dataset.duration + ' أسبوع';
        document.getElementById('course-lessons-multiple').textContent = option.dataset.lessons + ' حصة';
        document.getElementById('course-type-multiple').textContent = option.dataset.type;
        infoDiv.style.display = 'block';
        updateTotalCost();
    } else {
        infoDiv.style.display = 'none';
    }
}

function selectAllStudents() {
    const checkboxes = document.querySelectorAll('input[name="student_ids"]');
    checkboxes.forEach(cb => cb.checked = true);
    updateTotalCost();
}

function deselectAllStudents() {
    const checkboxes = document.querySelectorAll('input[name="student_ids"]');
    checkboxes.forEach(cb => cb.checked = false);
    updateTotalCost();
}

function updateTotalCost() {
    const courseSelect = document.querySelector('select[name="course_id"]');
    const selectedOption = courseSelect.options[courseSelect.selectedIndex];
    const checkedBoxes = document.querySelectorAll('input[name="student_ids"]:checked');

    if (selectedOption.value) {
        const pricePerStudent = parseFloat(selectedOption.dataset.price);
        const studentCount = checkedBoxes.length;
        const totalCost = pricePerStudent * studentCount;

        document.getElementById('selected-count').textContent = studentCount;
        document.getElementById('total-cost').textContent = totalCost.toLocaleString() + ' ريال';
    }
}

// Add event listeners for student checkboxes
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('input[name="student_ids"]');
    checkboxes.forEach(cb => {
        cb.addEventListener('change', updateTotalCost);
    });
});
</script>
{% endblock %}
