# Generated by Django 4.2.7 on 2025-05-23 12:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0006_discount_paymentrequest_subscriptionplan_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='TeacherPayoutRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ المطلوب')),
                ('available_earnings', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الأرباح المتاحة')),
                ('month', models.IntegerField(verbose_name='الشهر')),
                ('year', models.IntegerField(verbose_name='السنة')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليه'), ('paid', 'تم الدفع'), ('rejected', 'مرفوض'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('payment_method', models.CharField(blank=True, max_length=50, verbose_name='طريقة الدفع المفضلة')),
                ('bank_details', models.TextField(blank=True, verbose_name='تفاصيل البنك')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات المعلم')),
                ('admin_notes', models.TextField(blank=True, verbose_name='ملاحظات المدير')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المعالجة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('processed_by', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'admin'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_payout_requests', to=settings.AUTH_USER_MODEL, verbose_name='تمت المعالجة بواسطة')),
                ('teacher', models.ForeignKey(limit_choices_to={'user_type': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='payout_requests', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'طلب سحب معلم',
                'verbose_name_plural': 'طلبات سحب المعلمين',
                'ordering': ['-created_at'],
                'unique_together': {('teacher', 'month', 'year')},
            },
        ),
        migrations.CreateModel(
            name='TeacherPayout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ المدفوع')),
                ('month', models.IntegerField(verbose_name='الشهر')),
                ('year', models.IntegerField(verbose_name='السنة')),
                ('payment_method', models.CharField(max_length=50, verbose_name='طريقة الدفع')),
                ('completed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإكمال')),
                ('payout_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='completed_payout', to='courses.teacherpayoutrequest', verbose_name='طلب السحب')),
                ('processed_by', models.ForeignKey(limit_choices_to={'user_type': 'admin'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='تمت المعالجة بواسطة')),
                ('teacher', models.ForeignKey(limit_choices_to={'user_type': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='completed_payouts', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'دفعة معلم مكتملة',
                'verbose_name_plural': 'دفعات المعلمين المكتملة',
                'ordering': ['-completed_at'],
            },
        ),
        migrations.CreateModel(
            name='AdminPayoutProof',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('proof_image', models.ImageField(upload_to='admin_payout_proofs/%Y/%m/', verbose_name='صورة إثبات الدفع')),
                ('payment_method', models.CharField(max_length=50, verbose_name='طريقة الدفع')),
                ('transaction_id', models.CharField(blank=True, max_length=100, verbose_name='رقم المعاملة')),
                ('bank_name', models.CharField(blank=True, max_length=100, verbose_name='اسم البنك')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات المدير')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('payout_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payout_proof', to='courses.teacherpayoutrequest', verbose_name='طلب السحب')),
                ('uploaded_by', models.ForeignKey(limit_choices_to={'user_type': 'admin'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='رفع بواسطة')),
            ],
            options={
                'verbose_name': 'إثبات دفع للمعلم',
                'verbose_name_plural': 'إثباتات دفع المعلمين',
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
