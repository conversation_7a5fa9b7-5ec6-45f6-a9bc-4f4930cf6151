from django.urls import path
from . import views

app_name = 'courses'

urlpatterns = [
    # Course Management
    path('', views.course_list, name='list'),
    path('create/', views.course_create, name='create'),
    path('<int:course_id>/', views.course_detail, name='detail'),
    path('<int:course_id>/edit/', views.course_edit, name='edit'),
    path('<int:course_id>/delete/', views.course_delete, name='delete'),
    
    # Enrollment Management
    path('enrollments/', views.enrollment_list, name='enrollment_list'),
    path('enroll/<int:course_id>/', views.enroll_student, name='enroll'),
    path('enrollment/<int:enrollment_id>/', views.enrollment_detail, name='enrollment_detail'),
    path('enrollment/<int:enrollment_id>/edit/', views.enrollment_edit, name='enrollment_edit'),
    
    # Student Progress
    path('progress/', views.progress_list, name='progress_list'),
    path('progress/add/', views.add_progress, name='add_progress'),
    path('progress/<int:progress_id>/edit/', views.edit_progress, name='edit_progress'),
]
