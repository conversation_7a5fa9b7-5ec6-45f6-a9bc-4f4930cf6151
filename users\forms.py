from django import forms
from django.utils.translation import gettext_lazy as _
from .models import AcademySettings


class AcademySettingsForm(forms.ModelForm):
    """نموذج تعديل إعدادات الأكاديمية"""

    class Meta:
        model = AcademySettings
        fields = [
            'academy_name',
            'academy_email',
            'academy_phone',
            'academy_whatsapp',
            'academy_support_email',
            'academy_logo',
            'academy_address',
            'academy_description',
            'academy_slogan',
            'academy_working_hours',
        ]

        widgets = {
            'academy_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل اسم الأكاديمية',
                'dir': 'rtl',
            }),
            'academy_email': forms.EmailInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل البريد الإلكتروني للأكاديمية',
                'dir': 'ltr',
            }),
            'academy_phone': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل رقم هاتف الأكاديمية',
                'dir': 'ltr',
            }),
            'academy_whatsapp': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل رقم واتساب الأكاديمية',
                'dir': 'ltr',
            }),
            'academy_support_email': forms.EmailInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل البريد الإلكتروني للدعم الفني',
                'dir': 'ltr',
            }),
            'academy_logo': forms.FileInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
            }),
            'academy_address': forms.Textarea(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل عنوان الأكاديمية',
                'rows': 3,
                'dir': 'rtl',
            }),
            'academy_description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل وصف الأكاديمية',
                'rows': 5,
                'dir': 'rtl',
            }),
            'academy_slogan': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل سلوجان الأكاديمية',
                'dir': 'rtl',
            }),
            'academy_working_hours': forms.Textarea(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent',
                'placeholder': 'أدخل ساعات العمل',
                'rows': 3,
                'dir': 'rtl',
            }),
        }

        labels = {
            'academy_name': _('اسم الأكاديمية'),
            'academy_email': _('البريد الإلكتروني للأكاديمية'),
            'academy_phone': _('رقم هاتف الأكاديمية'),
            'academy_whatsapp': _('رقم واتساب الأكاديمية'),
            'academy_support_email': _('البريد الإلكتروني للدعم الفني'),
            'academy_logo': _('شعار الأكاديمية'),
            'academy_address': _('عنوان الأكاديمية'),
            'academy_description': _('وصف الأكاديمية'),
            'academy_slogan': _('سلوجان الأكاديمية'),
            'academy_working_hours': _('ساعات العمل'),
        }

        help_texts = {
            'academy_logo': _('يفضل أن يكون الشعار بحجم 200×200 بكسل'),
            'academy_description': _('وصف مختصر للأكاديمية يظهر في الصفحة الرئيسية'),
            'academy_slogan': _('يظهر في عناوين الصفحات وفي أماكن متعددة في النظام (مثل: نظام قرآنيا التعليمي)'),
            'academy_working_hours': _('مثال: الأحد - الخميس: 8:00 ص - 10:00 م، الجمعة: 2:00 م - 10:00 م'),
            'academy_whatsapp': _('يستخدم للتواصل عبر واتساب في صفحة الدعم'),
            'academy_support_email': _('يستخدم للتواصل في صفحات الدعم والمساعدة'),
        }
