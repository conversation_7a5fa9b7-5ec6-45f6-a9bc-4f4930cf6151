<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ SITE_NAME }} - {{ ACADEMY_SLOGAN }}{% endblock %}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Enhanced Styles -->
    {% load static %}
    <link href="{% static 'css/enhanced-style.css' %}" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .arabic-text {
            font-family: 'Amiri', serif;
        }

        /* Islamic Design Colors */
        :root {
            --primary-green: #2D5016;
            --light-green: #4A7C59;
            --gold: #D4AF37;
            --light-gold: #F4E4BC;
            --dark-blue: #1B365D;
            --light-blue: #E8F4FD;
        }

        .bg-islamic-primary { background-color: var(--primary-green); }
        .bg-islamic-light { background-color: var(--light-green); }
        .bg-islamic-gold { background-color: var(--gold); }
        .bg-islamic-light-gold { background-color: var(--light-gold); }
        .bg-islamic-dark { background-color: var(--dark-blue); }
        .bg-islamic-light-blue { background-color: var(--light-blue); }

        .text-islamic-primary { color: var(--primary-green); }
        .text-islamic-gold { color: var(--gold); }
        .text-islamic-dark { color: var(--dark-blue); }

        .border-islamic-gold { border-color: var(--gold); }

        /* Islamic Patterns */
        .islamic-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-green);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--light-green);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    {% if user.is_authenticated %}
        <!-- Sidebar Toggle Button (Mobile) -->
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Sidebar Overlay (Mobile) -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Mobile Sidebar -->
        <div class="mobile-sidebar" id="mobileSidebar">
            <button class="sidebar-close" id="sidebarClose">
                <i class="fas fa-times"></i>
            </button>
            {% include 'partials/mobile_navbar.html' %}
        </div>

        <!-- Desktop Sidebar -->
        <div class="desktop-sidebar">
            {% include 'partials/navbar.html' %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="main-content {% if user.is_authenticated %}mr-64{% endif %}">
        <!-- Messages -->
        {% if messages %}
            <div class="fixed top-4 left-4 right-4 z-50">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} mb-2 p-4 rounded-lg shadow-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                        <div class="flex justify-between items-center">
                            <span>{{ message }}</span>
                            <button onclick="this.parentElement.parentElement.remove()" class="text-lg font-bold">&times;</button>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Page Content -->
        <div class="p-6">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    {% if user.is_authenticated %}
        {% include 'partials/footer.html' %}
    {% endif %}

    <!-- Scripts -->
    <script>
        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);

        // متغيرات لتتبع الإشعارات المعروضة
        let shownNotifications = new Set();
        let shownMessages = new Set();

        // مسح جميع الإشعارات المنبثقة الموجودة عند تحميل الصفحة
        function clearAllExistingNotifications() {
            // مسح الإشعارات المنبثقة الموجودة
            const existingNotifications = document.querySelectorAll('.fixed.top-4.left-4');
            existingNotifications.forEach(notification => {
                if (notification.innerHTML.includes('رسالة جديدة') ||
                    notification.innerHTML.includes('رسالة من') ||
                    notification.classList.contains('bg-green-600') ||
                    notification.classList.contains('bg-blue-600')) {
                    notification.remove();
                }
            });

            // مسح البيانات المحفوظة
            shownNotifications.clear();
            shownMessages.clear();

            // مسح Local Storage إذا كان يحتوي على إشعارات
            try {
                localStorage.removeItem('shownNotifications');
                localStorage.removeItem('shownMessages');
                sessionStorage.removeItem('shownNotifications');
                sessionStorage.removeItem('shownMessages');
            } catch (e) {}
        }

        // Real-time notifications and messages update
        function updateNotificationsAndMessages() {
            fetch('/api/notifications-count/', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                },
            })
            .then(response => response.json())
            .then(data => {
                // تحديث عداد الإشعارات فقط
                const notificationCount = document.getElementById('notification-count');
                if (notificationCount) {
                    if (data.unread_notifications > 0) {
                        notificationCount.textContent = data.unread_notifications;
                        notificationCount.classList.remove('hidden');
                    } else {
                        notificationCount.classList.add('hidden');
                    }
                }

                // تحديث عداد الرسائل في القائمة الجانبية فقط
                const messagesBadges = document.querySelectorAll('.messages-count');
                messagesBadges.forEach(badge => {
                    if (data.unread_messages > 0) {
                        badge.textContent = data.unread_messages;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                });

                // لا نعرض إشعارات منبثقة هنا لمنع التكرار
                // الإشعارات المنبثقة تظهر فقط عند إنشاء رسالة جديدة فعلياً
            })
            .catch(error => console.log('تحديث الإشعارات:', error));
        }

        // إظهار إشعار رسالة جديدة - مع منع التكرار والتحقق من الصفحة الحالية
        function showNewMessageNotification(message) {
            // منع الإشعارات إذا كان المستخدم في صفحة المحادثات
            if (window.location.pathname.includes('/messages/')) {
                console.log('تم منع الإشعار - المستخدم في صفحة المحادثات');
                return;
            }

            // التحقق من عدم عرض هذه الرسالة من قبل
            const messageKey = `msg_${message.conversation_id}_${message.sender_name}`;
            if (shownMessages.has(messageKey)) {
                return; // لا نعرض الإشعار مرة أخرى
            }

            // إضافة الرسالة للقائمة المعروضة
            shownMessages.add(messageKey);

            const notification = document.createElement('div');
            notification.className = 'fixed top-4 left-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm animate-bounce';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-envelope text-xl ml-3"></i>
                    <div>
                        <p class="font-bold">رسالة جديدة</p>
                        <p class="text-sm">من: ${message.sender_name}</p>
                        <p class="text-xs mt-1">${message.preview}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
            playNotificationSound();
        }

        // إظهار إشعار عام جديد - مع منع التكرار والتحقق من الصفحة
        function showNewNotification(notification) {
            // منع الإشعارات إذا كان المستخدم في صفحة المحادثات
            if (window.location.pathname.includes('/messages/')) {
                console.log('تم منع الإشعار العام - المستخدم في صفحة المحادثات');
                return;
            }

            // التحقق من عدم عرض هذا الإشعار من قبل
            const notificationKey = `notif_${notification.id}_${notification.title}`;
            if (shownNotifications.has(notificationKey)) {
                return; // لا نعرض الإشعار مرة أخرى
            }

            // إضافة الإشعار للقائمة المعروضة
            shownNotifications.add(notificationKey);

            const notificationElement = document.createElement('div');
            notificationElement.className = 'fixed top-4 left-4 bg-green-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm animate-bounce';
            notificationElement.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-bell text-xl ml-3"></i>
                    <div>
                        <p class="font-bold">${notification.title}</p>
                        <p class="text-sm">${notification.message}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notificationElement);
            setTimeout(() => {
                if (notificationElement.parentElement) {
                    notificationElement.remove();
                }
            }, 5000);
            playNotificationSound();
        }

        // تشغيل صوت إشعار
        function playNotificationSound() {
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.play().catch(() => {});
            } catch (e) {}
        }

        // دالة لمسح الإشعارات المعروضة للمحادثة المحددة
        function clearShownMessagesForConversation(conversationId) {
            const keysToRemove = [];
            shownMessages.forEach(key => {
                if (key.includes(`msg_${conversationId}_`)) {
                    keysToRemove.push(key);
                }
            });
            keysToRemove.forEach(key => shownMessages.delete(key));
        }

        // مسح جميع الإشعارات المنبثقة عند تحميل الصفحة
        clearAllExistingNotifications();

        // تشغيل التحديث فقط خارج صفحات المحادثات
        if (!window.location.pathname.includes('/messages/')) {
            // تحديث كل 30 ثانية (تقليل التكرار)
            setInterval(updateNotificationsAndMessages, 30000);

            // تحديث عند تحميل الصفحة
            if (document.getElementById('notification-count')) {
                updateNotificationsAndMessages();
            }
        } else {
            // في صفحات المحادثات: مسح الإشعارات المعروضة فقط
            shownMessages.clear();
            console.log('تم إيقاف التحديث التلقائي - صفحة المحادثات');
        }

        // Live clock update
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const clockElement = document.getElementById('current-time');
            if (clockElement) {
                clockElement.textContent = timeString;
            }
        }

        // Update clock every second
        if (document.getElementById('current-time')) {
            setInterval(updateClock, 1000);
            updateClock(); // Initial call
        }

        // Enhanced animations on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.addEventListener('DOMContentLoaded', () => {
            const animatedElements = document.querySelectorAll('.animate-fade-in-up, .animate-slide-in-right');
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });

            // Enhanced hover effects for cards
            const cards = document.querySelectorAll('.enhanced-card, .stats-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-5px) scale(1.02)';
                });
                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Mobile sidebar functionality
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarClose = document.getElementById('sidebarClose');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const mobileSidebar = document.getElementById('mobileSidebar');

            function openSidebar() {
                if (mobileSidebar && sidebarOverlay) {
                    mobileSidebar.classList.add('active');
                    sidebarOverlay.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    console.log('Sidebar opened');
                }
            }

            function closeSidebar() {
                if (mobileSidebar && sidebarOverlay) {
                    mobileSidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                    console.log('Sidebar closed');
                }
            }

            // Add event listeners
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Toggle button clicked');
                    openSidebar();
                });
            }

            if (sidebarClose) {
                sidebarClose.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Close button clicked');
                    closeSidebar();
                });
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Overlay clicked');
                    closeSidebar();
                });
            }

            // Close sidebar on window resize if screen becomes large
            window.addEventListener('resize', () => {
                if (window.innerWidth > 1024) {
                    closeSidebar();
                }
            });

            // Close sidebar when clicking on sidebar links (mobile)
            setTimeout(() => {
                const sidebarLinks = document.querySelectorAll('.mobile-sidebar a');
                sidebarLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        if (window.innerWidth <= 1024) {
                            setTimeout(closeSidebar, 100);
                        }
                    });
                });
            }, 100);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
