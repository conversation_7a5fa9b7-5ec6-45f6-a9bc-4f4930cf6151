@echo off
echo 🕌 نظام قرآنيا التعليمي - الإعداد الأولي
echo ========================================
echo.

REM Check if Python is installed
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python 3.8 أو أحدث
    echo 📥 يمكنك تحميله من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

echo.
echo 🔧 إنشاء البيئة الافتراضية...
python -m venv venv

echo.
echo 📦 تفعيل البيئة الافتراضية...
call venv\Scripts\activate

echo.
echo 📥 تثبيت المتطلبات...
pip install -r requirements.txt

echo.
echo 🗄️  إعداد قاعدة البيانات...
python manage.py makemigrations
python manage.py migrate

echo.
echo 👤 إنشاء مستخدم مدير...
echo يرجى إدخال بيانات المدير:
python manage.py createsuperuser

echo.
echo 🔧 تحديث نوع المستخدم للمدير...
python update_system.py

echo.
echo 📊 إنشاء بيانات تجريبية...
python setup_system.py

echo.
echo 📁 جمع الملفات الثابتة...
python manage.py collectstatic --noinput

echo.
echo 🎉 تم الإعداد بنجاح!
echo.
echo 📋 الخطوات التالية:
echo    1. شغل run_server.bat لبدء الخادم
echo    2. افتح المتصفح على http://localhost:8080
echo    3. سجل الدخول باستخدام بيانات المدير
echo.
echo 📖 للمزيد من المعلومات اقرأ ملف README.md
echo.
pause
