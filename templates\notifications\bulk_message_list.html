{% extends 'base.html' %}
{% load static %}

{% block title %}الرسائل الجماعية المرسلة{% endblock %}

{% block extra_css %}
<style>
    .message-card {
        transition: all 0.3s ease;
        border-right: 4px solid transparent;
    }
    .message-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .message-announcement {
        border-right-color: #3b82f6;
    }
    .message-update {
        border-right-color: #10b981;
    }
    .message-maintenance {
        border-right-color: #f59e0b;
    }
    .message-urgent {
        border-right-color: #ef4444;
    }
    .message-training {
        border-right-color: #8b5cf6;
    }
    .message-event {
        border-right-color: #6366f1;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .status-sent {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    .status-draft {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-islamic-primary via-islamic-secondary to-islamic-accent">
    <div class="container mx-auto px-4 py-8">

        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-islamic-primary mb-2">الرسائل الجماعية المرسلة</h1>
                    <p class="text-gray-600">تابع جميع الرسائل الجماعية التي أرسلتها</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="{% url 'notifications:compose_bulk_message' %}" class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        رسالة جماعية جديدة
                    </a>
                    <a href="{% url 'notifications:message_list' %}" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-envelope ml-2"></i>
                        الرسائل العادية
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card rounded-xl shadow-lg p-6 text-white">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-paper-plane text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-white text-opacity-80 text-sm">إجمالي الرسائل</p>
                        <p class="text-2xl font-bold">{{ bulk_messages.paginator.count }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">تم الإرسال</p>
                        <p class="text-2xl font-bold text-green-600">
                            {% for message in bulk_messages %}{% if message.is_sent %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-users text-blue-600 text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">إجمالي المستقبلين</p>
                        <p class="text-2xl font-bold text-blue-600">
                            {% for message in bulk_messages %}{{ message.recipients_count|add:0 }}{% if not forloop.last %}+{% endif %}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-clock text-purple-600 text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">آخر إرسال</p>
                        <p class="text-sm font-bold text-purple-600">
                            {% if bulk_messages %}
                                {{ bulk_messages.0.sent_at|timesince|default:"لم يتم الإرسال" }} مضت
                            {% else %}
                                لا توجد رسائل
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages List -->
        {% if bulk_messages %}
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-6">
                <i class="fas fa-list text-blue-600 ml-2"></i>
                قائمة الرسائل الجماعية
            </h2>

            <div class="space-y-4">
                {% for message in bulk_messages %}
                <div class="message-card bg-gray-50 rounded-lg p-6
                            {% if message.message_type == 'announcement' %}message-announcement{% endif %}
                            {% if message.message_type == 'update' %}message-update{% endif %}
                            {% if message.message_type == 'maintenance' %}message-maintenance{% endif %}
                            {% if message.message_type == 'urgent' %}message-urgent{% endif %}
                            {% if message.message_type == 'training' %}message-training{% endif %}
                            {% if message.message_type == 'event' %}message-event{% endif %}">
                    
                    <div class="flex items-start justify-between">
                        <div class="flex items-start flex-1">
                            <!-- Message Icon -->
                            <div class="flex-shrink-0 ml-4">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center
                                            {% if message.message_type == 'announcement' %}bg-blue-100{% endif %}
                                            {% if message.message_type == 'update' %}bg-green-100{% endif %}
                                            {% if message.message_type == 'maintenance' %}bg-yellow-100{% endif %}
                                            {% if message.message_type == 'urgent' %}bg-red-100{% endif %}
                                            {% if message.message_type == 'training' %}bg-purple-100{% endif %}
                                            {% if message.message_type == 'event' %}bg-indigo-100{% endif %}">
                                    {% if message.message_type == 'announcement' %}
                                        <i class="fas fa-bullhorn text-blue-600"></i>
                                    {% elif message.message_type == 'update' %}
                                        <i class="fas fa-sync text-green-600"></i>
                                    {% elif message.message_type == 'maintenance' %}
                                        <i class="fas fa-tools text-yellow-600"></i>
                                    {% elif message.message_type == 'urgent' %}
                                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                                    {% elif message.message_type == 'training' %}
                                        <i class="fas fa-graduation-cap text-purple-600"></i>
                                    {% elif message.message_type == 'event' %}
                                        <i class="fas fa-calendar-star text-indigo-600"></i>
                                    {% else %}
                                        <i class="fas fa-envelope text-gray-600"></i>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Message Content -->
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-lg font-semibold text-gray-900">{{ message.subject }}</h3>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <!-- Message Type Badge -->
                                        <span class="
                                            {% if message.message_type == 'announcement' %}bg-blue-100 text-blue-800{% endif %}
                                            {% if message.message_type == 'update' %}bg-green-100 text-green-800{% endif %}
                                            {% if message.message_type == 'maintenance' %}bg-yellow-100 text-yellow-800{% endif %}
                                            {% if message.message_type == 'urgent' %}bg-red-100 text-red-800{% endif %}
                                            {% if message.message_type == 'training' %}bg-purple-100 text-purple-800{% endif %}
                                            {% if message.message_type == 'event' %}bg-indigo-100 text-indigo-800{% endif %}
                                            text-xs px-2 py-1 rounded-full font-medium">
                                            {{ message.get_message_type_display }}
                                        </span>
                                        
                                        <!-- Priority Badge -->
                                        {% if message.priority == 'urgent' %}
                                            <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium animate-pulse">عاجل</span>
                                        {% elif message.priority == 'high' %}
                                            <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium">عالي</span>
                                        {% elif message.priority == 'medium' %}
                                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">متوسط</span>
                                        {% else %}
                                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full font-medium">منخفض</span>
                                        {% endif %}
                                        
                                        <!-- Status Badge -->
                                        {% if message.is_sent %}
                                            <span class="status-sent text-white text-xs px-2 py-1 rounded-full font-medium">
                                                <i class="fas fa-check ml-1"></i>
                                                تم الإرسال
                                            </span>
                                        {% else %}
                                            <span class="status-draft text-white text-xs px-2 py-1 rounded-full font-medium">
                                                <i class="fas fa-edit ml-1"></i>
                                                مسودة
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>

                                <p class="text-gray-600 mb-4 leading-relaxed">{{ message.content|truncatewords:20 }}</p>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-users ml-2 text-blue-500"></i>
                                        <span class="font-medium">المستقبلين:</span>
                                        <span class="mr-1">{{ message.get_recipient_type_display }}</span>
                                    </div>
                                    
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-user-check ml-2 text-green-500"></i>
                                        <span class="font-medium">العدد:</span>
                                        <span class="mr-1">{{ message.recipients_count }} مستخدم</span>
                                    </div>
                                    
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-clock ml-2 text-purple-500"></i>
                                        <span class="font-medium">التاريخ:</span>
                                        <span class="mr-1">{{ message.created_at|date:"Y/m/d H:i" }}</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-sm text-gray-500">
                                        {% if message.sent_at %}
                                            <i class="fas fa-paper-plane ml-2"></i>
                                            <span>تم الإرسال: {{ message.sent_at|timesince }} مضت</span>
                                        {% else %}
                                            <i class="fas fa-edit ml-2"></i>
                                            <span>تم الإنشاء: {{ message.created_at|timesince }} مضت</span>
                                        {% endif %}
                                        
                                        {% if message.send_email %}
                                            <span class="mr-4 text-blue-600">
                                                <i class="fas fa-envelope ml-1"></i>
                                                تم إرسال إيميل
                                            </span>
                                        {% endif %}
                                    </div>

                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        {% if not message.is_sent %}
                                            <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                                                <i class="fas fa-paper-plane ml-1"></i>
                                                إرسال الآن
                                            </button>
                                        {% endif %}
                                        
                                        <button class="bg-blue-100 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors text-sm">
                                            <i class="fas fa-eye ml-1"></i>
                                            عرض التفاصيل
                                        </button>
                                        
                                        <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                                            <i class="fas fa-copy ml-1"></i>
                                            نسخ
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if bulk_messages.has_other_pages %}
            <div class="mt-8 flex items-center justify-center">
                <nav class="flex items-center space-x-2 space-x-reverse">
                    {% if bulk_messages.has_previous %}
                        <a href="?page={{ bulk_messages.previous_page_number }}" 
                           class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}

                    <span class="px-4 py-2 bg-islamic-primary text-white rounded-lg">
                        {{ bulk_messages.number }} من {{ bulk_messages.paginator.num_pages }}
                    </span>

                    {% if bulk_messages.has_next %}
                        <a href="?page={{ bulk_messages.next_page_number }}" 
                           class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
            {% endif %}
        </div>

        {% else %}
        <!-- Empty State -->
        <div class="bg-white rounded-xl shadow-lg p-12 text-center">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-paper-plane text-gray-400 text-4xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">لم ترسل أي رسائل جماعية بعد</h3>
            <p class="text-gray-600 mb-6 max-w-md mx-auto">
                ابدأ بإرسال رسالة جماعية للتواصل مع جميع المستخدمين أو مجموعة محددة منهم.
            </p>
            <a href="{% url 'notifications:compose_bulk_message' %}" 
               class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors inline-block">
                <i class="fas fa-plus ml-2"></i>
                إرسال رسالة جماعية
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
