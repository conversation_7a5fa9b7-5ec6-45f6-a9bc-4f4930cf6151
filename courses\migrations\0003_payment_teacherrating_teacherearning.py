# Generated by Django 4.2.7 on 2025-05-22 15:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('credit_card', 'بطاقة ائتمان'), ('bank_transfer', 'تحويل بنكي'), ('cash', 'نقداً'), ('wallet', 'محفظة إلكترونية')], max_length=15, verbose_name='طريقة الدفع')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('processing', 'قيد المعالجة'), ('completed', 'مكتمل'), ('failed', 'فاشل'), ('refunded', 'مسترد'), ('cancelled', 'ملغي')], default='pending', max_length=15, verbose_name='حالة الدفع')),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='معرف المعاملة')),
                ('payment_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الدفع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='courses.enrollment', verbose_name='التسجيل')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_payments', to=settings.AUTH_USER_MODEL, verbose_name='معالج بواسطة')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'المدفوعات',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='TeacherRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_ratings', models.PositiveIntegerField(default=0, verbose_name='إجمالي التقييمات')),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3, verbose_name='متوسط التقييم')),
                ('teaching_quality_avg', models.DecimalField(decimal_places=2, default=0.0, max_digits=3, verbose_name='متوسط جودة التدريس')),
                ('interaction_avg', models.DecimalField(decimal_places=2, default=0.0, max_digits=3, verbose_name='متوسط التفاعل')),
                ('punctuality_avg', models.DecimalField(decimal_places=2, default=0.0, max_digits=3, verbose_name='متوسط الالتزام بالوقت')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('teacher', models.OneToOneField(limit_choices_to={'user_type': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='overall_rating', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'تقييم المعلم الإجمالي',
                'verbose_name_plural': 'تقييمات المعلمين الإجمالية',
            },
        ),
        migrations.CreateModel(
            name='TeacherEarning',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ الربح')),
                ('commission_rate', models.DecimalField(decimal_places=2, default=70.0, max_digits=5, verbose_name='نسبة العمولة %')),
                ('is_paid', models.BooleanField(default=False, verbose_name='مدفوع')),
                ('paid_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الدفع')),
                ('month', models.PositiveIntegerField(verbose_name='الشهر')),
                ('year', models.PositiveIntegerField(verbose_name='السنة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_earnings', to='courses.enrollment', verbose_name='التسجيل')),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_earnings', to='courses.payment', verbose_name='الدفعة')),
                ('teacher', models.ForeignKey(limit_choices_to={'user_type': 'teacher'}, on_delete=django.db.models.deletion.CASCADE, related_name='earnings', to=settings.AUTH_USER_MODEL, verbose_name='المعلم')),
            ],
            options={
                'verbose_name': 'ربح المعلم',
                'verbose_name_plural': 'أرباح المعلمين',
                'ordering': ['-created_at'],
            },
        ),
    ]
