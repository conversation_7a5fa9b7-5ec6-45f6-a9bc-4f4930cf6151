{% extends 'base.html' %}
{% load static %}

{% block title %}رد على: {{ original_message.subject }}{% endblock %}

{% block extra_css %}
<style>
    .reply-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    .form-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .original-message {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-right: 4px solid #3b82f6;
    }
    .sender-avatar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="reply-form">
    <div class="container mx-auto px-4 py-8">

        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">رد على الرسالة</h1>
            <p class="text-white text-opacity-90 text-lg">{{ original_message.subject }}</p>
        </div>

        <!-- Form Card -->
        <div class="max-w-4xl mx-auto">
            <div class="form-card rounded-2xl shadow-2xl p-8">

                <!-- Original Message -->
                <div class="original-message rounded-xl p-6 mb-8">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-quote-right text-blue-600 ml-2"></i>
                        الرسالة الأصلية
                    </h3>

                    <div class="flex items-start mb-4">
                        <div class="sender-avatar w-12 h-12 rounded-full flex items-center justify-center text-white ml-4">
                            {% if original_message.sender.user_type == 'admin' %}
                                <i class="fas fa-crown"></i>
                            {% elif original_message.sender.user_type == 'teacher' %}
                                <i class="fas fa-chalkboard-teacher"></i>
                            {% else %}
                                <i class="fas fa-user-graduate"></i>
                            {% endif %}
                        </div>

                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-gray-900">{{ original_message.sender.get_full_name }}</h4>
                                <span class="text-sm text-gray-500">{{ original_message.created_at|date:"Y/m/d H:i" }}</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">
                                {% if original_message.sender.user_type == 'admin' %}
                                    مدير النظام
                                {% elif original_message.sender.user_type == 'teacher' %}
                                    معلم
                                {% else %}
                                    طالب
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-4">
                        <div class="text-gray-800 leading-relaxed">{{ original_message.content|safe }}</div>
                    </div>
                </div>

                <!-- Reply Form -->
                <form method="POST" class="space-y-6">
                    {% csrf_token %}

                    <!-- Reply To Info -->
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                <i class="fas fa-reply text-blue-600"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">رد إلى: {{ original_message.sender.get_full_name }}</p>
                                <p class="text-sm text-gray-600">الموضوع: رد: {{ original_message.subject }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Reply Content -->
                    <div>
                        <label for="content" class="block text-lg font-bold text-gray-800 mb-2">
                            <i class="fas fa-pen text-green-600 ml-2"></i>
                            محتوى الرد *
                        </label>
                        <textarea id="content"
                                  name="content"
                                  required
                                  rows="8"
                                  placeholder="اكتب ردك هنا..."
                                  class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-green-500 focus:outline-none transition-colors resize-none"></textarea>

                        <div class="flex justify-between items-center mt-2">
                            <p class="text-sm text-gray-500">
                                <i class="fas fa-info-circle ml-1"></i>
                                سيتم إرسال إشعار للمرسل الأصلي
                            </p>
                            <span id="char-count" class="text-sm text-gray-500">0 حرف</span>
                        </div>
                    </div>

                    <!-- Message Type Info -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-tag text-purple-600 ml-2"></i>
                                <span class="font-medium text-gray-700">نوع الرسالة:</span>
                                <span class="mr-2">
                                    {% if original_message.message_type == 'support' %}
                                        <span class="bg-orange-100 text-orange-800 text-sm px-2 py-1 rounded-full">دعم</span>
                                    {% elif original_message.message_type == 'announcement' %}
                                        <span class="bg-red-100 text-red-800 text-sm px-2 py-1 rounded-full">إعلان</span>
                                    {% else %}
                                        <span class="bg-green-100 text-green-800 text-sm px-2 py-1 rounded-full">خاص</span>
                                    {% endif %}
                                </span>
                            </div>

                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-clock ml-1"></i>
                                <span>{{ original_message.created_at|timesince }} مضت</span>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <a href="{% url 'notifications:message_detail' original_message.id %}"
                               class="bg-gray-100 text-gray-600 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors">
                                <i class="fas fa-arrow-right ml-2"></i>
                                العودة للرسالة
                            </a>

                            <a href="{% url 'notifications:message_list' %}"
                               class="bg-gray-100 text-gray-600 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors">
                                <i class="fas fa-list ml-2"></i>
                                قائمة الرسائل
                            </a>
                        </div>

                        <button type="submit"
                                class="bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-3 rounded-lg hover:from-green-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                                id="send-btn" disabled>
                            <i class="fas fa-paper-plane ml-2"></i>
                            إرسال الرد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Character counter and form validation
const contentTextarea = document.getElementById('content');
const charCount = document.getElementById('char-count');
const sendBtn = document.getElementById('send-btn');

contentTextarea.addEventListener('input', function() {
    const length = this.value.length;
    charCount.textContent = `${length} حرف`;

    // Color coding for character count
    if (length > 500) {
        charCount.classList.add('text-red-500');
        charCount.classList.remove('text-yellow-500', 'text-gray-500');
    } else if (length > 300) {
        charCount.classList.add('text-yellow-500');
        charCount.classList.remove('text-red-500', 'text-gray-500');
    } else {
        charCount.classList.add('text-gray-500');
        charCount.classList.remove('text-red-500', 'text-yellow-500');
    }

    // Enable/disable send button
    if (this.value.trim().length > 0) {
        sendBtn.disabled = false;
    } else {
        sendBtn.disabled = true;
    }

    // Auto-resize textarea
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const content = contentTextarea.value.trim();

    if (!content) {
        e.preventDefault();
        alert('يرجى كتابة محتوى الرد');
        contentTextarea.focus();
        return false;
    }

    if (content.length < 10) {
        e.preventDefault();
        alert('يرجى كتابة رد أطول (على الأقل 10 أحرف)');
        contentTextarea.focus();
        return false;
    }
});

// Auto-focus on content textarea
document.addEventListener('DOMContentLoaded', function() {
    contentTextarea.focus();
});
</script>
{% endblock %}
