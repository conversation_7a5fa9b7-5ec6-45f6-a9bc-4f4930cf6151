{% extends 'base.html' %}
{% load static %}

{% block title %}تقييمات المعلمين - قرآنيا{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">تقييمات المعلمين</h1>
                    <p class="mt-2 text-gray-600">مراقبة أداء المعلمين من خلال تقييمات الطلاب</p>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="{% url 'admin_dashboard' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center ml-4">
                        <i class="fas fa-star text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي التقييمات</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_ratings }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center ml-4">
                        <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">متوسط التقييم العام</p>
                        <p class="text-2xl font-bold text-gray-900">{{ average_rating }}/5</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center ml-4">
                        <i class="fas fa-chalkboard-teacher text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">المعلمون المقيمون</p>
                        <p class="text-2xl font-bold text-gray-900">{{ teacher_ratings.count }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Teacher Ratings Summary -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">تقييمات المعلمين الإجمالية</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        {% for rating in teacher_ratings %}
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-islamic-light-blue rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-user text-islamic-primary"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">{{ rating.teacher.get_full_name }}</p>
                                    <p class="text-sm text-gray-600">{{ rating.total_ratings }} تقييم</p>
                                </div>
                            </div>
                            <div class="text-left">
                                <div class="flex items-center">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= rating.average_rating %}
                                            <i class="fas fa-star text-yellow-400"></i>
                                        {% else %}
                                            <i class="far fa-star text-gray-300"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <p class="text-sm font-medium text-gray-900 mt-1">{{ rating.average_rating }}/5</p>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-8">
                            <i class="fas fa-star text-gray-400 text-4xl mb-3"></i>
                            <p class="text-gray-500">لا توجد تقييمات بعد</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Recent Ratings -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">آخر التقييمات</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        {% for rating in recent_ratings %}
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-2">
                                        <i class="fas fa-user text-blue-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ rating.lesson.enrollment.student.get_full_name }}</p>
                                        <p class="text-xs text-gray-600">قيّم {{ rating.lesson.enrollment.teacher.get_full_name }}</p>
                                    </div>
                                </div>
                                <div class="text-left">
                                    <div class="flex items-center">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= rating.overall_satisfaction %}
                                                <i class="fas fa-star text-yellow-400 text-sm"></i>
                                            {% else %}
                                                <i class="far fa-star text-gray-300 text-sm"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <p class="text-xs text-gray-600 mt-1">{{ rating.created_at|date:"Y-m-d" }}</p>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-3 gap-2 text-xs mb-2">
                                <div class="text-center">
                                    <span class="block text-gray-600">جودة التدريس</span>
                                    <span class="block font-medium">{{ rating.lesson_quality }}/5</span>
                                </div>
                                <div class="text-center">
                                    <span class="block text-gray-600">التفاعل</span>
                                    <span class="block font-medium">{{ rating.teacher_rating }}/5</span>
                                </div>
                                <div class="text-center">
                                    <span class="block text-gray-600">الجودة التقنية</span>
                                    <span class="block font-medium">{{ rating.technical_quality }}/5</span>
                                </div>
                            </div>
                            
                            {% if rating.feedback %}
                            <div class="bg-gray-50 rounded p-2 mt-2">
                                <p class="text-xs text-gray-700">{{ rating.feedback }}</p>
                            </div>
                            {% endif %}
                        </div>
                        {% empty %}
                        <div class="text-center py-8">
                            <i class="fas fa-comments text-gray-400 text-4xl mb-3"></i>
                            <p class="text-gray-500">لا توجد تقييمات حديثة</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Analysis -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">تحليل مفصل للتقييمات</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {% for rating in teacher_ratings %}
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-islamic-light-blue rounded-full flex items-center justify-center ml-3">
                                <i class="fas fa-chalkboard-teacher text-islamic-primary"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">{{ rating.teacher.get_full_name }}</h4>
                                <p class="text-sm text-gray-600">{{ rating.total_ratings }} تقييم</p>
                            </div>
                        </div>
                        
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">جودة التدريس</span>
                                <span class="text-sm font-medium">{{ rating.teaching_quality_avg|floatformat:1 }}/5</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">التفاعل</span>
                                <span class="text-sm font-medium">{{ rating.interaction_avg|floatformat:1 }}/5</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">الالتزام بالوقت</span>
                                <span class="text-sm font-medium">{{ rating.punctuality_avg|floatformat:1 }}/5</span>
                            </div>
                            <div class="border-t pt-2 mt-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-900">المتوسط العام</span>
                                    <span class="text-sm font-bold text-islamic-primary">{{ rating.average_rating }}/5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-span-full text-center py-8">
                        <i class="fas fa-chart-bar text-gray-400 text-4xl mb-3"></i>
                        <p class="text-gray-500">لا توجد بيانات تحليلية متاحة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
