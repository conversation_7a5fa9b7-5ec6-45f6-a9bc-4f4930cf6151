from django.contrib.auth import get_user_model
from notifications.models import Notification
from support.models import SupportTicket, SystemMessageRecipient
from messaging.models import Conversation, ChatMessage
from django.db.models import Q
from datetime import datetime
from users.models import AcademySettings

User = get_user_model()


def global_context(request):
    """إضافة متغيرات عامة لجميع القوالب"""
    context = {}

    # إضافة إعدادات الأكاديمية
    academy_settings = AcademySettings.get_settings()
    context['ACADEMY_SETTINGS'] = academy_settings
    context['SITE_NAME'] = academy_settings.academy_name
    context['SITE_VERSION'] = '1.0.0'
    context['CURRENT_YEAR'] = datetime.now().year

    # إضافة متغيرات مختصرة لإعدادات الأكاديمية
    context['ACADEMY_EMAIL'] = academy_settings.academy_email
    context['ACADEMY_PHONE'] = academy_settings.academy_phone
    context['ACADEMY_WHATSAPP'] = academy_settings.academy_whatsapp
    context['ACADEMY_SUPPORT_EMAIL'] = academy_settings.academy_support_email
    context['ACADEMY_ADDRESS'] = academy_settings.academy_address
    context['ACADEMY_DESCRIPTION'] = academy_settings.academy_description
    context['ACADEMY_SLOGAN'] = academy_settings.academy_slogan
    context['ACADEMY_LOGO'] = academy_settings.academy_logo
    context['ACADEMY_WORKING_HOURS'] = academy_settings.academy_working_hours

    if request.user.is_authenticated:
        user = request.user

        # 1. إحصائيات الإشعارات العامة
        unread_notifications = Notification.objects.filter(
            recipient=user,
            is_read=False
        ).count()
        context['unread_notifications_count'] = unread_notifications

        # 2. إحصائيات تذاكر الدعم
        if user.is_admin():
            # للمدير - التذاكر غير المقروءة
            unread_tickets = SupportTicket.objects.filter(
                is_read_by_admin=False
            ).count()
            context['unread_tickets_count'] = unread_tickets
        else:
            # للمستخدمين - تذاكرهم غير المقروءة
            user_unread_tickets = SupportTicket.objects.filter(
                created_by=user,
                is_read_by_user=False
            ).count()
            context['user_unread_tickets_count'] = user_unread_tickets

        # 3. إحصائيات رسائل النظام (للمعلمين والطلاب فقط)
        if not user.is_admin():
            unread_system_messages = SystemMessageRecipient.objects.filter(
                recipient=user,
                system_message__is_active=True,
                is_read=False
            ).count()
            context['unread_system_messages_count'] = unread_system_messages

        # 4. إحصائيات الرسائل الشخصية
        if user.user_type == 'admin':
            conversations = Conversation.objects.filter(is_active=True)
        else:
            conversations = Conversation.objects.filter(
                Q(participant1=user) | Q(participant2=user) |
                Q(student=user) | Q(teacher=user),
                is_active=True
            )

        unread_messages = 0
        for conversation in conversations:
            unread_messages += conversation.get_unread_count_for_user(user)

        context['unread_messages_count'] = unread_messages

        # 5. إحصائيات طلبات الدفع للطلاب
        if user.user_type == 'student':
            from courses.models import PaymentRequest
            pending_payment_requests = PaymentRequest.objects.filter(
                student=user,
                status='pending'
            ).count()
            context['pending_payment_requests_count'] = pending_payment_requests

        # 6. إحصائيات طلبات السحب للمعلمين
        elif user.user_type == 'teacher':
            from courses.models import TeacherPayoutRequest
            # عرض الإشعار فقط للطلبات التي تم تحديثها ولم يشاهدها المعلم
            pending_payout_requests = TeacherPayoutRequest.objects.filter(
                teacher=user,
                teacher_viewed_update=False,
                status__in=['approved', 'rejected', 'paid']
            ).count()
            context['pending_payout_requests_count'] = pending_payout_requests

        # 7. إحصائيات طلبات السحب للمدير
        elif user.user_type == 'admin':
            from courses.models import TeacherPayoutRequest
            pending_teacher_payouts = TeacherPayoutRequest.objects.filter(
                status='pending'
            ).count()
            context['pending_teacher_payouts_count'] = pending_teacher_payouts

        # 8. إحصائيات طلبات التحقق للمدير
        if user.user_type == 'admin':
            pending_verifications = User.objects.filter(
                verification_status='pending'
            ).exclude(user_type='admin').count()
            context['pending_verifications_count'] = pending_verifications
        else:
            context['pending_verifications_count'] = 0

    return context
