from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.views.decorators.http import require_POST

from .models import Notification, Message, BulkMessage
from django.contrib.auth import get_user_model

User = get_user_model()


@login_required
def notification_list(request):
    """عرض قائمة الإشعارات للمستخدم"""
    user = request.user

    # فلترة الإشعارات
    notification_type = request.GET.get('type', 'all')
    priority = request.GET.get('priority', 'all')
    is_read = request.GET.get('read', 'all')

    # بناء الاستعلام
    notifications = Notification.objects.filter(recipient=user)

    if notification_type != 'all':
        notifications = notifications.filter(notification_type=notification_type)

    if priority != 'all':
        notifications = notifications.filter(priority=priority)

    if is_read == 'unread':
        notifications = notifications.filter(is_read=False)
    elif is_read == 'read':
        notifications = notifications.filter(is_read=True)

    # ترتيب الإشعارات
    notifications = notifications.order_by('-created_at')

    # تقسيم الصفحات
    paginator = Paginator(notifications, 15)
    page_number = request.GET.get('page')
    notifications = paginator.get_page(page_number)

    # إحصائيات
    total_notifications = Notification.objects.filter(recipient=user).count()
    unread_notifications = Notification.objects.filter(recipient=user, is_read=False).count()
    urgent_notifications = Notification.objects.filter(
        recipient=user,
        priority='urgent',
        is_read=False
    ).count()

    # أنواع الإشعارات المتاحة للفلترة
    notification_types = Notification.objects.filter(
        recipient=user
    ).values_list('notification_type', flat=True).distinct()

    context = {
        'notifications': notifications,
        'total_notifications': total_notifications,
        'unread_notifications': unread_notifications,
        'urgent_notifications': urgent_notifications,
        'notification_types': notification_types,
        'current_type': notification_type,
        'current_priority': priority,
        'current_read_status': is_read,
        'notification_type_choices': Notification.NOTIFICATION_TYPES,
        'priority_choices': Notification.PRIORITY_LEVELS,
    }

    return render(request, 'notifications/list.html', context)


@login_required
@require_POST
def mark_notification_read(request, notification_id):
    """تمييز إشعار كمقروء"""
    notification = get_object_or_404(
        Notification,
        id=notification_id,
        recipient=request.user
    )

    if not notification.is_read:
        notification.is_read = True
        notification.read_at = timezone.now()
        notification.save()

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'success': True})

    return redirect('notifications:list')


@login_required
@require_POST
def mark_all_notifications_read(request):
    """تمييز جميع الإشعارات كمقروءة"""
    updated_count = Notification.objects.filter(
        recipient=request.user,
        is_read=False
    ).update(
        is_read=True,
        read_at=timezone.now()
    )

    messages.success(request, f'تم تمييز {updated_count} إشعار كمقروء.')

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'success': True, 'count': updated_count})

    return redirect('notifications:list')


@login_required
def message_list(request):
    """عرض قائمة الرسائل الداخلية"""
    user = request.user

    # الرسائل المستلمة
    received_messages = Message.objects.filter(recipient=user).order_by('-created_at')

    # الرسائل المرسلة
    sent_messages = Message.objects.filter(sender=user).order_by('-created_at')

    # تقسيم الصفحات
    paginator = Paginator(received_messages, 10)
    page_number = request.GET.get('page')
    messages_page = paginator.get_page(page_number)

    context = {
        'received_messages': messages_page,
        'sent_messages': sent_messages[:5],  # آخر 5 رسائل مرسلة
        'unread_count': received_messages.filter(is_read=False).count(),
    }

    return render(request, 'notifications/message_list.html', context)


@login_required
def compose_message(request):
    """إنشاء رسالة جديدة"""
    # إذا كان المدير، إعادة توجيه لصفحة الرسائل الجماعية
    if request.user.is_admin():
        return redirect('notifications:compose_bulk_message')

    if request.method == 'POST':
        recipient_id = request.POST.get('recipient')
        subject = request.POST.get('subject')
        content = request.POST.get('content')
        message_type = request.POST.get('message_type', 'private')

        if recipient_id and subject and content:
            try:
                recipient = User.objects.get(id=recipient_id)

                message = Message.objects.create(
                    sender=request.user,
                    recipient=recipient,
                    subject=subject,
                    content=content,
                    message_type=message_type
                )

                # إنشاء إشعار للمستقبل
                from .utils import create_notification
                create_notification(
                    recipient=recipient,
                    notification_type='new_message',
                    title=f'رسالة جديدة: {subject}',
                    message=f'رسالة جديدة من {request.user.get_full_name()}',
                    sender=request.user,
                    priority='medium',
                    action_url=f'/notifications/messages/{message.id}/',
                    action_text='عرض الرسالة'
                )

                messages.success(request, 'تم إرسال الرسالة بنجاح.')
                return redirect('notifications:message_list')

            except User.DoesNotExist:
                messages.error(request, 'المستخدم المحدد غير موجود.')
        else:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')

    # المعلمون والطلاب يمكنهم إرسال رسائل للمدير فقط
    users = User.objects.filter(user_type='admin', is_active=True)

    context = {
        'users': users,
        'message_types': Message.MESSAGE_TYPES,
    }

    return render(request, 'notifications/compose_message.html', context)


@login_required
def compose_bulk_message(request):
    """إنشاء رسالة جماعية (للمدير فقط)"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('notifications:message_list')

    if request.method == 'POST':
        subject = request.POST.get('subject')
        content = request.POST.get('content')
        message_type = request.POST.get('message_type', 'announcement')
        recipient_type = request.POST.get('recipient_type', 'all')
        priority = request.POST.get('priority', 'medium')
        send_email = request.POST.get('send_email') == 'on'
        custom_recipients = request.POST.getlist('custom_recipients')

        if subject and content:
            # إنشاء الرسالة الجماعية
            bulk_message = BulkMessage.objects.create(
                sender=request.user,
                subject=subject,
                content=content,
                message_type=message_type,
                recipient_type=recipient_type,
                priority=priority,
                send_email=send_email
            )

            # إضافة المستقبلين المحددين إذا كان النوع "custom"
            if recipient_type == 'custom' and custom_recipients:
                bulk_message.custom_recipients.set(custom_recipients)

            # إرسال الرسالة
            sent_count = bulk_message.send_to_recipients()

            messages.success(request, f'تم إرسال الرسالة إلى {sent_count} مستخدم بنجاح.')
            return redirect('notifications:bulk_message_list')
        else:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')

    # جلب المستخدمين للاختيار المخصص
    teachers = User.objects.filter(user_type='teacher', is_active=True)
    students = User.objects.filter(user_type='student', is_active=True)

    # إحصائيات المستخدمين
    stats = {
        'total_users': User.objects.filter(is_active=True).exclude(user_type='admin').count(),
        'teachers_count': teachers.count(),
        'students_count': students.count(),
        'active_users_count': User.objects.filter(
            is_active=True,
            last_login__gte=timezone.now() - timezone.timedelta(days=30)
        ).exclude(user_type='admin').count(),
        'new_users_count': User.objects.filter(
            is_active=True,
            date_joined__gte=timezone.now() - timezone.timedelta(days=7)
        ).exclude(user_type='admin').count(),
    }

    context = {
        'message_types': BulkMessage.MESSAGE_TYPES,
        'recipient_types': BulkMessage.RECIPIENT_TYPES,
        'priority_levels': BulkMessage.PRIORITY_LEVELS,
        'teachers': teachers,
        'students': students,
        'stats': stats,
    }

    return render(request, 'notifications/compose_bulk_message.html', context)


@login_required
def bulk_message_list(request):
    """عرض قائمة الرسائل الجماعية (للمدير فقط)"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('notifications:message_list')

    bulk_messages = BulkMessage.objects.filter(sender=request.user).order_by('-created_at')

    # تقسيم الصفحات
    paginator = Paginator(bulk_messages, 10)
    page_number = request.GET.get('page')
    bulk_messages = paginator.get_page(page_number)

    context = {
        'bulk_messages': bulk_messages,
    }

    return render(request, 'notifications/bulk_message_list.html', context)


@login_required
def message_detail(request, message_id):
    """عرض تفاصيل الرسالة"""
    message = get_object_or_404(
        Message,
        id=message_id,
        recipient=request.user
    )

    # تمييز الرسالة كمقروءة
    if not message.is_read:
        message.mark_as_read()

    # الردود على الرسالة
    replies = Message.objects.filter(parent_message=message).order_by('created_at')

    context = {
        'message': message,
        'replies': replies,
    }

    return render(request, 'notifications/message_detail.html', context)


@login_required
def reply_message(request, message_id):
    """الرد على رسالة"""
    original_message = get_object_or_404(
        Message,
        id=message_id,
        recipient=request.user
    )

    if request.method == 'POST':
        content = request.POST.get('content')

        if content:
            reply = Message.objects.create(
                sender=request.user,
                recipient=original_message.sender,
                subject=f'رد: {original_message.subject}',
                content=content,
                message_type=original_message.message_type,
                parent_message=original_message
            )

            # إنشاء إشعار للمرسل الأصلي
            from .utils import create_notification
            create_notification(
                recipient=original_message.sender,
                notification_type='new_message',
                title=f'رد على رسالتك: {original_message.subject}',
                message=f'رد جديد من {request.user.get_full_name()}',
                sender=request.user,
                priority='medium',
                action_url=f'/notifications/messages/{reply.id}/',
                action_text='عرض الرد'
            )

            messages.success(request, 'تم إرسال الرد بنجاح.')
            return redirect('notifications:message_detail', message_id=message_id)
        else:
            messages.error(request, 'يرجى كتابة محتوى الرد.')

    context = {
        'original_message': original_message,
    }

    return render(request, 'notifications/reply_message.html', context)

# Support functionality moved to support app
