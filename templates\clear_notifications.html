<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مسح الإشعارات المنبثقة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .icon {
            font-size: 4rem;
            color: #4CAF50;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2rem;
        }
        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .btn-danger {
            background: #f44336;
        }
        .btn-danger:hover {
            background: #da190b;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🧹</div>
        <h1>مسح الإشعارات المنبثقة</h1>
        <p>هذه الصفحة ستقوم بمسح جميع الإشعارات المنبثقة المتكررة من المتصفح</p>
        
        <button class="btn" onclick="clearNotifications()">
            🗑️ مسح الإشعارات المنبثقة
        </button>
        
        <button class="btn btn-danger" onclick="clearAllData()">
            🔥 مسح جميع البيانات المحفوظة
        </button>
        
        <div id="status" class="status"></div>
        
        <p style="margin-top: 30px; font-size: 0.9rem; color: #999;">
            بعد المسح، يرجى العودة إلى الموقع الرئيسي
        </p>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function clearNotifications() {
            try {
                // مسح الإشعارات المنبثقة الموجودة
                const notifications = document.querySelectorAll('.fixed.top-4.left-4, [class*="notification"], [class*="alert"]');
                let cleared = 0;
                
                notifications.forEach(notification => {
                    if (notification.innerHTML.includes('رسالة جديدة') || 
                        notification.innerHTML.includes('رسالة من') ||
                        notification.classList.contains('bg-green-600') ||
                        notification.classList.contains('bg-blue-600')) {
                        notification.remove();
                        cleared++;
                    }
                });

                // مسح البيانات المرتبطة بالإشعارات
                localStorage.removeItem('shownNotifications');
                localStorage.removeItem('shownMessages');
                sessionStorage.removeItem('shownNotifications');
                sessionStorage.removeItem('shownMessages');
                
                // مسح أي متغيرات عامة
                if (window.shownNotifications) {
                    window.shownNotifications.clear();
                }
                if (window.shownMessages) {
                    window.shownMessages.clear();
                }

                showStatus(`✅ تم مسح ${cleared} إشعار منبثق بنجاح!`, 'success');
                
                // إعادة توجيه بعد 2 ثانية
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
                
            } catch (error) {
                showStatus('❌ حدث خطأ أثناء المسح: ' + error.message, 'error');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المحفوظة؟ هذا سيؤثر على جميع الإعدادات المحفوظة.')) {
                try {
                    // مسح جميع البيانات المحفوظة
                    localStorage.clear();
                    sessionStorage.clear();
                    
                    // مسح الكوكيز (ما عدا الضرورية)
                    document.cookie.split(";").forEach(function(c) { 
                        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                    });

                    showStatus('✅ تم مسح جميع البيانات المحفوظة!', 'success');
                    
                    // إعادة توجيه بعد 2 ثانية
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                    
                } catch (error) {
                    showStatus('❌ حدث خطأ أثناء المسح: ' + error.message, 'error');
                }
            }
        }

        // مسح تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                clearNotifications();
            }, 1000);
        });
    </script>
</body>
</html>
