{% extends 'base.html' %}

{% block title %}تقرير الأرباح - نظام قرآنيا التعليمي{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-6">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-islamic-dark">تقرير الأرباح</h1>
                    <p class="text-gray-600 mt-1">تتبع أرباحك ومدفوعاتك الشهرية</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="{% url 'teacher_payouts' %}" class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200 flex items-center">
                        <i class="fas fa-hand-holding-usd ml-2"></i>
                        طلبات السحب
                    </a>
                    <select class="border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option>مايو 2025</option>
                        <option>أبريل 2025</option>
                        <option>مارس 2025</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Earnings Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <i class="fas fa-money-bill-wave text-green-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">أرباح هذا الشهر</p>
                    <p class="text-2xl font-bold text-gray-900">${{ monthly_earnings|floatformat:2 }}</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-green-600 font-medium">+15%</span>
                <span class="text-gray-500 mr-2">عن الشهر الماضي</span>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="fas fa-calendar-check text-blue-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">الحصص المكتملة</p>
                    <p class="text-2xl font-bold text-gray-900">{{ completed_lessons }}</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-gray-500">حصة هذا الشهر</span>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">ساعات التدريس</p>
                    <p class="text-2xl font-bold text-gray-900">{{ teaching_hours }}</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-gray-500">ساعة هذا الشهر</span>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">متوسط الساعة</p>
                    <p class="text-2xl font-bold text-gray-900">${{ hourly_average|default:"0" }}</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-gray-500">معدل الأجر</span>
            </div>
        </div>
    </div>

    <!-- Earnings Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Monthly Breakdown -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-chart-bar text-islamic-primary ml-2"></i>
                تفصيل الأرباح الشهرية
            </h3>
            <div class="space-y-4">
                {% for duration, data in earnings_breakdown.items %}
                {% if data.lessons_completed > 0 %}
                <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <p class="font-medium text-gray-900">حصص {{ duration }} دقيقة</p>
                            <p class="text-sm text-gray-600">{{ data.lessons_completed }} حصة مكتملة</p>
                        </div>
                        <span class="font-bold text-green-600 text-lg">${{ data.amount|floatformat:2 }}</span>
                    </div>

                    <div class="grid grid-cols-3 gap-3 text-xs bg-white p-3 rounded border">
                        <div class="text-center">
                            <span class="block text-gray-600">سعر الحصة</span>
                            <span class="block font-medium text-blue-600">${{ data.lesson_price|floatformat:2 }}</span>
                        </div>
                        <div class="text-center">
                            <span class="block text-gray-600">نسبة العمولة</span>
                            <span class="block font-medium text-purple-600">{{ data.commission_rate|floatformat:0 }}%</span>
                        </div>
                        <div class="text-center">
                            <span class="block text-gray-600">ربح الحصة</span>
                            <span class="block font-medium text-green-600">
                                ${% widthratio data.lesson_price 100 data.commission_rate %}
                            </span>
                        </div>
                    </div>

                    <div class="mt-2 text-xs text-gray-600 text-center">
                        الحساب: {{ data.lessons_completed }} حصة × ${% widthratio data.lesson_price 100 data.commission_rate %} = ${{ data.amount|floatformat:2 }}
                    </div>
                </div>
                {% endif %}
                {% endfor %}

                {% if monthly_earnings > 0 %}
                <div class="border-t pt-4">
                    <div class="bg-islamic-light-blue p-4 rounded-lg">
                        <div class="flex justify-between items-center mb-3">
                            <span class="font-semibold text-islamic-primary text-lg">إجمالي الأرباح هذا الشهر</span>
                            <span class="font-bold text-2xl text-islamic-primary">${{ monthly_earnings|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-center">
                            <a href="{% url 'teacher_payouts' %}" class="bg-islamic-primary text-white px-6 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200 flex items-center">
                                <i class="fas fa-money-bill-wave ml-2"></i>
                                طلب سحب الأرباح
                            </a>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-coins text-gray-400 text-4xl mb-3"></i>
                    <p class="text-gray-500">لا توجد أرباح هذا الشهر</p>
                    <p class="text-sm text-gray-400">ستظهر أرباحك هنا عند إكمال الحصص</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Payment History -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-history text-islamic-primary ml-2"></i>
                سجل المدفوعات
            </h3>
            <div class="space-y-4">
                {% for payment in payment_history %}
                <div class="flex justify-between items-center p-3 border {% if payment.is_current %}border-yellow-200 bg-yellow-50{% else %}border-gray-200{% endif %} rounded-lg">
                    <div>
                        <p class="font-medium text-gray-900">{{ payment.month_name }} {{ payment.year }}</p>
                        <p class="text-sm text-gray-600">{{ payment.status_text }}</p>
                    </div>
                    <div class="text-left">
                        <span class="font-bold {% if payment.is_paid %}text-green-600{% elif payment.is_current %}text-blue-600{% else %}text-gray-600{% endif %}">${{ payment.amount|floatformat:2 }}</span>
                        <p class="text-xs {% if payment.is_paid %}text-green-600{% elif payment.is_current %}text-blue-600{% else %}text-gray-600{% endif %}">
                            {{ payment.status_text }}
                        </p>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-4">
                    <p class="text-gray-500">لا يوجد سجل مدفوعات</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Earnings Chart Placeholder -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-chart-area text-islamic-primary ml-2"></i>
            رسم بياني للأرباح
        </h3>
        <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div class="text-center">
                <i class="fas fa-chart-line text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">سيتم إضافة الرسم البياني قريباً</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
