# 🔧 دليل استكشاف الأخطاء - نظام قرآنيا التعليمي

## 🚨 المشاكل الشائعة وحلولها

### 1. مشاكل التثبيت

#### ❌ Python غير موجود
```
'python' is not recognized as an internal or external command
```
**الحل:**
1. قم بتثبيت Python من https://www.python.org/downloads/
2. تأكد من إضافة Python إلى PATH أثناء التثبيت
3. أعد تشغيل Command Prompt

#### ❌ خطأ في pip
```
pip is not recognized as an internal or external command
```
**الحل:**
1. أعد تثبيت Python مع تفعيل خيار "Add to PATH"
2. أو استخدم: `python -m pip install -r requirements.txt`

#### ❌ خطأ في إنشاء البيئة الافتراضية
```
Error: Unable to create virtual environment
```
**الحل:**
1. تأ<PERSON><PERSON> من وجود مساحة كافية على القرص
2. شغل Command Prompt كمدير
3. جرب: `python -m venv venv --clear`

### 2. مشاكل قاعدة البيانات

#### ❌ خطأ في Migration
```
django.db.utils.OperationalError: no such table
```
**الحل:**
```bash
python manage.py makemigrations
python manage.py migrate
```

#### ❌ خطأ في إنشاء المدير
```
django.db.utils.IntegrityError: UNIQUE constraint failed
```
**الحل:**
1. احذف قاعدة البيانات: `del db.sqlite3`
2. أعد إنشاء قاعدة البيانات:
```bash
python manage.py makemigrations
python manage.py migrate
python manage.py createsuperuser
```

### 3. مشاكل الخادم

#### ❌ الخادم لا يبدأ
```
Error: That port is already in use
```
**الحل:**
1. أوقف أي خادم يعمل على المنفذ 8080
2. أو استخدم منفذ آخر: `python manage.py runserver 8081`
3. أو أعد تشغيل الكمبيوتر

#### ❌ خطأ في الوصول للموقع
```
This site can't be reached
```
**الحل:**
1. تأكد من تشغيل الخادم بنجاح
2. استخدم الرابط الصحيح: http://localhost:8080
3. جرب: http://127.0.0.1:8080
4. تأكد من عدم حجب Firewall للمنفذ

### 4. مشاكل تسجيل الدخول

#### ❌ بيانات تسجيل الدخول خاطئة
**الحل:**
1. **للمدير**: استخدم البيانات التي أدخلتها عند تشغيل `createsuperuser`
2. **للمعلم**: teacher1 / teacher123
3. **للطالب**: student1 / student123
4. تأكد من عدم وجود مسافات إضافية

#### ❌ المستخدم ليس مدير
```
You don't have permission to access this page
```
**الحل:**
```bash
python update_system.py
```

### 5. مشاكل الواجهة

#### ❌ التصميم لا يظهر بشكل صحيح
**الحل:**
1. امسح cache المتصفح (Ctrl+F5)
2. تأكد من تشغيل: `python manage.py collectstatic`
3. تحقق من اتصال الإنترنت (للخطوط والأيقونات)

#### ❌ الخطوط العربية لا تظهر
**الحل:**
1. تحقق من اتصال الإنترنت
2. امسح cache المتصفح
3. جرب متصفح آخر

### 6. مشاكل الأداء

#### ❌ النظام بطيء
**الحل:**
1. أعد تشغيل الخادم
2. تأكد من عدم تشغيل برامج أخرى ثقيلة
3. أعد تشغيل الكمبيوتر

#### ❌ استهلاك ذاكرة عالي
**الحل:**
1. أوقف الخادم عند عدم الاستخدام
2. أعد تشغيل النظام بانتظام

## 🔍 أدوات التشخيص

### فحص حالة النظام
```bash
# فحص Python
python --version

# فحص Django
python -c "import django; print(django.get_version())"

# فحص قاعدة البيانات
python manage.py check

# فحص الملفات الثابتة
python manage.py collectstatic --dry-run
```

### فحص المنافذ
```bash
# فحص المنفذ 8080
netstat -an | findstr :8080
```

### فحص السجلات
راجع رسائل الخطأ في terminal عند تشغيل الخادم

## 📋 خطوات الإصلاح الشامل

إذا واجهت مشاكل متعددة، جرب هذه الخطوات:

### 1. إعادة تعيين كاملة
```bash
# احذف البيئة الافتراضية
rmdir /s venv

# احذف قاعدة البيانات
del db.sqlite3

# أعد تشغيل الإعداد
setup.bat
```

### 2. فحص شامل
```bash
# فعل البيئة الافتراضية
venv\Scripts\activate

# فحص التثبيت
pip list

# فحص Django
python manage.py check

# اختبار الخادم
python manage.py runserver 8080 --verbosity=2
```

## 🆘 طلب المساعدة

### قبل طلب المساعدة، اجمع هذه المعلومات:

1. **نظام التشغيل**: Windows 10/11
2. **إصدار Python**: `python --version`
3. **رسالة الخطأ الكاملة**: انسخ النص كاملاً
4. **الخطوات التي أدت للخطأ**: اشرح ما فعلته قبل ظهور الخطأ
5. **لقطة شاشة**: إذا كان الخطأ في الواجهة

### طرق التواصل:
- **GitHub Issues**: لتقارير الأخطاء
- **البريد الإلكتروني**: {{ ACADEMY_EMAIL }}
- **الوثائق**: README.md

## 💡 نصائح لتجنب المشاكل

### أفضل الممارسات:
1. **أوقف الخادم بشكل صحيح**: استخدم Ctrl+C
2. **لا تحذف ملفات النظام**: تجنب حذف ملفات Python أو Django
3. **استخدم البيئة الافتراضية**: لا تثبت الحزم خارجها
4. **احتفظ بنسخة احتياطية**: من قاعدة البيانات والإعدادات
5. **حدث النظام بانتظام**: شغل `git pull` للتحديثات

### صيانة دورية:
```bash
# أسبوعياً
python manage.py check
python manage.py collectstatic

# شهرياً
pip list --outdated
```

---

**إذا لم تجد حلاً لمشكلتك هنا، لا تتردد في التواصل معنا!**

🕌 **{{ SITE_NAME }}** - نحن هنا لمساعدتك
