{% extends 'base.html' %}
{% load static %}

{% block title %}الرسائل الداخلية{% endblock %}

{% block extra_css %}
<style>
    .message-card {
        transition: all 0.3s ease;
        border-right: 4px solid transparent;
    }
    .message-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .message-unread {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-right-color: #3b82f6;
    }
    .message-support {
        border-right-color: #f59e0b;
    }
    .message-announcement {
        border-right-color: #ef4444;
    }
    .message-private {
        border-right-color: #10b981;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-islamic-primary via-islamic-secondary to-islamic-accent">
    <div class="container mx-auto px-4 py-8">

        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-islamic-primary mb-2">الرسائل الداخلية</h1>
                    <p class="text-gray-600">تواصل مع المستخدمين الآخرين في المنصة</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="{% url 'notifications:compose_message' %}" class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        رسالة جديدة
                    </a>
                    <a href="{% url 'notifications:list' %}" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-bell ml-2"></i>
                        الإشعارات
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="stats-card rounded-xl shadow-lg p-6 text-white">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-inbox text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-white text-opacity-80 text-sm">الرسائل المستلمة</p>
                        <p class="text-2xl font-bold">{{ received_messages.paginator.count }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-envelope-open text-blue-600 text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">غير مقروءة</p>
                        <p class="text-2xl font-bold text-blue-600">{{ unread_count }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4">
                        <i class="fas fa-paper-plane text-green-600 text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">الرسائل المرسلة</p>
                        <p class="text-2xl font-bold text-green-600">{{ sent_messages|length }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages List -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Received Messages -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">
                        <i class="fas fa-inbox text-blue-600 ml-2"></i>
                        الرسائل المستلمة
                    </h2>

                    {% if received_messages %}
                    <div class="space-y-4">
                        {% for message in received_messages %}
                        <div class="message-card bg-gray-50 rounded-lg p-4 cursor-pointer
                                    {% if not message.is_read %}message-unread{% endif %}
                                    {% if message.message_type == 'support' %}message-support{% endif %}
                                    {% if message.message_type == 'announcement' %}message-announcement{% endif %}
                                    {% if message.message_type == 'private' %}message-private{% endif %}"
                             onclick="window.location.href='{% url 'notifications:message_detail' message.id %}'">

                            <div class="flex items-start justify-between">
                                <div class="flex items-start flex-1">
                                    <!-- Sender Avatar -->
                                    <div class="flex-shrink-0 ml-4">
                                        <div class="w-10 h-10 rounded-full flex items-center justify-center
                                                    {% if message.sender.user_type == 'admin' %}bg-yellow-100{% endif %}
                                                    {% if message.sender.user_type == 'teacher' %}bg-green-100{% endif %}
                                                    {% if message.sender.user_type == 'student' %}bg-blue-100{% endif %}">
                                            {% if message.sender.user_type == 'admin' %}
                                                <i class="fas fa-crown text-yellow-600"></i>
                                            {% elif message.sender.user_type == 'teacher' %}
                                                <i class="fas fa-chalkboard-teacher text-green-600"></i>
                                            {% else %}
                                                <i class="fas fa-user-graduate text-blue-600"></i>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Message Content -->
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-2">
                                            <h3 class="text-lg font-semibold text-gray-900">{{ message.subject }}</h3>
                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                {% if message.message_type == 'support' %}
                                                    <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">دعم</span>
                                                {% elif message.message_type == 'announcement' %}
                                                    <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">إعلان</span>
                                                {% else %}
                                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">خاص</span>
                                                {% endif %}

                                                {% if not message.is_read %}
                                                    <span class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></span>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <p class="text-gray-600 text-sm mb-2">{{ message.content|striptags|truncatewords:15 }}</p>

                                        <div class="flex items-center justify-between text-sm text-gray-500">
                                            <span>من: {{ message.sender.get_full_name }}</span>
                                            <span>{{ message.created_at|timesince }} مضت</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Pagination -->
                    {% if received_messages.has_other_pages %}
                    <div class="mt-6 flex items-center justify-center">
                        <nav class="flex items-center space-x-2 space-x-reverse">
                            {% if received_messages.has_previous %}
                                <a href="?page={{ received_messages.previous_page_number }}"
                                   class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% endif %}

                            <span class="px-4 py-2 bg-islamic-primary text-white rounded-lg">
                                {{ received_messages.number }} من {{ received_messages.paginator.num_pages }}
                            </span>

                            {% if received_messages.has_next %}
                                <a href="?page={{ received_messages.next_page_number }}"
                                   class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                    {% endif %}

                    {% else %}
                    <!-- Empty State -->
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-inbox text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد رسائل</h3>
                        <p class="text-gray-600">لم تتلق أي رسائل حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Sent Messages Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">
                        <i class="fas fa-paper-plane text-green-600 ml-2"></i>
                        آخر الرسائل المرسلة
                    </h2>

                    {% if sent_messages %}
                    <div class="space-y-4">
                        {% for message in sent_messages %}
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 ml-3">
                                    <div class="w-8 h-8 rounded-full flex items-center justify-center
                                                {% if message.recipient.user_type == 'admin' %}bg-yellow-100{% endif %}
                                                {% if message.recipient.user_type == 'teacher' %}bg-green-100{% endif %}
                                                {% if message.recipient.user_type == 'student' %}bg-blue-100{% endif %}">
                                        {% if message.recipient.user_type == 'admin' %}
                                            <i class="fas fa-crown text-yellow-600 text-sm"></i>
                                        {% elif message.recipient.user_type == 'teacher' %}
                                            <i class="fas fa-chalkboard-teacher text-green-600 text-sm"></i>
                                        {% else %}
                                            <i class="fas fa-user-graduate text-blue-600 text-sm"></i>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-gray-900 mb-1">{{ message.subject|truncatechars:30 }}</h4>
                                    <p class="text-xs text-gray-600 mb-2">إلى: {{ message.recipient.get_full_name }}</p>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-500">{{ message.created_at|timesince }} مضت</span>
                                        {% if message.is_read %}
                                            <i class="fas fa-check-double text-green-500 text-xs" title="تم القراءة"></i>
                                        {% else %}
                                            <i class="fas fa-check text-gray-400 text-xs" title="تم الإرسال"></i>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-8">
                        <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-paper-plane text-gray-400"></i>
                        </div>
                        <p class="text-gray-600 text-sm">لم ترسل أي رسائل بعد</p>
                    </div>
                    {% endif %}

                    <!-- Quick Actions -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <a href="{% url 'notifications:compose_message' %}"
                           class="w-full bg-islamic-primary text-white px-4 py-3 rounded-lg hover:bg-islamic-secondary transition-colors text-center block">
                            <i class="fas fa-plus ml-2"></i>
                            إنشاء رسالة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh unread count every 30 seconds
setInterval(function() {
    // You can add AJAX call here to update unread count
}, 30000);
</script>
{% endblock %}
