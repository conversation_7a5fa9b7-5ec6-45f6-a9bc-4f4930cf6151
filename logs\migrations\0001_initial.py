# Generated by Django 4.2.7 on 2025-05-23 23:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='وقت الحدث')),
                ('action', models.CharField(max_length=100, verbose_name='الإجراء')),
                ('details', models.TextField(verbose_name='التفاصيل')),
                ('_data', models.TextField(blank=True, db_column='data', null=True, verbose_name='بيانات إضافية')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='system_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل النظام',
                'verbose_name_plural': 'سجلات النظام',
                'ordering': ['-timestamp'],
            },
        ),
    ]
