# Generated by Django 4.2.7 on 2025-05-22 17:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SupportTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket_number', models.CharField(max_length=20, unique=True, verbose_name='رقم التذكرة')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التذكرة')),
                ('description', models.TextField(verbose_name='وصف المشكلة')),
                ('category', models.CharField(choices=[('technical', 'مشكلة تقنية'), ('account', 'مشكلة في الحساب'), ('payment', 'مشكلة في الدفع'), ('lesson', 'مشكلة في الحصة'), ('general', 'استفسار عام'), ('complaint', 'شكوى'), ('suggestion', 'اقتراح')], default='general', max_length=20, verbose_name='فئة التذكرة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('open', 'مفتوحة'), ('in_progress', 'قيد المعالجة'), ('waiting_response', 'في انتظار الرد'), ('resolved', 'محلولة'), ('closed', 'مغلقة')], default='open', max_length=20, verbose_name='حالة التذكرة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('closed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإغلاق')),
                ('is_read_by_admin', models.BooleanField(default=False, verbose_name='مقروءة من المدير')),
                ('is_read_by_user', models.BooleanField(default=True, verbose_name='مقروءة من المستخدم')),
                ('admin_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات المدير')),
                ('assigned_to', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'admin'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_tickets', to=settings.AUTH_USER_MODEL, verbose_name='مُعيّن إلى')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_tickets', to=settings.AUTH_USER_MODEL, verbose_name='منشئ التذكرة')),
            ],
            options={
                'verbose_name': 'تذكرة دعم',
                'verbose_name_plural': 'تذاكر الدعم',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SupportTicketResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرد')),
                ('is_admin_response', models.BooleanField(default=False, verbose_name='رد من المدير')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='support_attachments/', verbose_name='مرفق')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='support_responses', to=settings.AUTH_USER_MODEL, verbose_name='كاتب الرد')),
                ('ticket', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='support.supportticket', verbose_name='التذكرة')),
            ],
            options={
                'verbose_name': 'رد تذكرة دعم',
                'verbose_name_plural': 'ردود تذاكر الدعم',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='SupportMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الرسالة')),
                ('content', models.TextField(verbose_name='محتوى الرسالة')),
                ('message_type', models.CharField(choices=[('announcement', 'إعلان'), ('warning', 'تحذير'), ('reminder', 'تذكير'), ('update', 'تحديث'), ('maintenance', 'صيانة')], default='announcement', max_length=20, verbose_name='نوع الرسالة')),
                ('is_urgent', models.BooleanField(default=False, verbose_name='عاجل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('recipients', models.ManyToManyField(related_name='received_support_messages', to=settings.AUTH_USER_MODEL, verbose_name='المستقبلون')),
                ('sent_by', models.ForeignKey(limit_choices_to={'user_type': 'admin'}, on_delete=django.db.models.deletion.CASCADE, related_name='sent_support_messages', to=settings.AUTH_USER_MODEL, verbose_name='مُرسل بواسطة')),
            ],
            options={
                'verbose_name': 'رسالة دعم',
                'verbose_name_plural': 'رسائل الدعم',
                'ordering': ['-created_at'],
            },
        ),
    ]
