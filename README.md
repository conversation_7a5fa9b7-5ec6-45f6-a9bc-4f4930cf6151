# 🕌 نظام قرآنيا التعليمي - Qurania LMS

منصة تعليمية متخصصة في تعليم القرآن الكريم وعلومه بأحدث التقنيات والأساليب التعليمية.

## 📋 نظرة عامة

نظام قرآنيا هو نظام إدارة تعلم (LMS) مصمم خصيصاً لتعليم القرآن الكريم والعلوم الإسلامية. يوفر النظام بيئة تعليمية تفاعلية تجمع بين المعلمين المتخصصين والطلاب الراغبين في تعلم القرآن الكريم.

## ✨ المميزات الرئيسية

### 👥 أنواع المستخدمين
- **المدير**: إدارة شاملة للنظام والمستخدمين والدورات
- **المعلم**: إدارة الحصص والطلاب وتتبع التقدم
- **الطالب**: حضور الحصص وتتبع التقدم الشخصي

### 🎯 الوظائف الأساسية
- ✅ نظام مصادقة متقدم مع أنواع مستخدمين مختلفة
- ✅ إدارة الدورات والمناهج التعليمية
- ✅ جدولة الحصص المرنة
- ✅ تتبع تقدم الطلاب
- ✅ نظام التقييمات والتقييم
- ✅ الإشعارات والرسائل
- ✅ نظام الدعم الفني
- ✅ تقارير شاملة وإحصائيات

### 🎨 التصميم
- 🌙 تصميم إسلامي عصري بألوان الأخضر والذهبي
- 📱 تصميم متجاوب يعمل على جميع الأجهزة
- 🔤 دعم كامل للغة العربية مع خطوط مناسبة
- 🎯 واجهة مستخدم بديهية وسهلة الاستخدام

## 🛠️ التقنيات المستخدمة

### Backend
- **Django 4.2.7** - إطار العمل الرئيسي
- **Python 3.8+** - لغة البرمجة
- **SQLite** - قاعدة البيانات (قابلة للتطوير إلى PostgreSQL)

### Frontend
- **HTML5 & CSS3** - هيكل وتنسيق الصفحات
- **Tailwind CSS** - إطار عمل CSS
- **JavaScript** - التفاعل والديناميكية
- **Font Awesome** - الأيقونات
- **Google Fonts** - الخطوط العربية

### أدوات إضافية
- **Pillow** - معالجة الصور
- **Requests** - طلبات HTTP
- **pytz** - إدارة المناطق الزمنية

## 📦 التثبيت والإعداد

### المتطلبات الأساسية
- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- Git

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/qurania-lms.git
cd qurania-lms
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate  # على Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد قاعدة البيانات**
```bash
python manage.py makemigrations
python manage.py migrate
```

5. **إنشاء مستخدم مدير**
```bash
python manage.py createsuperuser
python setup_admin.py  # لتحديث نوع المستخدم
```

6. **تشغيل الخادم**
```bash
python manage.py runserver
```

7. **الوصول للنظام**
افتح المتصفح وانتقل إلى: `http://localhost:8000`

## 🗂️ هيكل المشروع

```
qurania/
├── qurania_lms/          # إعدادات المشروع الرئيسية
├── users/                # إدارة المستخدمين والمصادقة
├── courses/              # إدارة الدورات والمناهج
├── lessons/              # إدارة الحصص والجدولة
├── notifications/        # الإشعارات والرسائل والدعم
├── templates/            # قوالب HTML
│   ├── auth/            # صفحات المصادقة
│   ├── dashboard/       # لوحات التحكم
│   ├── admin/           # صفحات الإدارة
│   ├── teacher/         # صفحات المعلم
│   ├── student/         # صفحات الطالب
│   ├── common/          # صفحات مشتركة
│   └── static_pages/    # صفحات ثابتة
├── static/               # الملفات الثابتة (CSS, JS, Images)
├── media/                # ملفات المستخدمين المرفوعة
├── requirements.txt      # متطلبات Python
└── README.md            # هذا الملف
```

## 👤 أنواع المستخدمين والصلاحيات

### 🔧 المدير (Admin)
- إدارة جميع المستخدمين
- إنشاء وتعديل الدورات
- عرض التقارير والإحصائيات
- إدارة نظام الدعم الفني
- إعدادات النظام العامة

### 👨‍🏫 المعلم (Teacher)
- إدارة الحصص الشخصية
- تتبع تقدم الطلاب
- تسجيل الحضور والغياب
- إنشاء التقييمات
- التواصل مع الطلاب

### 👨‍🎓 الطالب (Student)
- حضور الحصص المجدولة
- تتبع التقدم الشخصي
- عرض الدرجات والتقييمات
- التواصل مع المعلمين
- أرشيف الحصص السابقة

## 🎨 لوحات التحكم

### لوحة تحكم المدير
- إحصائيات شاملة للنظام
- إدارة المستخدمين والدورات
- تقارير الأداء والاستخدام
- إدارة تذاكر الدعم الفني

### لوحة تحكم المعلم
- جدول الحصص الأسبوعي
- قائمة الطلاب النشطين
- إحصائيات الأرباح والتقييمات
- أدوات إدارة الحصص

### لوحة تحكم الطالب
- الحصص القادمة واليومية
- تقدم الحفظ والمراجعة
- الدورات المسجل بها
- الإنجازات والشهادات

## 🔧 الإعدادات والتخصيص

### إعدادات قاعدة البيانات
يمكن تغيير قاعدة البيانات من SQLite إلى PostgreSQL أو MySQL عبر تعديل ملف `settings.py`.

### إعدادات البريد الإلكتروني
لتفعيل إرسال رسائل إعادة تعيين كلمة المرور، قم بتحديث إعدادات البريد الإلكتروني في `settings.py`.

### التخصيص
- الألوان والتصميم: `static/css/custom.css`
- الشعار والصور: `static/images/`
- النصوص والترجمات: ملفات القوالب

## 🚀 التطوير المستقبلي

### الميزات المخططة
- [ ] تكامل Jitsi API للحصص المباشرة
- [ ] نظام الدفع الإلكتروني
- [ ] تطبيق الجوال (React Native)
- [ ] نظام الشهادات الرقمية
- [ ] تحليلات متقدمة بالذكاء الاصطناعي
- [ ] دعم اللغات المتعددة

### التحسينات التقنية
- [ ] API RESTful كامل
- [ ] نظام التخزين السحابي
- [ ] تحسين الأداء والسرعة
- [ ] اختبارات تلقائية شاملة
- [ ] نظام CI/CD

## 🤝 المساهمة

نرحب بالمساهمات من المطورين! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل والدعم

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://qurania.com
- **GitHub Issues**: لتقارير الأخطاء والاقتراحات

## 🙏 شكر وتقدير

- فريق Django لإطار العمل الرائع
- مجتمع Tailwind CSS للتصميم الجميل
- جميع المساهمين في المشروع

---

**"وَقُلْ رَبِّ زِدْنِي عِلْمًا"** - سورة طه، آية 114

تم تطوير هذا النظام بحب وإخلاص لخدمة كتاب الله العزيز 🤲
