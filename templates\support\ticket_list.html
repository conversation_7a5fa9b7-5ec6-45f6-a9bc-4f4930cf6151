{% extends 'base.html' %}
{% load static %}

{% block title %}تذاكر الدعم{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-islamic-primary via-islamic-secondary to-islamic-accent">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-islamic-primary mb-2">تذاكر الدعم</h1>
                    <p class="text-gray-600">إدارة ومتابعة تذاكر الدعم الفني</p>
                </div>
                <div class="flex space-x-4 space-x-reverse">
                    {% if not user.is_admin %}
                    <a href="{% url 'create_ticket' %}" class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        تذكرة جديدة
                    </a>
                    {% endif %}
                    {% if user.is_admin %}
                    <a href="{% url 'admin_support' %}" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-dashboard ml-2"></i>
                        لوحة التحكم
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select name="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        <option value="open" {% if status_filter == 'open' %}selected{% endif %}>مفتوحة</option>
                        <option value="in_progress" {% if status_filter == 'in_progress' %}selected{% endif %}>قيد المعالجة</option>
                        <option value="waiting_response" {% if status_filter == 'waiting_response' %}selected{% endif %}>في انتظار الرد</option>
                        <option value="resolved" {% if status_filter == 'resolved' %}selected{% endif %}>محلولة</option>
                        <option value="closed" {% if status_filter == 'closed' %}selected{% endif %}>مغلقة</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الأولوية</label>
                    <select name="priority" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الأولويات</option>
                        <option value="low" {% if priority_filter == 'low' %}selected{% endif %}>منخفضة</option>
                        <option value="medium" {% if priority_filter == 'medium' %}selected{% endif %}>متوسطة</option>
                        <option value="high" {% if priority_filter == 'high' %}selected{% endif %}>عالية</option>
                        <option value="urgent" {% if priority_filter == 'urgent' %}selected{% endif %}>عاجلة</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                    <select name="category" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الفئات</option>
                        <option value="technical" {% if category_filter == 'technical' %}selected{% endif %}>مشكلة تقنية</option>
                        <option value="account" {% if category_filter == 'account' %}selected{% endif %}>مشكلة في الحساب</option>
                        <option value="payment" {% if category_filter == 'payment' %}selected{% endif %}>مشكلة في الدفع</option>
                        <option value="lesson" {% if category_filter == 'lesson' %}selected{% endif %}>مشكلة في الحصة</option>
                        <option value="general" {% if category_filter == 'general' %}selected{% endif %}>استفسار عام</option>
                        <option value="complaint" {% if category_filter == 'complaint' %}selected{% endif %}>شكوى</option>
                        <option value="suggestion" {% if category_filter == 'suggestion' %}selected{% endif %}>اقتراح</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <div class="flex">
                        <input type="text" name="search" value="{{ search_query }}" placeholder="البحث في التذاكر..." 
                               class="flex-1 border border-gray-300 rounded-r-lg px-3 py-2 focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <button type="submit" class="bg-islamic-primary text-white px-4 py-2 rounded-l-lg hover:bg-islamic-secondary transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Tickets List -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            {% if tickets %}
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم التذكرة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنوان</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأولوية</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            {% if user.is_admin %}
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                            {% endif %}
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for ticket in tickets %}
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                                    #{{ ticket.ticket_number }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{{ ticket.title }}</div>
                                <div class="text-sm text-gray-500">{{ ticket.description|truncatechars:50 }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">{{ ticket.get_category_display }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm {{ ticket.get_priority_color }} font-medium">
                                    {{ ticket.get_priority_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ ticket.get_status_color }} bg-opacity-10">
                                    {{ ticket.get_status_display }}
                                </span>
                            </td>
                            {% if user.is_admin %}
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ ticket.created_by.get_full_name }}</div>
                                <div class="text-sm text-gray-500">{{ ticket.created_by.get_user_type_display }}</div>
                            </td>
                            {% endif %}
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ ticket.created_at|date:"Y/m/d H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{% url 'ticket_detail' ticket.id %}" class="text-islamic-primary hover:text-islamic-secondary ml-3">
                                    عرض
                                </a>
                                {% if user.is_admin or ticket.can_be_closed_by_user %}
                                <button onclick="closeTicket({{ ticket.id }})" class="text-red-600 hover:text-red-900">
                                    إغلاق
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if tickets.has_other_pages %}
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if tickets.has_previous %}
                        <a href="?page={{ tickets.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            السابق
                        </a>
                        {% endif %}
                        {% if tickets.has_next %}
                        <a href="?page={{ tickets.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            التالي
                        </a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                عرض
                                <span class="font-medium">{{ tickets.start_index }}</span>
                                إلى
                                <span class="font-medium">{{ tickets.end_index }}</span>
                                من
                                <span class="font-medium">{{ tickets.paginator.count }}</span>
                                نتيجة
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if tickets.has_previous %}
                                <a href="?page={{ tickets.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                                {% endif %}
                                
                                {% for num in tickets.paginator.page_range %}
                                {% if tickets.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-islamic-primary bg-islamic-primary text-sm font-medium text-white">
                                    {{ num }}
                                </span>
                                {% else %}
                                <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                                {% endif %}
                                {% endfor %}
                                
                                {% if tickets.has_next %}
                                <a href="?page={{ tickets.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% else %}
            <div class="text-center py-12">
                <i class="fas fa-inbox text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد تذاكر</h3>
                <p class="text-gray-500 mb-6">لم يتم العثور على تذاكر دعم مطابقة للفلاتر المحددة</p>
                {% if not user.is_admin %}
                <a href="{% url 'create_ticket' %}" class="bg-islamic-primary text-white px-6 py-3 rounded-lg hover:bg-islamic-secondary transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء تذكرة جديدة
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function closeTicket(ticketId) {
    if (confirm('هل أنت متأكد من إغلاق هذه التذكرة؟')) {
        fetch(`/support/tickets/${ticketId}/close/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || 'حدث خطأ أثناء إغلاق التذكرة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إغلاق التذكرة');
        });
    }
}
</script>
{% endblock %}
