{% extends 'base.html' %}

{% block title %}إدارة المستخدمين - نظام قرآنيا التعليمي{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-6">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-islamic-dark">إدارة المستخدمين</h1>
                    <p class="text-gray-600 mt-1">إدارة جميع المستخدمين في النظام</p>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button class="bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-user-plus ml-2"></i>
                        إضافة مستخدم جديد
                    </button>
                    <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200">
                        <i class="fas fa-file-export ml-2"></i>
                        تصدير البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">البحث والتصفية</h3>

        <form method="GET" action="{% url 'admin_users' %}">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <input type="text" name="search" value="{{ search_query|default:'' }}"
                           placeholder="البحث بالاسم أو البريد الإلكتروني..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع المستخدم</label>
                    <select name="user_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الأنواع</option>
                        <option value="admin" {% if user_type_filter == 'admin' %}selected{% endif %}>مدير</option>
                        <option value="teacher" {% if user_type_filter == 'teacher' %}selected{% endif %}>معلم</option>
                        <option value="student" {% if user_type_filter == 'student' %}selected{% endif %}>طالب</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-islamic-primary focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>

                <div class="flex items-end space-x-2 space-x-reverse">
                    <button type="submit" class="flex-1 bg-islamic-primary text-white px-4 py-2 rounded-lg hover:bg-islamic-light transition-colors duration-200">
                        <i class="fas fa-search ml-2"></i>
                        بحث
                    </button>
                    <a href="{% url 'admin_users' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_users|default:0 }}</p>
                    <p class="text-xs text-gray-500 mt-1">+{{ new_users_this_month|default:0 }} هذا الشهر</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <i class="fas fa-user-graduate text-green-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">الطلاب</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_students|default:0 }}</p>
                    <p class="text-xs text-gray-500 mt-1">{{ students_percentage }}% من المجموع</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <i class="fas fa-chalkboard-teacher text-purple-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">المعلمون</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_teachers|default:0 }}</p>
                    <p class="text-xs text-gray-500 mt-1">{{ total_admins|default:0 }} مديرين</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                    <i class="fas fa-user-check text-yellow-600 text-xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">المستخدمون النشطون</p>
                    <p class="text-2xl font-bold text-gray-900">{{ active_users|default:0 }}</p>
                    <p class="text-xs text-gray-500 mt-1">{{ active_percentage }}% معدل النشاط</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">قائمة المستخدمين</h3>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البريد الإلكتروني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التسجيل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for user_stat in users_with_stats %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    {% if user_stat.user.profile_picture %}
                                        <img class="h-10 w-10 rounded-full object-cover" src="{{ user_stat.user.profile_picture.url }}" alt="{{ user_stat.user.get_full_name }}">
                                    {% else %}
                                        <div class="h-10 w-10 rounded-full bg-islamic-light-blue flex items-center justify-center">
                                            <i class="fas fa-user text-islamic-primary"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ user_stat.user.get_full_name|default:user_stat.user.username }}
                                    </div>
                                    <div class="text-sm text-gray-500">@{{ user_stat.user.username }}</div>
                                    {% if user_stat.user.user_type == 'teacher' and user_stat.avg_rating > 0 %}
                                        <div class="text-xs text-yellow-600">
                                            <i class="fas fa-star"></i> {{ user_stat.avg_rating|floatformat:1 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if user_stat.user.user_type == 'student' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    طالب
                                </span>
                            {% elif user_stat.user.user_type == 'teacher' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                    معلم
                                </span>
                            {% elif user_stat.user.user_type == 'admin' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    مدير
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user_stat.user.email }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ user_stat.user.created_at|date:"Y-m-d" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if user_stat.user.is_active %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    نشط
                                </span>
                            {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    غير نشط
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <!-- عرض الإحصائيات -->
                                <div class="text-xs text-gray-500 ml-4">
                                    {% if user_stat.user.user_type == 'student' %}
                                        <div>{{ user_stat.enrollments_count }} دورة</div>
                                        <div>{{ user_stat.lessons_count }} حصة</div>
                                    {% elif user_stat.user.user_type == 'teacher' %}
                                        <div>{{ user_stat.enrollments_count }} طالب</div>
                                        <div>{{ user_stat.lessons_count }} حصة</div>
                                    {% endif %}
                                </div>
                                <!-- الإجراءات -->
                                <button class="text-islamic-primary hover:text-islamic-light" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-blue-600 hover:text-blue-900" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% if user_stat.user.user_type == 'student' %}
                                    <a href="{% url 'admin_create_enrollment' %}?student={{ user_stat.user.id }}" class="text-green-600 hover:text-green-900" title="تسجيل في دورة">
                                        <i class="fas fa-plus-circle"></i>
                                    </a>
                                    <a href="{% url 'admin_courses' %}?student={{ user_stat.user.id }}" class="text-blue-600 hover:text-blue-900" title="عرض الدورات">
                                        <i class="fas fa-book"></i>
                                    </a>
                                {% elif user_stat.user.user_type == 'teacher' %}
                                    <a href="{% url 'admin_create_enrollment' %}?teacher={{ user_stat.user.id }}" class="text-green-600 hover:text-green-900" title="إضافة طلاب">
                                        <i class="fas fa-user-plus"></i>
                                    </a>
                                    <a href="{% url 'admin_lessons' %}?teacher={{ user_stat.user.id }}" class="text-purple-600 hover:text-purple-900" title="إدارة الحصص">
                                        <i class="fas fa-calendar"></i>
                                    </a>
                                {% endif %}
                                {% if not user_stat.user.is_superuser %}
                                    <button class="text-red-600 hover:text-red-900" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            لا توجد مستخدمين مطابقين للبحث
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    السابق
                </a>
                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    التالي
                </a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        عرض <span class="font-medium">1</span> إلى <span class="font-medium">10</span> من <span class="font-medium">156</span> نتيجة
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">2</a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">3</a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
