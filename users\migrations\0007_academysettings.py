# Generated by Django 4.2.7 on 2025-05-23 21:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0006_user_was_active'),
    ]

    operations = [
        migrations.CreateModel(
            name='AcademySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('academy_name', models.CharField(default='أكاديمية القرآنية', max_length=100, verbose_name='اسم الأكاديمية')),
                ('academy_email', models.EmailField(default='<EMAIL>', max_length=100, verbose_name='البريد الإلكتروني للأكاديمية')),
                ('academy_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم هاتف الأكاديمية')),
                ('academy_logo', models.ImageField(blank=True, null=True, upload_to='academy/', verbose_name='شعار الأكاديمية')),
                ('academy_address', models.TextField(blank=True, null=True, verbose_name='عنوان الأكاديمية')),
                ('academy_description', models.TextField(blank=True, null=True, verbose_name='وصف الأكاديمية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعدادات الأكاديمية',
                'verbose_name_plural': 'إعدادات الأكاديمية',
            },
        ),
    ]
