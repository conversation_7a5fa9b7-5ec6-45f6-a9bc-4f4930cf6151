{% extends 'base.html' %}

{% block title %}تفاصيل المستخدم - {{ user.get_full_name|default:user.username }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-6">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="{% url 'admin_users' %}" class="text-gray-500 hover:text-gray-700 ml-4">
                        <i class="fas fa-arrow-right text-xl"></i>
                    </a>
                    <div>
                        <h1 class="text-2xl font-bold text-islamic-dark">
                            تفاصيل المستخدم: {{ user.get_full_name|default:user.username }}
                        </h1>
                        <p class="text-gray-600 mt-1">عرض شامل لجميع البيانات والعلاقات المترابطة</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-edit ml-2"></i>
                        تعديل
                    </button>
                    {% if user.user_type == 'student' %}
                        <a href="{% url 'admin_create_enrollment' %}?student={{ user.id }}" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة دورة
                        </a>
                    {% elif user.user_type == 'teacher' %}
                        <a href="{% url 'admin_schedule_lesson' %}?teacher={{ user.id }}" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors duration-200">
                            <i class="fas fa-calendar-plus ml-2"></i>
                            جدولة حصة
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Basic Info -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0 h-16 w-16">
                    {% if user.profile_picture %}
                        <img class="h-16 w-16 rounded-full object-cover" src="{{ user.profile_picture.url }}" alt="{{ user.get_full_name }}">
                    {% else %}
                        <div class="h-16 w-16 rounded-full bg-islamic-light-blue flex items-center justify-center">
                            <i class="fas fa-user text-islamic-primary text-2xl"></i>
                        </div>
                    {% endif %}
                </div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ user.get_full_name|default:user.username }}</h3>
                    <p class="text-gray-600">@{{ user.username }}</p>
                    <div class="flex items-center mt-1">
                        {% if user.user_type == 'student' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                طالب
                            </span>
                        {% elif user.user_type == 'teacher' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                معلم
                            </span>
                        {% elif user.user_type == 'admin' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                مدير
                            </span>
                        {% endif %}
                        {% if user.is_active %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 mr-2">
                                نشط
                            </span>
                        {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 mr-2">
                                غير نشط
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">البريد الإلكتروني:</span>
                    <span class="text-gray-900">{{ user.email }}</span>
                </div>
                {% if user.phone %}
                <div class="flex justify-between">
                    <span class="text-gray-600">الهاتف:</span>
                    <span class="text-gray-900">{{ user.phone }}</span>
                </div>
                {% endif %}
                <div class="flex justify-between">
                    <span class="text-gray-600">تاريخ التسجيل:</span>
                    <span class="text-gray-900">{{ user.created_at|date:"Y-m-d" }}</span>
                </div>
                {% if user.date_of_birth %}
                <div class="flex justify-between">
                    <span class="text-gray-600">تاريخ الميلاد:</span>
                    <span class="text-gray-900">{{ user.date_of_birth|date:"Y-m-d" }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="lg:col-span-2 grid grid-cols-2 md:grid-cols-4 gap-4">
            {% if user.user_type == 'student' %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ stats.total_enrollments|default:0 }}</div>
                        <div class="text-sm text-gray-600">إجمالي الدورات</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{{ stats.active_enrollments|default:0 }}</div>
                        <div class="text-sm text-gray-600">دورات نشطة</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">{{ stats.total_lessons|default:0 }}</div>
                        <div class="text-sm text-gray-600">إجمالي الحصص</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-600">{{ stats.completed_lessons|default:0 }}</div>
                        <div class="text-sm text-gray-600">حصص مكتملة</div>
                    </div>
                </div>
            {% elif user.user_type == 'teacher' %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ stats.total_students|default:0 }}</div>
                        <div class="text-sm text-gray-600">إجمالي الطلاب</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{{ stats.active_enrollments|default:0 }}</div>
                        <div class="text-sm text-gray-600">طلاب نشطين</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">{{ stats.total_lessons|default:0 }}</div>
                        <div class="text-sm text-gray-600">إجمالي الحصص</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-600">
                            {% if stats.avg_rating_received > 0 %}
                                {{ stats.avg_rating_received|floatformat:1 }}
                            {% else %}
                                0.0
                            {% endif %}
                        </div>
                        <div class="text-sm text-gray-600">متوسط التقييم</div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Content Tabs -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 space-x-reverse px-6" aria-label="Tabs">
                {% if user.user_type == 'student' %}
                    <button class="tab-button active border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="enrollments">
                        الدورات المسجلة
                    </button>
                    <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="lessons">
                        الحصص
                    </button>
                    <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="ratings">
                        التقييمات المعطاة
                    </button>
                {% elif user.user_type == 'teacher' %}
                    <button class="tab-button active border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="students">
                        الطلاب
                    </button>
                    <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="lessons">
                        الحصص
                    </button>
                    <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="ratings">
                        التقييمات المستلمة
                    </button>
                    <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="earnings">
                        الأرباح
                    </button>
                {% endif %}
            </nav>
        </div>

        <div class="p-6">
            {% if user.user_type == 'student' %}
                <!-- Student Enrollments Tab -->
                <div id="enrollments-tab" class="tab-content">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">الدورات المسجلة</h3>
                    {% if enrollments %}
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الدورة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المعلم</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ البداية</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    {% for enrollment in enrollments %}
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ enrollment.course.title }}</div>
                                            <div class="text-sm text-gray-500">{{ enrollment.course.get_course_type_display }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ enrollment.teacher.get_full_name|default:enrollment.teacher.username }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                {% if enrollment.status == 'active' %}bg-green-100 text-green-800
                                                {% elif enrollment.status == 'completed' %}bg-blue-100 text-blue-800
                                                {% elif enrollment.status == 'pending' %}bg-yellow-100 text-yellow-800
                                                {% else %}bg-red-100 text-red-800{% endif %}">
                                                {{ enrollment.get_status_display }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ enrollment.start_date|date:"Y-m-d" }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="{% url 'admin_lessons' %}?enrollment={{ enrollment.id }}" class="text-islamic-primary hover:text-islamic-light">
                                                عرض الحصص
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-gray-500 text-center py-8">لا توجد دورات مسجلة</p>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Tab functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'border-islamic-primary', 'text-islamic-primary');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            
            tabContents.forEach(content => {
                content.style.display = 'none';
            });
            
            // Add active class to clicked button
            this.classList.add('active', 'border-islamic-primary', 'text-islamic-primary');
            this.classList.remove('border-transparent', 'text-gray-500');
            
            // Show target content
            const targetContent = document.getElementById(targetTab + '-tab');
            if (targetContent) {
                targetContent.style.display = 'block';
            }
        });
    });
    
    // Show first tab by default
    if (tabContents.length > 0) {
        tabContents[0].style.display = 'block';
    }
});
</script>
{% endblock %}
