from django.db import models
from django.contrib.auth import get_user_model
import json

User = get_user_model()

class SystemLog(models.Model):
    """نموذج لتسجيل أحداث النظام"""

    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='system_logs',
        verbose_name="المستخدم"
    )

    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name="وقت الحدث"
    )

    action = models.CharField(
        max_length=100,
        verbose_name="الإجراء"
    )

    details = models.TextField(
        verbose_name="التفاصيل"
    )

    # لتخزين بيانات إضافية بتنسيق JSON
    _data = models.TextField(
        blank=True,
        null=True,
        db_column='data',
        verbose_name="بيانات إضافية"
    )

    ip_address = models.GenericIPAddressField(
        blank=True,
        null=True,
        verbose_name="عنوان IP"
    )

    class Meta:
        verbose_name = "سجل النظام"
        verbose_name_plural = "سجلات النظام"
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.action} - {self.timestamp}"

    @property
    def data(self):
        """استرجاع البيانات المخزنة بتنسيق JSON"""
        if self._data:
            try:
                return json.loads(self._data)
            except json.JSONDecodeError:
                return {}
        return {}

    @data.setter
    def data(self, value):
        """تخزين البيانات بتنسيق JSON"""
        if value is None:
            self._data = None
        else:
            self._data = json.dumps(value, ensure_ascii=False)
