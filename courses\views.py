from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Course, Enrollment, Payment
from .utils import create_enrollment, create_payment
from lessons.models import Lesson

User = get_user_model()

# Placeholder views - will be implemented later

@login_required
def course_list(request):
    return render(request, 'courses/list.html')

@login_required
def course_create(request):
    return render(request, 'courses/create.html')

@login_required
def course_detail(request, course_id):
    return render(request, 'courses/detail.html')

@login_required
def course_edit(request, course_id):
    return render(request, 'courses/edit.html')

@login_required
def course_delete(request, course_id):
    return redirect('courses:list')

@login_required
def enrollment_list(request):
    return render(request, 'courses/enrollment_list.html')

@login_required
def enroll_student(request, course_id):
    return redirect('courses:list')

@login_required
def enrollment_detail(request, enrollment_id):
    return render(request, 'courses/enrollment_detail.html')

@login_required
def enrollment_edit(request, enrollment_id):
    return render(request, 'courses/enrollment_edit.html')

@login_required
def progress_list(request):
    return render(request, 'courses/progress_list.html')

@login_required
def add_progress(request):
    return render(request, 'courses/add_progress.html')

@login_required
def edit_progress(request, progress_id):
    return render(request, 'courses/edit_progress.html')


# وظائف جديدة للنظام المترابط

@login_required
def admin_create_enrollment(request):
    """إنشاء تسجيل جديد من قبل الأدمن"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    if request.method == 'POST':
        student_id = request.POST.get('student_id')
        course_id = request.POST.get('course_id')
        teacher_id = request.POST.get('teacher_id')
        payment_method = request.POST.get('payment_method', 'cash')

        try:
            student = User.objects.get(id=student_id, user_type='student')
            course = Course.objects.get(id=course_id, is_active=True)
            teacher = User.objects.get(id=teacher_id, user_type='teacher')

            # إنشاء التسجيل
            enrollment, message = create_enrollment(
                student=student,
                course=course,
                teacher=teacher,
                admin_user=request.user
            )

            if enrollment:
                # إنشاء الدفعة
                payment = create_payment(
                    enrollment=enrollment,
                    amount=course.price,
                    payment_method=payment_method,
                    admin_user=request.user
                )

                messages.success(request, f"تم إنشاء التسجيل والدفع بنجاح. تم إنشاء {enrollment.lessons.count()} حصة.")
                return redirect('admin_enrollments')
            else:
                messages.error(request, message)

        except User.DoesNotExist:
            messages.error(request, "المستخدم غير موجود.")
        except Course.DoesNotExist:
            messages.error(request, "الدورة غير موجودة.")
        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

    # جلب البيانات للنموذج
    students = User.objects.filter(user_type='student', is_active=True)
    teachers = User.objects.filter(user_type='teacher', is_active=True)
    courses = Course.objects.filter(is_active=True)

    context = {
        'students': students,
        'teachers': teachers,
        'courses': courses,
    }

    return render(request, 'admin/create_enrollment.html', context)


@login_required
def admin_enrollments(request):
    """عرض جميع التسجيلات"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    enrollments = Enrollment.objects.all().order_by('-created_at')

    context = {
        'enrollments': enrollments,
    }

    return render(request, 'admin/enrollments.html', context)


@login_required
def admin_create_lesson(request):
    """إنشاء حصة جديدة من قبل الأدمن"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    if request.method == 'POST':
        enrollment_id = request.POST.get('enrollment_id')
        title = request.POST.get('title')
        description = request.POST.get('description', '')
        scheduled_date = request.POST.get('scheduled_date')
        scheduled_time = request.POST.get('scheduled_time')
        duration_minutes = request.POST.get('duration_minutes', 45)

        try:
            enrollment = Enrollment.objects.get(id=enrollment_id, status='active')

            # تحويل التاريخ والوقت
            from datetime import datetime
            datetime_str = f"{scheduled_date} {scheduled_time}"
            scheduled_datetime = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")
            scheduled_datetime = timezone.make_aware(scheduled_datetime)

            # إنشاء الحصة
            lesson = Lesson.objects.create(
                enrollment=enrollment,
                title=title,
                description=description,
                scheduled_date=scheduled_datetime,
                duration_minutes=int(duration_minutes),
                created_by=request.user
            )

            # إنشاء إشعارات
            from .utils import create_lesson_notifications
            create_lesson_notifications(lesson)

            messages.success(request, "تم إنشاء الحصة بنجاح.")
            return redirect('admin_lessons')

        except Enrollment.DoesNotExist:
            messages.error(request, "التسجيل غير موجود.")
        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

    # جلب التسجيلات النشطة
    enrollments = Enrollment.objects.filter(status='active').select_related('student', 'teacher', 'course')

    context = {
        'enrollments': enrollments,
    }

    return render(request, 'admin/create_lesson.html', context)
