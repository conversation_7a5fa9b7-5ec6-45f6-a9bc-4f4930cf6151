@echo off
title نظام قرآنيا التعليمي
color 0A

echo.
echo ========================================
echo 🕌 نظام قرآنيا التعليمي
echo ========================================
echo.
echo 🚀 بدء تشغيل النظام...
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo ❌ البيئة الافتراضية غير موجودة
    echo 📋 يرجى تشغيل setup.bat أولاً لإعداد النظام
    echo.
    pause
    exit /b 1
)

REM Activate virtual environment
echo 🔧 تفعيل البيئة الافتراضية...
call venv\Scripts\activate

REM Check for migrations
echo 📊 فحص قاعدة البيانات...
python manage.py makemigrations --check --dry-run > nul 2>&1
if errorlevel 1 (
    echo 🔄 تطبيق تحديثات قاعدة البيانات...
    python manage.py makemigrations
    python manage.py migrate
)

REM Start the server
echo.
echo ✅ تم بدء النظام بنجاح!
echo.
echo 🌐 رابط النظام: http://localhost:8080
echo.
echo 📋 بيانات تسجيل الدخول:
echo ================================
echo 👤 المدير:
echo    اسم المستخدم: admin
echo    كلمة المرور: [التي أدخلتها عند الإعداد]
echo.
echo 👨‍🏫 معلم تجريبي:
echo    اسم المستخدم: teacher1
echo    كلمة المرور: teacher123
echo.
echo 👨‍🎓 طالب تجريبي:
echo    اسم المستخدم: student1
echo    كلمة المرور: student123
echo ================================
echo.
echo 🛑 لإيقاف الخادم اضغط Ctrl+C
echo 📖 للمساعدة اقرأ ملف README.md
echo.

REM Start Django server
python manage.py runserver 8080
