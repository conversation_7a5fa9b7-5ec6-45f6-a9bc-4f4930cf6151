# Generated by Django 4.2.7 on 2025-05-22 20:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('messaging', '0003_alter_conversation_unique_together_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='chatmessage',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحذف'),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='deleted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_messages', to=settings.AUTH_USER_MODEL, verbose_name='محذوف بواسطة'),
        ),
    ]
